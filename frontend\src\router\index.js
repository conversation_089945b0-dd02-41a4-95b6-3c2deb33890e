import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user.js'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue')
    },
    {
      path: '/recipes',
      name: 'recipes',
      component: () => import('../views/RecipeListView.vue')
    },
    {
      path: '/recipes/create',
      name: 'createRecipe',
      component: () => import('../views/CreateRecipeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/recipes/:id/edit',
      name: 'editRecipe',
      component: () => import('../views/EditRecipeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/recipes/:id',
      name: 'recipe-detail',
      component: () => import('../views/RecipeDetailView.vue')
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue')
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/inheritor/dashboard',
      name: 'inheritorDashboard',
      component: () => import('../views/InheritorDashboard.vue'),
      meta: { requiresAuth: true, requiresRole: 'INHERITOR' }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
  } else if (to.meta.requiresAdmin && !userStore.isAdmin) {
    next('/')
  } else if (to.meta.requiresRole && userStore.user?.role !== to.meta.requiresRole) {
    next('/')
  } else {
    next()
  }
})

export default router
