{"version": 3, "file": "encoding.js", "sources": ["../esm/encoding/defaultEncoding.js", "../esm/encoding/encodings.js", "../esm/encoding/finished.js", "../esm/helper/getArrayVal.js", "../esm/encoding/utilities.js", "../esm/helper/getGlobalScope.js", "../esm/encoding/encoding-indexes-provider.js", "../esm/encoding/indexes.js", "../esm/encoding/terminology.js", "../esm/coders/big5/Big5Decoder.js", "../esm/coders/big5/Big5Encoder.js", "../esm/coders/euc-jp/EUCJPDecoder.js", "../esm/coders/euc-jp/EUCJPEncoder.js", "../esm/coders/euc-kr/EUCKRDecoder.js", "../esm/coders/euc-kr/EUCKREncoder.js", "../esm/coders/gb18030/GB18030Decoder.js", "../esm/coders/gb18030/GB18030Encoder.js", "../esm/coders/iso-2022-jp/ISO2022JPDecoder.js", "../esm/coders/iso-2022-jp/ISO2022JPEncoder.js", "../esm/coders/shift-jis/ShiftJISDecoder.js", "../esm/coders/shift-jis/ShiftJISEncoder.js", "../esm/coders/single-byte/SingleByteDecoder.js", "../esm/coders/single-byte/SingleByteEncoder.js", "../esm/coders/utf-16/converCodeUnitToBytes.js", "../esm/coders/utf-16/UTF16Decoder.js", "../esm/coders/utf-16/UTF16Encoder.js", "../esm/coders/utf-8/UTF8Decoder.js", "../esm/coders/utf-8/UTF8Encoder.js", "../esm/coders/x-user-defined/XUserDefinedDecoder.js", "../esm/coders/x-user-defined/XUserDefinedEncoder.js", "../esm/encoding/encoding-factory.js", "../esm/common/Stream.js", "../esm/common/TextDecoder.js", "../esm/common/TextEncoder.js", "../esm/encoding.js"], "sourcesContent": ["/** @const */ export var DEFAULT_ENCODING = 'utf-8';\n//# sourceMappingURL=defaultEncoding.js.map", "/**\n * @param {boolean} fatal If true, decoding errors raise an exception.\n * @param {number=} opt_code_point Override the standard fallback code point.\n * @return {number} The code point to insert on a decoding error.\n */\nexport function decoderError(fatal, opt_code_point) {\n    if (opt_code_point === void 0) { opt_code_point = undefined; }\n    if (fatal)\n        throw TypeError(\"Decoder error\");\n    return opt_code_point || 0xfffd;\n}\n/**\n * @param {number} code_point The code point that could not be encoded.\n * @return {number} Always throws, no value is actually returned.\n */\nexport function encoderError(code_point) {\n    throw TypeError(\"The code point \" + code_point + \" could not be encoded.\");\n}\n// 5.2 Names and labels\n// TODO: Define @typedef for Encoding: {name:string,labels:Array.<string>}\n// https://github.com/google/closure-compiler/issues/247\n/**\n * @param {string} label The encoding label.\n * @return {?{name:string,labels:Array.<string>}}\n */\nexport function getEncoding(label) {\n    // 1. Remove any leading and trailing ASCII whitespace from label.\n    var keyLabel = String(label).trim().toLowerCase();\n    // 2. If label is an ASCII case-insensitive match for any of the\n    // labels listed in the table below, return the corresponding\n    // encoding, and failure otherwise.\n    if (keyLabel in label_to_encoding) {\n        return label_to_encoding[keyLabel];\n    }\n    return null;\n}\n/**\n * Encodings table: https://encoding.spec.whatwg.org/encodings.json\n * @const\n * @type {!Array.<{\n *          heading: string,\n *          encodings: Array.<{name:string,labels:Array.<string>}>\n *        }>}\n */\nvar encodings = [\n    {\n        encodings: [\n            {\n                labels: [\"unicode-1-1-utf-8\", \"utf-8\", \"utf8\"],\n                name: \"UTF-8\",\n            },\n        ],\n        heading: \"The Encoding\",\n    },\n    {\n        encodings: [\n            {\n                labels: [\"866\", \"cp866\", \"csibm866\", \"ibm866\"],\n                name: \"IBM866\",\n            },\n            {\n                labels: [\n                    \"csisolatin2\",\n                    \"iso-8859-2\",\n                    \"iso-ir-101\",\n                    \"iso8859-2\",\n                    \"iso88592\",\n                    \"iso_8859-2\",\n                    \"iso_8859-2:1987\",\n                    \"l2\",\n                    \"latin2\",\n                ],\n                name: \"ISO-8859-2\",\n            },\n            {\n                labels: [\n                    \"csisolatin3\",\n                    \"iso-8859-3\",\n                    \"iso-ir-109\",\n                    \"iso8859-3\",\n                    \"iso88593\",\n                    \"iso_8859-3\",\n                    \"iso_8859-3:1988\",\n                    \"l3\",\n                    \"latin3\",\n                ],\n                name: \"ISO-8859-3\",\n            },\n            {\n                labels: [\n                    \"csisolatin4\",\n                    \"iso-8859-4\",\n                    \"iso-ir-110\",\n                    \"iso8859-4\",\n                    \"iso88594\",\n                    \"iso_8859-4\",\n                    \"iso_8859-4:1988\",\n                    \"l4\",\n                    \"latin4\",\n                ],\n                name: \"ISO-8859-4\",\n            },\n            {\n                labels: [\n                    \"csisolatincyrillic\",\n                    \"cyrillic\",\n                    \"iso-8859-5\",\n                    \"iso-ir-144\",\n                    \"iso8859-5\",\n                    \"iso88595\",\n                    \"iso_8859-5\",\n                    \"iso_8859-5:1988\",\n                ],\n                name: \"ISO-8859-5\",\n            },\n            {\n                labels: [\n                    \"arabic\",\n                    \"asmo-708\",\n                    \"csiso88596e\",\n                    \"csiso88596i\",\n                    \"csisolatinarabic\",\n                    \"ecma-114\",\n                    \"iso-8859-6\",\n                    \"iso-8859-6-e\",\n                    \"iso-8859-6-i\",\n                    \"iso-ir-127\",\n                    \"iso8859-6\",\n                    \"iso88596\",\n                    \"iso_8859-6\",\n                    \"iso_8859-6:1987\",\n                ],\n                name: \"ISO-8859-6\",\n            },\n            {\n                labels: [\n                    \"csisolatingreek\",\n                    \"ecma-118\",\n                    \"elot_928\",\n                    \"greek\",\n                    \"greek8\",\n                    \"iso-8859-7\",\n                    \"iso-ir-126\",\n                    \"iso8859-7\",\n                    \"iso88597\",\n                    \"iso_8859-7\",\n                    \"iso_8859-7:1987\",\n                    \"sun_eu_greek\",\n                ],\n                name: \"ISO-8859-7\",\n            },\n            {\n                labels: [\n                    \"csiso88598e\",\n                    \"csisolatinhebrew\",\n                    \"hebrew\",\n                    \"iso-8859-8\",\n                    \"iso-8859-8-e\",\n                    \"iso-ir-138\",\n                    \"iso8859-8\",\n                    \"iso88598\",\n                    \"iso_8859-8\",\n                    \"iso_8859-8:1988\",\n                    \"visual\",\n                ],\n                name: \"ISO-8859-8\",\n            },\n            {\n                labels: [\"csiso88598i\", \"iso-8859-8-i\", \"logical\"],\n                name: \"ISO-8859-8-I\",\n            },\n            {\n                labels: [\n                    \"csisolatin6\",\n                    \"iso-8859-10\",\n                    \"iso-ir-157\",\n                    \"iso8859-10\",\n                    \"iso885910\",\n                    \"l6\",\n                    \"latin6\",\n                ],\n                name: \"ISO-8859-10\",\n            },\n            {\n                labels: [\"iso-8859-13\", \"iso8859-13\", \"iso885913\"],\n                name: \"ISO-8859-13\",\n            },\n            {\n                labels: [\"iso-8859-14\", \"iso8859-14\", \"iso885914\"],\n                name: \"ISO-8859-14\",\n            },\n            {\n                labels: [\n                    \"csisolatin9\",\n                    \"iso-8859-15\",\n                    \"iso8859-15\",\n                    \"iso885915\",\n                    \"iso_8859-15\",\n                    \"l9\",\n                ],\n                name: \"ISO-8859-15\",\n            },\n            {\n                labels: [\"iso-8859-16\"],\n                name: \"ISO-8859-16\",\n            },\n            {\n                labels: [\"cskoi8r\", \"koi\", \"koi8\", \"koi8-r\", \"koi8_r\"],\n                name: \"KOI8-R\",\n            },\n            {\n                labels: [\"koi8-ru\", \"koi8-u\"],\n                name: \"KOI8-U\",\n            },\n            {\n                labels: [\"csmacintosh\", \"mac\", \"macintosh\", \"x-mac-roman\"],\n                name: \"macintosh\",\n            },\n            {\n                labels: [\n                    \"dos-874\",\n                    \"iso-8859-11\",\n                    \"iso8859-11\",\n                    \"iso885911\",\n                    \"tis-620\",\n                    \"windows-874\",\n                ],\n                name: \"windows-874\",\n            },\n            {\n                labels: [\"cp1250\", \"windows-1250\", \"x-cp1250\"],\n                name: \"windows-1250\",\n            },\n            {\n                labels: [\"cp1251\", \"windows-1251\", \"x-cp1251\"],\n                name: \"windows-1251\",\n            },\n            {\n                labels: [\n                    \"ansi_x3.4-1968\",\n                    \"cp1252\",\n                    \"cp819\",\n                    \"ibm819\",\n                    \"iso-ir-100\",\n                    \"windows-1252\",\n                    \"x-cp1252\",\n                ],\n                name: \"windows-1252\",\n            },\n            {\n                labels: [\n                    \"ascii\",\n                    \"us-ascii\",\n                    \"iso-8859-1\",\n                    \"iso8859-1\",\n                    \"iso88591\",\n                    \"iso_8859-1\",\n                    \"iso_8859-1:1987\",\n                    \"l1\",\n                    \"latin1\",\n                    \"csisolatin1\",\n                ],\n                name: \"iso-8859-1\",\n            },\n            {\n                labels: [\"cp1253\", \"windows-1253\", \"x-cp1253\"],\n                name: \"windows-1253\",\n            },\n            {\n                labels: [\n                    \"cp1254\",\n                    \"csisolatin5\",\n                    \"iso-8859-9\",\n                    \"iso-ir-148\",\n                    \"iso8859-9\",\n                    \"iso88599\",\n                    \"iso_8859-9\",\n                    \"iso_8859-9:1989\",\n                    \"l5\",\n                    \"latin5\",\n                    \"windows-1254\",\n                    \"x-cp1254\",\n                ],\n                name: \"windows-1254\",\n            },\n            {\n                labels: [\"cp1255\", \"windows-1255\", \"x-cp1255\"],\n                name: \"windows-1255\",\n            },\n            {\n                labels: [\"cp1256\", \"windows-1256\", \"x-cp1256\"],\n                name: \"windows-1256\",\n            },\n            {\n                labels: [\"cp1257\", \"windows-1257\", \"x-cp1257\"],\n                name: \"windows-1257\",\n            },\n            {\n                labels: [\"cp1258\", \"windows-1258\", \"x-cp1258\"],\n                name: \"windows-1258\",\n            },\n            {\n                labels: [\"x-mac-cyrillic\", \"x-mac-ukrainian\"],\n                name: \"x-mac-cyrillic\",\n            },\n        ],\n        heading: \"Legacy single-byte encodings\",\n    },\n    {\n        encodings: [\n            {\n                labels: [\n                    \"chinese\",\n                    \"csgb2312\",\n                    \"csiso58gb231280\",\n                    \"gb2312\",\n                    \"gb_2312\",\n                    \"gb_2312-80\",\n                    \"gbk\",\n                    \"iso-ir-58\",\n                    \"x-gbk\",\n                ],\n                name: \"GBK\",\n            },\n            {\n                labels: [\"gb18030\"],\n                name: \"gb18030\",\n            },\n        ],\n        heading: \"Legacy multi-byte Chinese (simplified) encodings\",\n    },\n    {\n        encodings: [\n            {\n                labels: [\"big5\", \"big5-hkscs\", \"cn-big5\", \"csbig5\", \"x-x-big5\"],\n                name: \"Big5\",\n            },\n        ],\n        heading: \"Legacy multi-byte Chinese (traditional) encodings\",\n    },\n    {\n        encodings: [\n            {\n                labels: [\"cseucpkdfmtjapanese\", \"euc-jp\", \"x-euc-jp\"],\n                name: \"EUC-JP\",\n            },\n            {\n                labels: [\"csiso2022jp\", \"iso-2022-jp\"],\n                name: \"ISO-2022-JP\",\n            },\n            {\n                labels: [\n                    \"csshiftjis\",\n                    \"ms932\",\n                    \"ms_kanji\",\n                    \"shift-jis\",\n                    \"shift_jis\",\n                    \"sjis\",\n                    \"windows-31j\",\n                    \"x-sjis\",\n                ],\n                name: \"Shift_JIS\",\n            },\n        ],\n        heading: \"Legacy multi-byte Japanese encodings\",\n    },\n    {\n        encodings: [\n            {\n                labels: [\n                    \"cseuckr\",\n                    \"csksc56011987\",\n                    \"euc-kr\",\n                    \"iso-ir-149\",\n                    \"korean\",\n                    \"ks_c_5601-1987\",\n                    \"ks_c_5601-1989\",\n                    \"ksc5601\",\n                    \"ksc_5601\",\n                    \"windows-949\",\n                ],\n                name: \"EUC-KR\",\n            },\n        ],\n        heading: \"Legacy multi-byte Korean encodings\",\n    },\n    {\n        encodings: [\n            {\n                labels: [\n                    \"csiso2022kr\",\n                    \"hz-gb-2312\",\n                    \"iso-2022-cn\",\n                    \"iso-2022-cn-ext\",\n                    \"iso-2022-kr\",\n                ],\n                name: \"replacement\",\n            },\n            {\n                labels: [\"utf-16be\"],\n                name: \"UTF-16BE\",\n            },\n            {\n                labels: [\"utf-16\", \"utf-16le\"],\n                name: \"UTF-16LE\",\n            },\n            {\n                labels: [\"x-user-defined\"],\n                name: \"x-user-defined\",\n            },\n        ],\n        heading: \"Legacy miscellaneous encodings\",\n    },\n];\n// Label to encoding registry.\n/** @type {Object.<string,{name:string,labels:Array.<string>}>} */\nvar label_to_encoding = {};\nencodings.forEach(function (category) {\n    category.encodings.forEach(function (encoding) {\n        encoding.labels.forEach(function (label) {\n            label_to_encoding[label] = encoding;\n        });\n    });\n});\nexport { encodings };\n//# sourceMappingURL=encodings.js.map", "// 5.1 Encoders and decoders\n/** @const */\nexport var finished = -1;\n//# sourceMappingURL=finished.js.map", "export function getArrayVal(idxVal) {\n    return Array.isArray(idxVal) ? idxVal : [idxVal];\n}\n//# sourceMappingURL=getArrayVal.js.map", "/**\n * @param {number} a The number to test.\n * @param {number} min The minimum value in the range, inclusive.\n * @param {number} max The maximum value in the range, inclusive.\n * @return {boolean} True if a >= min and a <= max.\n */\nexport function inRange(a, min, max) {\n    return min <= a && a <= max;\n}\n/**\n * @param {!Array.<*>} array The array to check.\n * @param {*} item The item to look for in the array.\n * @return {boolean} True if the item appears in the array.\n */\nexport function includes(array, item) {\n    return array.indexOf(item) !== -1;\n}\n/**\n * @param {*} o\n * @return {Object}\n */\nexport function ToDictionary(o) {\n    if (o === undefined || o === null)\n        return {};\n    if (o === Object(o))\n        return o;\n    throw TypeError('Could not convert argument to dictionary');\n}\n/**\n * @param {string} string Input string of UTF-16 code units.\n * @return {!Array.<number>} Code points.\n */\nexport function stringToCodePoints(string) {\n    // https://heycam.github.io/webidl/#dfn-obtain-unicode\n    // 1. Let S be the DOMString value.\n    var s = String(string);\n    // 2. Let n be the length of S.\n    var n = s.length;\n    // 3. Initialize i to 0.\n    var i = 0;\n    // 4. Initialize U to be an empty sequence of Unicode characters.\n    var u = [];\n    // 5. While i < n:\n    while (i < n) {\n        // 1. Let c be the code unit in S at index i.\n        var c = s.charCodeAt(i);\n        // 2. Depending on the value of c:\n        // c < 0xD800 or c > 0xDFFF\n        if (c < 0xD800 || c > 0xDFFF) {\n            // Append to U the Unicode character with code point c.\n            u.push(c);\n        }\n        // 0xDC00 ≤ c ≤ 0xDFFF\n        else if (0xDC00 <= c && c <= 0xDFFF) {\n            // Append to U a U+FFFD REPLACEMENT CHARACTER.\n            u.push(0xFFFD);\n        }\n        // 0xD800 ≤ c ≤ 0xDBFF\n        else if (0xD800 <= c && c <= 0xDBFF) {\n            // 1. If i = n−1, then append to U a U+FFFD REPLACEMENT\n            // CHARACTER.\n            if (i === n - 1) {\n                u.push(0xFFFD);\n            }\n            // 2. Otherwise, i < n−1:\n            else {\n                // 1. Let d be the code unit in S at index i+1.\n                var d = s.charCodeAt(i + 1);\n                // 2. If 0xDC00 ≤ d ≤ 0xDFFF, then:\n                if (0xDC00 <= d && d <= 0xDFFF) {\n                    // 1. Let a be c & 0x3FF.\n                    var a = c & 0x3FF;\n                    // 2. Let b be d & 0x3FF.\n                    var b = d & 0x3FF;\n                    // 3. Append to U the Unicode character with code point\n                    // 2^16+2^10*a+b.\n                    u.push(0x10000 + (a << 10) + b);\n                    // 4. Set i to i+1.\n                    i += 1;\n                }\n                // 3. Otherwise, d < 0xDC00 or d > 0xDFFF. Append to U a\n                // U+FFFD REPLACEMENT CHARACTER.\n                else {\n                    u.push(0xFFFD);\n                }\n            }\n        }\n        // 3. Set i to i+1.\n        i += 1;\n    }\n    // 6. Return U.\n    return u;\n}\n/**\n * @param {!Array.<number>} code_points Array of code points.\n * @return {string} string String of UTF-16 code units.\n */\nexport function codePointsToString(code_points) {\n    var s = '';\n    for (var i = 0; i < code_points.length; ++i) {\n        var cp = code_points[i];\n        if (cp <= 0xFFFF) {\n            s += String.fromCharCode(cp);\n        }\n        else {\n            cp -= 0x10000;\n            s += String.fromCharCode((cp >> 10) + 0xD800, (cp & 0x3FF) + 0xDC00);\n        }\n    }\n    return s;\n}\n//# sourceMappingURL=utilities.js.map", "export function getGlobalScope() {\n    if (typeof global !== 'undefined')\n        return global;\n    if (typeof window !== 'undefined')\n        return window;\n    if (typeof self !== 'undefined')\n        return self;\n    return;\n}\n//# sourceMappingURL=getGlobalScope.js.map", "import { getGlobalScope } from \"../helper/getGlobalScope\";\nvar _encodingIndexes;\nfunction checkForEncodingIndexes() {\n    if (typeof TextEncodingIndexes !== 'undefined')\n        return TextEncodingIndexes.encodingIndexes;\n    var glo = getGlobalScope();\n    if (!glo)\n        return null;\n    if ('TextEncodingIndexes' in glo)\n        return global['TextEncodingIndexes']['encodingIndexes'];\n    if ('encoding-indexes' in glo)\n        return global['encodingIndexes'];\n    return null;\n}\nexport function getEncodingIndexes() {\n    if (_encodingIndexes) {\n        return _encodingIndexes;\n    }\n    var indexes = checkForEncodingIndexes();\n    if (!indexes) {\n        return null;\n    }\n    _encodingIndexes = indexes;\n    return indexes;\n}\n//# sourceMappingURL=encoding-indexes-provider.js.map", "import { getArrayVal } from \"../helper/getArrayVal\";\nimport { inRange } from \"./utilities\";\nimport { getEncodingIndexes } from \"./encoding-indexes-provider\";\n/**\n * @param {number} pointer The |pointer| to search for.\n * @param {(!Array.<?number>|undefined)} index The |index| to search within.\n * @return {?number} The code point corresponding to |pointer| in |index|,\n *     or null if |code point| is not in |index|.\n */\nexport function indexCodePointFor(pointer, index) {\n    if (!index)\n        return null;\n    return index[pointer] || null;\n}\n/**\n * @param {number} code_point The |code point| to search for.\n * @param {!Array.<?number>} index The |index| to search within.\n * @return {?number} The first pointer corresponding to |code point| in\n *     |index|, or null if |code point| is not in |index|.\n */\nexport function indexPointerFor(code_point, index) {\n    var pointer = index.indexOf(code_point);\n    return pointer === -1 ? null : pointer;\n}\n/**\n * @param {string} name Name of the index.\n * @return {(!Array.<number>|!Array.<Array.<number>>)}\n *  */\nexport function index(name) {\n    var encodingIndexes = getEncodingIndexes();\n    if (!encodingIndexes) {\n        throw Error(\"Indexes missing.\" +\n            \" Did you forget to include encoding-indexes.js first?\");\n    }\n    return encodingIndexes[name];\n}\n/**\n * @param {number} pointer The |pointer| to search for in the gb18030 index.\n * @return {?number} The code point corresponding to |pointer| in |index|,\n *     or null if |code point| is not in the gb18030 index.\n */\nexport function indexGB18030RangesCodePointFor(pointer) {\n    // 1. If pointer is greater than 39419 and less than 189000, or\n    // pointer is greater than 1237575, return null.\n    if ((pointer > 39419 && pointer < 189000) || (pointer > 1237575))\n        return null;\n    // 2. If pointer is 7457, return code point U+E7C7.\n    if (pointer === 7457)\n        return 0xE7C7;\n    // 3. Let offset be the last pointer in index gb18030 ranges that\n    // is equal to or less than pointer and let code point offset be\n    // its corresponding code point.\n    var offset = 0;\n    var code_point_offset = 0;\n    var idx = index('gb18030-ranges');\n    for (var i = 0; i < idx.length; ++i) {\n        /** @type {!Array.<number>} */\n        var entry = getArrayVal(idx[i]);\n        if (entry[0] <= pointer) {\n            offset = entry[0];\n            code_point_offset = entry[1];\n        }\n        else {\n            break;\n        }\n    }\n    // 4. Return a code point whose value is code point offset +\n    // pointer − offset.\n    return code_point_offset + pointer - offset;\n}\n/**\n * @param {number} code_point The |code point| to locate in the gb18030 index.\n * @return {number} The first pointer corresponding to |code point| in the\n *     gb18030 index.\n */\nexport function indexGB18030RangesPointerFor(code_point) {\n    // 1. If code point is U+E7C7, return pointer 7457.\n    if (code_point === 0xE7C7)\n        return 7457;\n    // 2. Let offset be the last code point in index gb18030 ranges\n    // that is equal to or less than code point and let pointer offset\n    // be its corresponding pointer.\n    var offset = 0;\n    var pointer_offset = 0;\n    var idx = index('gb18030-ranges');\n    for (var i = 0; i < idx.length; ++i) {\n        var idxVal = idx[i];\n        /** @type {!Array.<number>} */\n        var entry = getArrayVal(idxVal);\n        if (entry[1] <= code_point) {\n            offset = entry[1];\n            pointer_offset = entry[0];\n        }\n        else {\n            break;\n        }\n    }\n    // 3. Return a pointer whose value is pointer offset + code point\n    // − offset.\n    return pointer_offset + code_point - offset;\n}\n/**\n * @param {number} code_point The |code_point| to search for in the Shift_JIS\n *     index.\n * @return {?number} The code point corresponding to |pointer| in |index|,\n *     or null if |code point| is not in the Shift_JIS index.\n */\nexport function indexShiftJISPointerFor(code_point) {\n    // 1. Let index be index jis0208 excluding all entries whose\n    // pointer is in the range 8272 to 8835, inclusive.\n    shift_jis_index = shift_jis_index ||\n        index('jis0208').map(function (code_point, pointer) {\n            return inRange(pointer, 8272, 8835) ? null : code_point;\n        });\n    var index_ = shift_jis_index;\n    // 2. Return the index pointer for code point in index.\n    return index_.indexOf(code_point);\n}\nvar shift_jis_index;\n/**\n * @param {number} code_point The |code_point| to search for in the big5\n *     index.\n * @return {?number} The code point corresponding to |pointer| in |index|,\n *     or null if |code point| is not in the big5 index.\n */\nexport function indexBig5PointerFor(code_point) {\n    // 1. Let index be index Big5 excluding all entries whose pointer\n    big5_index_no_hkscs = big5_index_no_hkscs ||\n        index('big5').map(function (code_point, pointer) {\n            return (pointer < (0xA1 - 0x81) * 157) ? null : code_point;\n        });\n    var index_ = big5_index_no_hkscs;\n    // 2. If code point is U+2550, U+255E, U+2561, U+256A, U+5341, or\n    // U+5345, return the last pointer corresponding to code point in\n    // index.\n    if (code_point === 0x2550 || code_point === 0x255E ||\n        code_point === 0x2561 || code_point === 0x256A ||\n        code_point === 0x5341 || code_point === 0x5345) {\n        return index_.lastIndexOf(code_point);\n    }\n    // 3. Return the index pointer for code point in index.\n    return indexPointerFor(code_point, index_);\n}\nvar big5_index_no_hkscs;\n//# sourceMappingURL=indexes.js.map", "//\n// Implementation of Encoding specification\n// https://encoding.spec.whatwg.org/\n//\n//\n// 4. Terminology\n//\n/**\n * An ASCII byte is a byte in the range 0x00 to 0x7F, inclusive.\n * @param {number} a The number to test.\n * @return {boolean} True if a is in the range 0x00 to 0x7F, inclusive.\n */\nexport function isASCIIByte(a) {\n    return 0x00 <= a && a <= 0x7F;\n}\n/**\n * An ASCII code point is a code point in the range U+0000 to\n * U+007F, inclusive.\n */\nexport var isASCIICodePoint = isASCIIByte;\n/**\n * End-of-stream is a special token that signifies no more tokens\n * are in the stream.\n * @const\n */ export var end_of_stream = -1;\n//# sourceMappingURL=terminology.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexCodePointFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIIByte } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n  * @constructor\n  * @implements {Decoder}\n  * @param {{fatal: boolean}} options\n  */\nvar Big5Decoder = /** @class */ (function () {\n    function Big5Decoder(options) {\n        this.fatal = options.fatal;\n        // Big5's decoder has an associated Big5 lead (initially 0x00).\n        /** @type {number} */ this.Big5_lead = 0x00;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    Big5Decoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream and Big5 lead is not 0x00, set\n        // Big5 lead to 0x00 and return error.\n        if (bite === end_of_stream && this.Big5_lead !== 0x00) {\n            this.Big5_lead = 0x00;\n            return decoderError(this.fatal);\n        }\n        // 2. If byte is end-of-stream and Big5 lead is 0x00, return\n        // finished.\n        if (bite === end_of_stream && this.Big5_lead === 0x00)\n            return finished;\n        // 3. If Big5 lead is not 0x00, let lead be Big5 lead, let\n        // pointer be null, set Big5 lead to 0x00, and then run these\n        // substeps:\n        if (this.Big5_lead !== 0x00) {\n            var lead = this.Big5_lead;\n            var pointer = null;\n            this.Big5_lead = 0x00;\n            // 1. Let offset be 0x40 if byte is less than 0x7F and 0x62\n            // otherwise.\n            var offset = bite < 0x7F ? 0x40 : 0x62;\n            // 2. If byte is in the range 0x40 to 0x7E, inclusive, or 0xA1\n            // to 0xFE, inclusive, set pointer to (lead − 0x81) × 157 +\n            // (byte − offset).\n            if (inRange(bite, 0x40, 0x7E) || inRange(bite, 0xA1, 0xFE))\n                pointer = (lead - 0x81) * 157 + (bite - offset);\n            // 3. If there is a row in the table below whose first column\n            // is pointer, return the two code points listed in its second\n            // column\n            // Pointer | Code points\n            // --------+--------------\n            // 1133    | U+00CA U+0304\n            // 1135    | U+00CA U+030C\n            // 1164    | U+00EA U+0304\n            // 1166    | U+00EA U+030C\n            switch (pointer) {\n                case 1133: return [0x00CA, 0x0304];\n                case 1135: return [0x00CA, 0x030C];\n                case 1164: return [0x00EA, 0x0304];\n                case 1166: return [0x00EA, 0x030C];\n            }\n            // 4. Let code point be null if pointer is null and the index\n            // code point for pointer in index Big5 otherwise.\n            var code_point = (pointer === null) ? null :\n                indexCodePointFor(pointer, index('big5'));\n            // 5. If code point is null and byte is an ASCII byte, prepend\n            // byte to stream.\n            if (code_point === null && isASCIIByte(bite))\n                stream.prepend(bite);\n            // 6. If code point is null, return error.\n            if (code_point === null)\n                return decoderError(this.fatal);\n            // 7. Return a code point whose value is code point.\n            return code_point;\n        }\n        // 4. If byte is an ASCII byte, return a code point whose value\n        // is byte.\n        if (isASCIIByte(bite))\n            return bite;\n        // 5. If byte is in the range 0x81 to 0xFE, inclusive, set Big5\n        // lead to byte and return continue.\n        if (inRange(bite, 0x81, 0xFE)) {\n            this.Big5_lead = bite;\n            return null;\n        }\n        // 6. Return error.\n        return decoderError(this.fatal);\n    };\n    return Big5Decoder;\n}());\nexport { Big5Decoder };\n//# sourceMappingURL=Big5Decoder.js.map", "import { encoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { indexBig5PointerFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\n/**\n   * @constructor\n   * @implements {Encoder}\n   * @param {{fatal: boolean}} options\n   */\nvar Big5Encoder = /** @class */ (function () {\n    function Big5Encoder(options) {\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    Big5Encoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is an ASCII code point, return a byte whose\n        // value is code point.\n        if (isASCIICodePoint(code_point))\n            return code_point;\n        // 3. Let pointer be the index Big5 pointer for code point.\n        var pointer = indexBig5PointerFor(code_point);\n        // 4. If pointer is null, return error with code point.\n        if (pointer === null)\n            return encoderError(code_point);\n        // 5. Let lead be Math.floor(pointer / 157) + 0x81.\n        var lead = Math.floor(pointer / 157) + 0x81;\n        // 6. If lead is less than 0xA1, return error with code point.\n        if (lead < 0xA1)\n            return encoderError(code_point);\n        // 7. Let trail be pointer % 157.\n        var trail = pointer % 157;\n        // 8. Let offset be 0x40 if trail is less than 0x3F and 0x62\n        // otherwise.\n        var offset = trail < 0x3F ? 0x40 : 0x62;\n        // Return two bytes whose values are lead and trail + offset.\n        return [lead, trail + offset];\n    };\n    return Big5Encoder;\n}());\nexport { Big5Encoder };\n//# sourceMappingURL=Big5Encoder.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexCodePointFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIIByte } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */\nvar EUCJPDecoder = /** @class */ (function () {\n    function EUCJPDecoder(options) {\n        this.fatal = options.fatal;\n        // euc-jp's decoder has an associated euc-jp jis0212 flag\n        // (initially unset) and euc-jp lead (initially 0x00).\n        /** @type {boolean} */ this.eucjp_jis0212_flag = false,\n            /** @type {number} */ this.eucjp_lead = 0x00;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    EUCJPDecoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream and euc-jp lead is not 0x00, set\n        // euc-jp lead to 0x00, and return error.\n        if (bite === end_of_stream && this.eucjp_lead !== 0x00) {\n            this.eucjp_lead = 0x00;\n            return decoderError(this.fatal);\n        }\n        // 2. If byte is end-of-stream and euc-jp lead is 0x00, return\n        // finished.\n        if (bite === end_of_stream && this.eucjp_lead === 0x00)\n            return finished;\n        // 3. If euc-jp lead is 0x8E and byte is in the range 0xA1 to\n        // 0xDF, inclusive, set euc-jp lead to 0x00 and return a code\n        // point whose value is 0xFF61 − 0xA1 + byte.\n        if (this.eucjp_lead === 0x8E && inRange(bite, 0xA1, 0xDF)) {\n            this.eucjp_lead = 0x00;\n            return 0xFF61 - 0xA1 + bite;\n        }\n        // 4. If euc-jp lead is 0x8F and byte is in the range 0xA1 to\n        // 0xFE, inclusive, set the euc-jp jis0212 flag, set euc-jp lead\n        // to byte, and return continue.\n        if (this.eucjp_lead === 0x8F && inRange(bite, 0xA1, 0xFE)) {\n            this.eucjp_jis0212_flag = true;\n            this.eucjp_lead = bite;\n            return null;\n        }\n        // 5. If euc-jp lead is not 0x00, let lead be euc-jp lead, set\n        // euc-jp lead to 0x00, and run these substeps:\n        if (this.eucjp_lead !== 0x00) {\n            var lead = this.eucjp_lead;\n            this.eucjp_lead = 0x00;\n            // 1. Let code point be null.\n            var code_point = null;\n            // 2. If lead and byte are both in the range 0xA1 to 0xFE,\n            // inclusive, set code point to the index code point for (lead\n            // − 0xA1) × 94 + byte − 0xA1 in index jis0208 if the euc-jp\n            // jis0212 flag is unset and in index jis0212 otherwise.\n            if (inRange(lead, 0xA1, 0xFE) && inRange(bite, 0xA1, 0xFE)) {\n                code_point = indexCodePointFor((lead - 0xA1) * 94 + (bite - 0xA1), index(!this.eucjp_jis0212_flag ? 'jis0208' : 'jis0212'));\n            }\n            // 3. Unset the euc-jp jis0212 flag.\n            this.eucjp_jis0212_flag = false;\n            // 4. If byte is not in the range 0xA1 to 0xFE, inclusive,\n            // prepend byte to stream.\n            if (!inRange(bite, 0xA1, 0xFE))\n                stream.prepend(bite);\n            // 5. If code point is null, return error.\n            if (code_point === null)\n                return decoderError(this.fatal);\n            // 6. Return a code point whose value is code point.\n            return code_point;\n        }\n        // 6. If byte is an ASCII byte, return a code point whose value\n        // is byte.\n        if (isASCIIByte(bite))\n            return bite;\n        // 7. If byte is 0x8E, 0x8F, or in the range 0xA1 to 0xFE,\n        // inclusive, set euc-jp lead to byte and return continue.\n        if (bite === 0x8E || bite === 0x8F || inRange(bite, 0xA1, 0xFE)) {\n            this.eucjp_lead = bite;\n            return null;\n        }\n        // 8. Return error.\n        return decoderError(this.fatal);\n    };\n    return EUCJPDecoder;\n}());\nexport { EUCJPDecoder };\n//# sourceMappingURL=EUCJPDecoder.js.map", "import { encoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexPointerFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */\nvar EUCJPEncoder = /** @class */ (function () {\n    function EUCJPEncoder(options) {\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    EUCJPEncoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is an ASCII code point, return a byte whose\n        // value is code point.\n        if (isASCIICodePoint(code_point))\n            return code_point;\n        // 3. If code point is U+00A5, return byte 0x5C.\n        if (code_point === 0x00A5)\n            return 0x5C;\n        // 4. If code point is U+203E, return byte 0x7E.\n        if (code_point === 0x203E)\n            return 0x7E;\n        // 5. If code point is in the range U+FF61 to U+FF9F, inclusive,\n        // return two bytes whose values are 0x8E and code point −\n        // 0xFF61 + 0xA1.\n        if (inRange(code_point, 0xFF61, 0xFF9F))\n            return [0x8E, code_point - 0xFF61 + 0xA1];\n        // 6. If code point is U+2212, set it to U+FF0D.\n        if (code_point === 0x2212)\n            code_point = 0xFF0D;\n        // 7. Let pointer be the index pointer for code point in index\n        // jis0208.\n        var pointer = indexPointerFor(code_point, index('jis0208'));\n        // 8. If pointer is null, return error with code point.\n        if (pointer === null)\n            return encoderError(code_point);\n        // 9. Let lead be Math.floor(pointer / 94) + 0xA1.\n        var lead = Math.floor(pointer / 94) + 0xA1;\n        // 10. Let trail be pointer % 94 + 0xA1.\n        var trail = pointer % 94 + 0xA1;\n        // 11. Return two bytes whose values are lead and trail.\n        return [lead, trail];\n    };\n    return EUCJPEncoder;\n}());\nexport { EUCJPEncoder };\n//# sourceMappingURL=EUCJPEncoder.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexCodePointFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIIByte } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */\nvar EUCKRDecoder = /** @class */ (function () {\n    function EUCKRDecoder(options) {\n        this.fatal = options.fatal;\n        // euc-kr's decoder has an associated euc-kr lead (initially 0x00).\n        /** @type {number} */ this.euckr_lead = 0x00;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    EUCKRDecoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream and euc-kr lead is not 0x00, set\n        // euc-kr lead to 0x00 and return error.\n        if (bite === end_of_stream && this.euckr_lead !== 0) {\n            this.euckr_lead = 0x00;\n            return decoderError(this.fatal);\n        }\n        // 2. If byte is end-of-stream and euc-kr lead is 0x00, return\n        // finished.\n        if (bite === end_of_stream && this.euckr_lead === 0)\n            return finished;\n        // 3. If euc-kr lead is not 0x00, let lead be euc-kr lead, let\n        // pointer be null, set euc-kr lead to 0x00, and then run these\n        // substeps:\n        if (this.euckr_lead !== 0x00) {\n            var lead = this.euckr_lead;\n            var pointer = null;\n            this.euckr_lead = 0x00;\n            // 1. If byte is in the range 0x41 to 0xFE, inclusive, set\n            // pointer to (lead − 0x81) × 190 + (byte − 0x41).\n            if (inRange(bite, 0x41, 0xFE))\n                pointer = (lead - 0x81) * 190 + (bite - 0x41);\n            // 2. Let code point be null, if pointer is null, and the\n            // index code point for pointer in index euc-kr otherwise.\n            var code_point = (pointer === null)\n                ? null : indexCodePointFor(pointer, index('euc-kr'));\n            // 3. If code point is null and byte is an ASCII byte, prepend\n            // byte to stream.\n            if (pointer === null && isASCIIByte(bite))\n                stream.prepend(bite);\n            // 4. If code point is null, return error.\n            if (code_point === null)\n                return decoderError(this.fatal);\n            // 5. Return a code point whose value is code point.\n            return code_point;\n        }\n        // 4. If byte is an ASCII byte, return a code point whose value\n        // is byte.\n        if (isASCIIByte(bite))\n            return bite;\n        // 5. If byte is in the range 0x81 to 0xFE, inclusive, set\n        // euc-kr lead to byte and return continue.\n        if (inRange(bite, 0x81, 0xFE)) {\n            this.euckr_lead = bite;\n            return null;\n        }\n        // 6. Return error.\n        return decoderError(this.fatal);\n    };\n    return EUCKRDecoder;\n}());\nexport { EUCKRDecoder };\n//# sourceMappingURL=EUCKRDecoder.js.map", "import { encoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexPointerFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */\nvar EUCKREncoder = /** @class */ (function () {\n    function EUCKREncoder(options) {\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    EUCKREncoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is an ASCII code point, return a byte whose\n        // value is code point.\n        if (isASCIICodePoint(code_point))\n            return code_point;\n        // 3. Let pointer be the index pointer for code point in index\n        // euc-kr.\n        var pointer = indexPointerFor(code_point, index('euc-kr'));\n        // 4. If pointer is null, return error with code point.\n        if (pointer === null)\n            return encoderError(code_point);\n        // 5. Let lead be Math.floor(pointer / 190) + 0x81.\n        var lead = Math.floor(pointer / 190) + 0x81;\n        // 6. Let trail be pointer % 190 + 0x41.\n        var trail = (pointer % 190) + 0x41;\n        // 7. Return two bytes whose values are lead and trail.\n        return [lead, trail];\n    };\n    return EUCKREncoder;\n}());\nexport { EUCKREncoder };\n//# sourceMappingURL=EUCKREncoder.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexCodePointFor, indexGB18030RangesCodePointFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIIByte } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */\nvar GB18030Decoder = /** @class */ (function () {\n    function GB18030Decoder(options) {\n        this.fatal = options.fatal;\n        // gb18030's decoder has an associated gb18030 first, gb18030\n        // second, and gb18030 third (all initially 0x00).\n        /** @type {number} */ this.gb18030_first = 0x00,\n            /** @type {number} */ this.gb18030_second = 0x00,\n            /** @type {number} */ this.gb18030_third = 0x00;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    GB18030Decoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream and gb18030 first, gb18030\n        // second, and gb18030 third are 0x00, return finished.\n        if (bite === end_of_stream && this.gb18030_first === 0x00 &&\n            this.gb18030_second === 0x00 && this.gb18030_third === 0x00) {\n            return finished;\n        }\n        // 2. If byte is end-of-stream, and gb18030 first, gb18030\n        // second, or gb18030 third is not 0x00, set gb18030 first,\n        // gb18030 second, and gb18030 third to 0x00, and return error.\n        if (bite === end_of_stream &&\n            (this.gb18030_first !== 0x00 || this.gb18030_second !== 0x00 ||\n                this.gb18030_third !== 0x00)) {\n            this.gb18030_first = 0x00;\n            this.gb18030_second = 0x00;\n            this.gb18030_third = 0x00;\n            decoderError(this.fatal);\n        }\n        var code_point;\n        // 3. If gb18030 third is not 0x00, run these substeps:\n        if (this.gb18030_third !== 0x00) {\n            // 1. Let code point be null.\n            code_point = null;\n            // 2. If byte is in the range 0x30 to 0x39, inclusive, set\n            // code point to the index gb18030 ranges code point for\n            // (((gb18030 first − 0x81) × 10 + gb18030 second − 0x30) ×\n            // 126 + gb18030 third − 0x81) × 10 + byte − 0x30.\n            if (inRange(bite, 0x30, 0x39)) {\n                code_point = indexGB18030RangesCodePointFor((((this.gb18030_first - 0x81) * 10 + this.gb18030_second - 0x30) * 126 +\n                    this.gb18030_third - 0x81) * 10 + bite - 0x30);\n            }\n            // 3. Let buffer be a byte sequence consisting of gb18030\n            // second, gb18030 third, and byte, in order.\n            var buffer = [this.gb18030_second, this.gb18030_third, bite];\n            // 4. Set gb18030 first, gb18030 second, and gb18030 third to\n            // 0x00.\n            this.gb18030_first = 0x00;\n            this.gb18030_second = 0x00;\n            this.gb18030_third = 0x00;\n            // 5. If code point is null, prepend buffer to stream and\n            // return error.\n            if (code_point === null) {\n                stream.prepend(buffer);\n                return decoderError(this.fatal);\n            }\n            // 6. Return a code point whose value is code point.\n            return code_point;\n        }\n        // 4. If gb18030 second is not 0x00, run these substeps:\n        if (this.gb18030_second !== 0x00) {\n            // 1. If byte is in the range 0x81 to 0xFE, inclusive, set\n            // gb18030 third to byte and return continue.\n            if (inRange(bite, 0x81, 0xFE)) {\n                this.gb18030_third = bite;\n                return null;\n            }\n            // 2. Prepend gb18030 second followed by byte to stream, set\n            // gb18030 first and gb18030 second to 0x00, and return error.\n            stream.prepend([this.gb18030_second, bite]);\n            this.gb18030_first = 0x00;\n            this.gb18030_second = 0x00;\n            return decoderError(this.fatal);\n        }\n        // 5. If gb18030 first is not 0x00, run these substeps:\n        if (this.gb18030_first !== 0x00) {\n            // 1. If byte is in the range 0x30 to 0x39, inclusive, set\n            // gb18030 second to byte and return continue.\n            if (inRange(bite, 0x30, 0x39)) {\n                this.gb18030_second = bite;\n                return null;\n            }\n            // 2. Let lead be gb18030 first, let pointer be null, and set\n            // gb18030 first to 0x00.\n            var lead = this.gb18030_first;\n            var pointer = null;\n            this.gb18030_first = 0x00;\n            // 3. Let offset be 0x40 if byte is less than 0x7F and 0x41\n            // otherwise.\n            var offset = bite < 0x7F ? 0x40 : 0x41;\n            // 4. If byte is in the range 0x40 to 0x7E, inclusive, or 0x80\n            // to 0xFE, inclusive, set pointer to (lead − 0x81) × 190 +\n            // (byte − offset).\n            if (inRange(bite, 0x40, 0x7E) || inRange(bite, 0x80, 0xFE))\n                pointer = (lead - 0x81) * 190 + (bite - offset);\n            // 5. Let code point be null if pointer is null and the index\n            // code point for pointer in index gb18030 otherwise.\n            code_point = pointer === null ? null :\n                indexCodePointFor(pointer, index('gb18030'));\n            // 6. If code point is null and byte is an ASCII byte, prepend\n            // byte to stream.\n            if (code_point === null && isASCIIByte(bite))\n                stream.prepend(bite);\n            // 7. If code point is null, return error.\n            if (code_point === null)\n                return decoderError(this.fatal);\n            // 8. Return a code point whose value is code point.\n            return code_point;\n        }\n        // 6. If byte is an ASCII byte, return a code point whose value\n        // is byte.\n        if (isASCIIByte(bite))\n            return bite;\n        // 7. If byte is 0x80, return code point U+20AC.\n        if (bite === 0x80)\n            return 0x20AC;\n        // 8. If byte is in the range 0x81 to 0xFE, inclusive, set\n        // gb18030 first to byte and return continue.\n        if (inRange(bite, 0x81, 0xFE)) {\n            this.gb18030_first = bite;\n            return null;\n        }\n        // 9. Return error.\n        return decoderError(this.fatal);\n    };\n    return GB18030Decoder;\n}());\nexport { GB18030Decoder };\n//# sourceMappingURL=GB18030Decoder.js.map", "import { encoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexGB18030RangesPointerFor, indexPointerFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n * @param {boolean=} gbk_flag\n */\nvar GB18030Encoder = /** @class */ (function () {\n    function GB18030Encoder(options, gbk_flag) {\n        if (gbk_flag === void 0) { gbk_flag = undefined; }\n        this.gbk_flag = gbk_flag;\n        this.fatal = options.fatal;\n        // gb18030's decoder has an associated gbk flag (initially unset).\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    GB18030Encoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is an ASCII code point, return a byte whose\n        // value is code point.\n        if (isASCIICodePoint(code_point))\n            return code_point;\n        // 3. If code point is U+E5E5, return error with code point.\n        if (code_point === 0xE5E5)\n            return encoderError(code_point);\n        // 4. If the gbk flag is set and code point is U+20AC, return\n        // byte 0x80.\n        if (this.gbk_flag && code_point === 0x20AC)\n            return 0x80;\n        // 5. Let pointer be the index pointer for code point in index\n        // gb18030.\n        var pointer = indexPointerFor(code_point, index('gb18030'));\n        // 6. If pointer is not null, run these substeps:\n        if (pointer !== null) {\n            // 1. Let lead be Math.floor(pointer / 190) + 0x81.\n            var lead = Math.floor(pointer / 190) + 0x81;\n            // 2. Let trail be pointer % 190.\n            var trail = pointer % 190;\n            // 3. Let offset be 0x40 if trail is less than 0x3F and 0x41 otherwise.\n            var offset = trail < 0x3F ? 0x40 : 0x41;\n            // 4. Return two bytes whose values are lead and trail + offset.\n            return [lead, trail + offset];\n        }\n        // 7. If gbk flag is set, return error with code point.\n        if (this.gbk_flag)\n            return encoderError(code_point);\n        // 8. Set pointer to the index gb18030 ranges pointer for code\n        // point.\n        pointer = indexGB18030RangesPointerFor(code_point);\n        // 9. Let byte1 be Math.floor(pointer / 10 / 126 / 10).\n        var byte1 = Math.floor(pointer / 10 / 126 / 10);\n        // 10. Set pointer to pointer − byte1 × 10 × 126 × 10.\n        pointer = pointer - byte1 * 10 * 126 * 10;\n        // 11. Let byte2 be Math.floor(pointer / 10 / 126).\n        var byte2 = Math.floor(pointer / 10 / 126);\n        // 12. Set pointer to pointer − byte2 × 10 × 126.\n        pointer = pointer - byte2 * 10 * 126;\n        // 13. Let byte3 be Math.floor(pointer / 10).\n        var byte3 = Math.floor(pointer / 10);\n        // 14. Let byte4 be pointer − byte3 × 10.\n        var byte4 = pointer - byte3 * 10;\n        // 15. Return four bytes whose values are byte1 + 0x81, byte2 +\n        // 0x30, byte3 + 0x81, byte4 + 0x30.\n        return [byte1 + 0x81,\n            byte2 + 0x30,\n            byte3 + 0x81,\n            byte4 + 0x30];\n    };\n    return GB18030Encoder;\n}());\nexport { GB18030Encoder };\n//# sourceMappingURL=GB18030Encoder.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexCodePointFor } from \"../../encoding/indexes\";\nimport { end_of_stream } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\nvar states;\n(function (states) {\n    states[states[\"ASCII\"] = 0] = \"ASCII\";\n    states[states[\"Roman\"] = 1] = \"Roman\";\n    states[states[\"Katakana\"] = 2] = \"Katakana\";\n    states[states[\"LeadByte\"] = 3] = \"LeadByte\";\n    states[states[\"TrailByte\"] = 4] = \"TrailByte\";\n    states[states[\"EscapeStart\"] = 5] = \"EscapeStart\";\n    states[states[\"Escape\"] = 6] = \"Escape\";\n})(states || (states = {}));\nvar ISO2022JPDecoder = /** @class */ (function () {\n    /**\n     * @constructor\n     * @implements {Decoder}\n     * @param {{fatal: boolean}} options\n     */\n    function ISO2022JPDecoder(options) {\n        this.fatal = options.fatal;\n        // iso-2022-jp's decoder has an associated iso-2022-jp decoder\n        // state (initially ASCII), iso-2022-jp decoder output state\n        // (initially ASCII), iso-2022-jp lead (initially 0x00), and\n        // iso-2022-jp output flag (initially unset).\n        /** @type {number} */ this.iso2022jp_decoder_state = states.ASCII,\n            /** @type {number} */ this.iso2022jp_decoder_output_state = states.ASCII,\n            /** @type {number} */ this.iso2022jp_lead = 0x00,\n            /** @type {boolean} */ this.iso2022jp_output_flag = false;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    ISO2022JPDecoder.prototype.handler = function (stream, bite) {\n        // switching on iso-2022-jp decoder state:\n        switch (this.iso2022jp_decoder_state) {\n            default:\n            case states.ASCII:\n                // ASCII\n                // Based on byte:\n                // 0x1B\n                if (bite === 0x1B) {\n                    // Set iso-2022-jp decoder state to escape start and return\n                    // continue.\n                    this.iso2022jp_decoder_state = states.EscapeStart;\n                    return null;\n                }\n                // 0x00 to 0x7F, excluding 0x0E, 0x0F, and 0x1B\n                if (inRange(bite, 0x00, 0x7F) && bite !== 0x0E\n                    && bite !== 0x0F && bite !== 0x1B) {\n                    // Unset the iso-2022-jp output flag and return a code point\n                    // whose value is byte.\n                    this.iso2022jp_output_flag = false;\n                    return bite;\n                }\n                // end-of-stream\n                if (bite === end_of_stream) {\n                    // Return finished.\n                    return finished;\n                }\n                // Otherwise\n                // Unset the iso-2022-jp output flag and return error.\n                this.iso2022jp_output_flag = false;\n                return decoderError(this.fatal);\n            case states.Roman:\n                // Roman\n                // Based on byte:\n                // 0x1B\n                if (bite === 0x1B) {\n                    // Set iso-2022-jp decoder state to escape start and return\n                    // continue.\n                    this.iso2022jp_decoder_state = states.EscapeStart;\n                    return null;\n                }\n                // 0x5C\n                if (bite === 0x5C) {\n                    // Unset the iso-2022-jp output flag and return code point\n                    // U+00A5.\n                    this.iso2022jp_output_flag = false;\n                    return 0x00A5;\n                }\n                // 0x7E\n                if (bite === 0x7E) {\n                    // Unset the iso-2022-jp output flag and return code point\n                    // U+203E.\n                    this.iso2022jp_output_flag = false;\n                    return 0x203E;\n                }\n                // 0x00 to 0x7F, excluding 0x0E, 0x0F, 0x1B, 0x5C, and 0x7E\n                if (inRange(bite, 0x00, 0x7F) && bite !== 0x0E && bite !== 0x0F\n                    && bite !== 0x1B && bite !== 0x5C && bite !== 0x7E) {\n                    // Unset the iso-2022-jp output flag and return a code point\n                    // whose value is byte.\n                    this.iso2022jp_output_flag = false;\n                    return bite;\n                }\n                // end-of-stream\n                if (bite === end_of_stream) {\n                    // Return finished.\n                    return finished;\n                }\n                // Otherwise\n                // Unset the iso-2022-jp output flag and return error.\n                this.iso2022jp_output_flag = false;\n                return decoderError(this.fatal);\n            case states.Katakana:\n                // Katakana\n                // Based on byte:\n                // 0x1B\n                if (bite === 0x1B) {\n                    // Set iso-2022-jp decoder state to escape start and return\n                    // continue.\n                    this.iso2022jp_decoder_state = states.EscapeStart;\n                    return null;\n                }\n                // 0x21 to 0x5F\n                if (inRange(bite, 0x21, 0x5F)) {\n                    // Unset the iso-2022-jp output flag and return a code point\n                    // whose value is 0xFF61 − 0x21 + byte.\n                    this.iso2022jp_output_flag = false;\n                    return 0xFF61 - 0x21 + bite;\n                }\n                // end-of-stream\n                if (bite === end_of_stream) {\n                    // Return finished.\n                    return finished;\n                }\n                // Otherwise\n                // Unset the iso-2022-jp output flag and return error.\n                this.iso2022jp_output_flag = false;\n                return decoderError(this.fatal);\n            case states.LeadByte:\n                // Lead byte\n                // Based on byte:\n                // 0x1B\n                if (bite === 0x1B) {\n                    // Set iso-2022-jp decoder state to escape start and return\n                    // continue.\n                    this.iso2022jp_decoder_state = states.EscapeStart;\n                    return null;\n                }\n                // 0x21 to 0x7E\n                if (inRange(bite, 0x21, 0x7E)) {\n                    // Unset the iso-2022-jp output flag, set iso-2022-jp lead\n                    // to byte, iso-2022-jp decoder state to trail byte, and\n                    // return continue.\n                    this.iso2022jp_output_flag = false;\n                    this.iso2022jp_lead = bite;\n                    this.iso2022jp_decoder_state = states.TrailByte;\n                    return null;\n                }\n                // end-of-stream\n                if (bite === end_of_stream) {\n                    // Return finished.\n                    return finished;\n                }\n                // Otherwise\n                // Unset the iso-2022-jp output flag and return error.\n                this.iso2022jp_output_flag = false;\n                return decoderError(this.fatal);\n            case states.TrailByte:\n                // Trail byte\n                // Based on byte:\n                // 0x1B\n                if (bite === 0x1B) {\n                    // Set iso-2022-jp decoder state to escape start and return\n                    // continue.\n                    this.iso2022jp_decoder_state = states.EscapeStart;\n                    return decoderError(this.fatal);\n                }\n                // 0x21 to 0x7E\n                if (inRange(bite, 0x21, 0x7E)) {\n                    // 1. Set the iso-2022-jp decoder state to lead byte.\n                    this.iso2022jp_decoder_state = states.LeadByte;\n                    // 2. Let pointer be (iso-2022-jp lead − 0x21) × 94 + byte − 0x21.\n                    var pointer = (this.iso2022jp_lead - 0x21) * 94 + bite - 0x21;\n                    // 3. Let code point be the index code point for pointer in\n                    // index jis0208.\n                    var code_point = indexCodePointFor(pointer, index('jis0208'));\n                    // 4. If code point is null, return error.\n                    if (code_point === null)\n                        return decoderError(this.fatal);\n                    // 5. Return a code point whose value is code point.\n                    return code_point;\n                }\n                // end-of-stream\n                if (bite === end_of_stream) {\n                    // Set the iso-2022-jp decoder state to lead byte, prepend\n                    // byte to stream, and return error.\n                    this.iso2022jp_decoder_state = states.LeadByte;\n                    stream.prepend(bite);\n                    return decoderError(this.fatal);\n                }\n                // Otherwise\n                // Set iso-2022-jp decoder state to lead byte and return\n                // error.\n                this.iso2022jp_decoder_state = states.LeadByte;\n                return decoderError(this.fatal);\n            case states.EscapeStart:\n                // Escape start\n                // 1. If byte is either 0x24 or 0x28, set iso-2022-jp lead to\n                // byte, iso-2022-jp decoder state to escape, and return\n                // continue.\n                if (bite === 0x24 || bite === 0x28) {\n                    this.iso2022jp_lead = bite;\n                    this.iso2022jp_decoder_state = states.Escape;\n                    return null;\n                }\n                // 2. Prepend byte to stream.\n                stream.prepend(bite);\n                // 3. Unset the iso-2022-jp output flag, set iso-2022-jp\n                // decoder state to iso-2022-jp decoder output state, and\n                // return error.\n                this.iso2022jp_output_flag = false;\n                this.iso2022jp_decoder_state = this.iso2022jp_decoder_output_state;\n                return decoderError(this.fatal);\n            case states.Escape:\n                // Escape\n                // 1. Let lead be iso-2022-jp lead and set iso-2022-jp lead to\n                // 0x00.\n                var lead = this.iso2022jp_lead;\n                this.iso2022jp_lead = 0x00;\n                // 2. Let state be null.\n                var state = null;\n                // 3. If lead is 0x28 and byte is 0x42, set state to ASCII.\n                if (lead === 0x28 && bite === 0x42)\n                    state = states.ASCII;\n                // 4. If lead is 0x28 and byte is 0x4A, set state to Roman.\n                if (lead === 0x28 && bite === 0x4A)\n                    state = states.Roman;\n                // 5. If lead is 0x28 and byte is 0x49, set state to Katakana.\n                if (lead === 0x28 && bite === 0x49)\n                    state = states.Katakana;\n                // 6. If lead is 0x24 and byte is either 0x40 or 0x42, set\n                // state to lead byte.\n                if (lead === 0x24 && (bite === 0x40 || bite === 0x42))\n                    state = states.LeadByte;\n                // 7. If state is non-null, run these substeps:\n                if (state !== null) {\n                    // 1. Set iso-2022-jp decoder state and iso-2022-jp decoder\n                    // output state to states.\n                    this.iso2022jp_decoder_state = this.iso2022jp_decoder_state = state;\n                    // 2. Let output flag be the iso-2022-jp output flag.\n                    var output_flag = this.iso2022jp_output_flag;\n                    // 3. Set the iso-2022-jp output flag.\n                    this.iso2022jp_output_flag = true;\n                    // 4. Return continue, if output flag is unset, and error\n                    // otherwise.\n                    return !output_flag ? null : decoderError(this.fatal);\n                }\n                // 8. Prepend lead and byte to stream.\n                stream.prepend([lead, bite]);\n                // 9. Unset the iso-2022-jp output flag, set iso-2022-jp\n                // decoder state to iso-2022-jp decoder output state and\n                // return error.\n                this.iso2022jp_output_flag = false;\n                this.iso2022jp_decoder_state = this.iso2022jp_decoder_output_state;\n                return decoderError(this.fatal);\n        }\n    };\n    return ISO2022JPDecoder;\n}());\nexport { ISO2022JPDecoder };\n//# sourceMappingURL=ISO2022JPDecoder.js.map", "import { encoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexPointerFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\nvar states;\n(function (states) {\n    states[states[\"ASCII\"] = 0] = \"ASCII\";\n    states[states[\"Roman\"] = 1] = \"Roman\";\n    states[states[\"jis0208\"] = 2] = \"jis0208\";\n})(states || (states = {}));\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */\nvar ISO2022JPEncoder = /** @class */ (function () {\n    function ISO2022JPEncoder(options) {\n        this.fatal = options.fatal;\n        // iso-2022-jp's encoder has an associated iso-2022-jp encoder\n        // state which is one of ASCII, Roman, and jis0208 (initially\n        // ASCII).\n        /** @type {number} */ this.iso2022jp_state = states.ASCII;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    ISO2022JPEncoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream and iso-2022-jp encoder\n        // state is not ASCII, prepend code point to stream, set\n        // iso-2022-jp encoder state to ASCII, and return three bytes\n        // 0x1B 0x28 0x42.\n        if (code_point === end_of_stream &&\n            this.iso2022jp_state !== states.ASCII) {\n            stream.prepend(code_point);\n            this.iso2022jp_state = states.ASCII;\n            return [0x1B, 0x28, 0x42];\n        }\n        // 2. If code point is end-of-stream and iso-2022-jp encoder\n        // state is ASCII, return finished.\n        if (code_point === end_of_stream && this.iso2022jp_state === states.ASCII)\n            return finished;\n        // 3. If ISO-2022-JP encoder state is ASCII or Roman, and code\n        // point is U+000E, U+000F, or U+001B, return error with U+FFFD.\n        if ((this.iso2022jp_state === states.ASCII ||\n            this.iso2022jp_state === states.Roman) &&\n            (code_point === 0x000E || code_point === 0x000F ||\n                code_point === 0x001B)) {\n            return encoderError(0xFFFD);\n        }\n        // 4. If iso-2022-jp encoder state is ASCII and code point is an\n        // ASCII code point, return a byte whose value is code point.\n        if (this.iso2022jp_state === states.ASCII &&\n            isASCIICodePoint(code_point))\n            return code_point;\n        // 5. If iso-2022-jp encoder state is Roman and code point is an\n        // ASCII code point, excluding U+005C and U+007E, or is U+00A5\n        // or U+203E, run these substeps:\n        if (this.iso2022jp_state === states.Roman &&\n            ((isASCIICodePoint(code_point) &&\n                code_point !== 0x005C && code_point !== 0x007E) ||\n                (code_point == 0x00A5 || code_point == 0x203E))) {\n            // 1. If code point is an ASCII code point, return a byte\n            // whose value is code point.\n            if (isASCIICodePoint(code_point))\n                return code_point;\n            // 2. If code point is U+00A5, return byte 0x5C.\n            if (code_point === 0x00A5)\n                return 0x5C;\n            // 3. If code point is U+203E, return byte 0x7E.\n            if (code_point === 0x203E)\n                return 0x7E;\n        }\n        // 6. If code point is an ASCII code point, and iso-2022-jp\n        // encoder state is not ASCII, prepend code point to stream, set\n        // iso-2022-jp encoder state to ASCII, and return three bytes\n        // 0x1B 0x28 0x42.\n        if (isASCIICodePoint(code_point) &&\n            this.iso2022jp_state !== states.ASCII) {\n            stream.prepend(code_point);\n            this.iso2022jp_state = states.ASCII;\n            return [0x1B, 0x28, 0x42];\n        }\n        // 7. If code point is either U+00A5 or U+203E, and iso-2022-jp\n        // encoder state is not Roman, prepend code point to stream, set\n        // iso-2022-jp encoder state to Roman, and return three bytes\n        // 0x1B 0x28 0x4A.\n        if ((code_point === 0x00A5 || code_point === 0x203E) &&\n            this.iso2022jp_state !== states.Roman) {\n            stream.prepend(code_point);\n            this.iso2022jp_state = states.Roman;\n            return [0x1B, 0x28, 0x4A];\n        }\n        // 8. If code point is U+2212, set it to U+FF0D.\n        if (code_point === 0x2212)\n            code_point = 0xFF0D;\n        // 9. Let pointer be the index pointer for code point in index\n        // jis0208.\n        var pointer = indexPointerFor(code_point, index('jis0208'));\n        // 10. If pointer is null, return error with code point.\n        if (pointer === null)\n            return encoderError(code_point);\n        // 11. If iso-2022-jp encoder state is not jis0208, prepend code\n        // point to stream, set iso-2022-jp encoder state to jis0208,\n        // and return three bytes 0x1B 0x24 0x42.\n        if (this.iso2022jp_state !== states.jis0208) {\n            stream.prepend(code_point);\n            this.iso2022jp_state = states.jis0208;\n            return [0x1B, 0x24, 0x42];\n        }\n        // 12. Let lead be Math.floor(pointer / 94) + 0x21.\n        var lead = Math.floor(pointer / 94) + 0x21;\n        // 13. Let trail be pointer % 94 + 0x21.\n        var trail = pointer % 94 + 0x21;\n        // 14. Return two bytes whose values are lead and trail.\n        return [lead, trail];\n    };\n    return ISO2022JPEncoder;\n}());\nexport { ISO2022JPEncoder };\n//# sourceMappingURL=ISO2022JPEncoder.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { index, indexCodePointFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIIByte } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */\nvar ShiftJISDecoder = /** @class */ (function () {\n    function ShiftJISDecoder(options) {\n        this.fatal = options.fatal;\n        // Shift_JIS's decoder has an associated Shift_JIS lead (initially\n        // 0x00).\n        /** @type {number} */ this.Shift_JIS_lead = 0x00;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    ShiftJISDecoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream and Shift_JIS lead is not 0x00,\n        // set Shift_JIS lead to 0x00 and return error.\n        if (bite === end_of_stream && this.Shift_JIS_lead !== 0x00) {\n            this.Shift_JIS_lead = 0x00;\n            return decoderError(this.fatal);\n        }\n        // 2. If byte is end-of-stream and Shift_JIS lead is 0x00,\n        // return finished.\n        if (bite === end_of_stream && this.Shift_JIS_lead === 0x00)\n            return finished;\n        // 3. If Shift_JIS lead is not 0x00, let lead be Shift_JIS lead,\n        // let pointer be null, set Shift_JIS lead to 0x00, and then run\n        // these substeps:\n        if (this.Shift_JIS_lead !== 0x00) {\n            var lead = this.Shift_JIS_lead;\n            var pointer = null;\n            this.Shift_JIS_lead = 0x00;\n            // 1. Let offset be 0x40, if byte is less than 0x7F, and 0x41\n            // otherwise.\n            var offset = (bite < 0x7F) ? 0x40 : 0x41;\n            // 2. Let lead offset be 0x81, if lead is less than 0xA0, and\n            // 0xC1 otherwise.\n            var lead_offset = (lead < 0xA0) ? 0x81 : 0xC1;\n            // 3. If byte is in the range 0x40 to 0x7E, inclusive, or 0x80\n            // to 0xFC, inclusive, set pointer to (lead − lead offset) ×\n            // 188 + byte − offset.\n            if (inRange(bite, 0x40, 0x7E) || inRange(bite, 0x80, 0xFC))\n                pointer = (lead - lead_offset) * 188 + bite - offset;\n            // 4. If pointer is in the range 8836 to 10715, inclusive,\n            // return a code point whose value is 0xE000 − 8836 + pointer.\n            if (inRange(pointer, 8836, 10715))\n                return 0xE000 - 8836 + pointer;\n            // 5. Let code point be null, if pointer is null, and the\n            // index code point for pointer in index jis0208 otherwise.\n            var code_point = (pointer === null) ? null :\n                indexCodePointFor(pointer, index('jis0208'));\n            // 6. If code point is null and byte is an ASCII byte, prepend\n            // byte to stream.\n            if (code_point === null && isASCIIByte(bite))\n                stream.prepend(bite);\n            // 7. If code point is null, return error.\n            if (code_point === null)\n                return decoderError(this.fatal);\n            // 8. Return a code point whose value is code point.\n            return code_point;\n        }\n        // 4. If byte is an ASCII byte or 0x80, return a code point\n        // whose value is byte.\n        if (isASCIIByte(bite) || bite === 0x80)\n            return bite;\n        // 5. If byte is in the range 0xA1 to 0xDF, inclusive, return a\n        // code point whose value is 0xFF61 − 0xA1 + byte.\n        if (inRange(bite, 0xA1, 0xDF))\n            return 0xFF61 - 0xA1 + bite;\n        // 6. If byte is in the range 0x81 to 0x9F, inclusive, or 0xE0\n        // to 0xFC, inclusive, set Shift_JIS lead to byte and return\n        // continue.\n        if (inRange(bite, 0x81, 0x9F) || inRange(bite, 0xE0, 0xFC)) {\n            this.Shift_JIS_lead = bite;\n            return null;\n        }\n        // 7. Return error.\n        return decoderError(this.fatal);\n    };\n    return ShiftJISDecoder;\n}());\nexport { ShiftJISDecoder };\n//# sourceMappingURL=ShiftJISDecoder.js.map", "import { encoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { indexShiftJISPointerFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */\nvar ShiftJISEncoder = /** @class */ (function () {\n    function ShiftJISEncoder(options) {\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    ShiftJISEncoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is an ASCII code point or U+0080, return a\n        // byte whose value is code point.\n        if (isASCIICodePoint(code_point) || code_point === 0x0080)\n            return code_point;\n        // 3. If code point is U+00A5, return byte 0x5C.\n        if (code_point === 0x00A5)\n            return 0x5C;\n        // 4. If code point is U+203E, return byte 0x7E.\n        if (code_point === 0x203E)\n            return 0x7E;\n        // 5. If code point is in the range U+FF61 to U+FF9F, inclusive,\n        // return a byte whose value is code point − 0xFF61 + 0xA1.\n        if (inRange(code_point, 0xFF61, 0xFF9F))\n            return code_point - 0xFF61 + 0xA1;\n        // 6. If code point is U+2212, set it to U+FF0D.\n        if (code_point === 0x2212)\n            code_point = 0xFF0D;\n        // 7. Let pointer be the index Shift_JIS pointer for code point.\n        var pointer = indexShiftJISPointerFor(code_point);\n        // 8. If pointer is null, return error with code point.\n        if (pointer === null)\n            return encoderError(code_point);\n        // 9. Let lead be Math.floor(pointer / 188).\n        var lead = Math.floor(pointer / 188);\n        // 10. Let lead offset be 0x81, if lead is less than 0x1F, and\n        // 0xC1 otherwise.\n        var lead_offset = (lead < 0x1F) ? 0x81 : 0xC1;\n        // 11. Let trail be pointer % 188.\n        var trail = pointer % 188;\n        // 12. Let offset be 0x40, if trail is less than 0x3F, and 0x41\n        // otherwise.\n        var offset = (trail < 0x3F) ? 0x40 : 0x41;\n        // 13. Return two bytes whose values are lead + lead offset and\n        // trail + offset.\n        return [lead + lead_offset, trail + offset];\n    };\n    return ShiftJISEncoder;\n}());\nexport { ShiftJISEncoder };\n//# sourceMappingURL=ShiftJISEncoder.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { end_of_stream, isASCIIByte } from \"../../encoding/terminology\";\n/**\n * @constructor\n * @implements {Decoder}\n * @param {!Array.<number>} index The encoding index.\n * @param {{fatal: boolean}} options\n */\nvar SingleByteDecoder = /** @class */ (function () {\n    function SingleByteDecoder(index, options) {\n        this.index = index;\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    SingleByteDecoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream, return finished.\n        if (bite === end_of_stream)\n            return finished;\n        // 2. If byte is an ASCII byte, return a code point whose value\n        // is byte.\n        if (isASCIIByte(bite))\n            return bite;\n        // 3. Let code point be the index code point for byte − 0x80 in\n        // index single-byte.\n        var code_point = this.index[bite - 0x80];\n        // 4. If code point is null, return error.\n        if (!code_point)\n            return decoderError(this.fatal);\n        // 5. Return a code point whose value is code point.\n        return code_point;\n    };\n    return SingleByteDecoder;\n}());\nexport { SingleByteDecoder };\n//# sourceMappingURL=SingleByteDecoder.js.map", "import { encoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { indexPointerFor } from \"../../encoding/indexes\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\n/**\n * @constructor\n * @implements {Encoder}\n * @param {!Array.<?number>} index The encoding index.\n * @param {{fatal: boolean}} options\n */\nvar SingleByteEncoder = /** @class */ (function () {\n    function SingleByteEncoder(index, options) {\n        this.index = index;\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    SingleByteEncoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is an ASCII code point, return a byte whose\n        // value is code point.\n        if (isASCIICodePoint(code_point))\n            return code_point;\n        // 3. Let pointer be the index pointer for code point in index\n        // single-byte.\n        var pointer = indexPointerFor(code_point, this.index);\n        // 4. If pointer is null, return error with code point.\n        if (pointer === null)\n            encoderError(code_point);\n        // 5. Return a byte whose value is pointer + 0x80.\n        return pointer + 0x80;\n    };\n    return SingleByteEncoder;\n}());\nexport { SingleByteEncoder };\n//# sourceMappingURL=SingleByteEncoder.js.map", "/**\n * @param {number} code_unit\n * @param {boolean} utf16be\n * @return {!Array.<number>} bytes\n */\nexport function convertCodeUnitToBytes(code_unit, utf16be) {\n    // 1. Let byte1 be code unit >> 8.\n    var byte1 = code_unit >> 8;\n    // 2. Let byte2 be code unit & 0x00FF.\n    var byte2 = code_unit & 0x00FF;\n    // 3. Then return the bytes in order:\n    // utf-16be flag is set: byte1, then byte2.\n    if (utf16be)\n        return [byte1, byte2];\n    // utf-16be flag is unset: byte2, then byte1.\n    return [byte2, byte1];\n}\n//# sourceMappingURL=converCodeUnitToBytes.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { end_of_stream } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\nimport { convertCodeUnitToBytes } from \"./converCodeUnitToBytes\";\n/**\n * @constructor\n * @implements {Decoder}\n * @param {boolean} utf16_be True if big-endian, false if little-endian.\n * @param {{fatal: boolean}} options\n */\nvar UTF16Decoder = /** @class */ (function () {\n    function UTF16Decoder(utf16_be, options) {\n        this.utf16_be = utf16_be;\n        this.fatal = options.fatal;\n        /** @type {?number} */ this.utf16_lead_byte = null;\n        /** @type {?number} */ this.utf16_lead_surrogate = null;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    UTF16Decoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream and either utf-16 lead byte or\n        // utf-16 lead surrogate is not null, set utf-16 lead byte and\n        // utf-16 lead surrogate to null, and return error.\n        if (bite === end_of_stream && (this.utf16_lead_byte !== null ||\n            this.utf16_lead_surrogate !== null)) {\n            return decoderError(this.fatal);\n        }\n        // 2. If byte is end-of-stream and utf-16 lead byte and utf-16\n        // lead surrogate are null, return finished.\n        if (bite === end_of_stream && this.utf16_lead_byte === null &&\n            this.utf16_lead_surrogate === null) {\n            return finished;\n        }\n        // 3. If utf-16 lead byte is null, set utf-16 lead byte to byte\n        // and return continue.\n        if (this.utf16_lead_byte === null) {\n            this.utf16_lead_byte = bite;\n            return null;\n        }\n        // 4. Let code unit be the result of:\n        var code_unit;\n        if (this.utf16_be) {\n            // utf-16be decoder flag is set\n            //   (utf-16 lead byte << 8) + byte.\n            code_unit = (this.utf16_lead_byte << 8) + bite;\n        }\n        else {\n            // utf-16be decoder flag is unset\n            //   (byte << 8) + utf-16 lead byte.\n            code_unit = (bite << 8) + this.utf16_lead_byte;\n        }\n        // Then set utf-16 lead byte to null.\n        this.utf16_lead_byte = null;\n        // 5. If utf-16 lead surrogate is not null, let lead surrogate\n        // be utf-16 lead surrogate, set utf-16 lead surrogate to null,\n        // and then run these substeps:\n        if (this.utf16_lead_surrogate !== null) {\n            var lead_surrogate = this.utf16_lead_surrogate;\n            this.utf16_lead_surrogate = null;\n            // 1. If code unit is in the range U+DC00 to U+DFFF,\n            // inclusive, return a code point whose value is 0x10000 +\n            // ((lead surrogate − 0xD800) << 10) + (code unit − 0xDC00).\n            if (inRange(code_unit, 0xDC00, 0xDFFF)) {\n                return 0x10000 + (lead_surrogate - 0xD800) * 0x400 +\n                    (code_unit - 0xDC00);\n            }\n            // 2. Prepend the sequence resulting of converting code unit\n            // to bytes using utf-16be decoder flag to stream and return\n            // error.\n            stream.prepend(convertCodeUnitToBytes(code_unit, this.utf16_be));\n            return decoderError(this.fatal);\n        }\n        // 6. If code unit is in the range U+D800 to U+DBFF, inclusive,\n        // set utf-16 lead surrogate to code unit and return continue.\n        if (inRange(code_unit, 0xD800, 0xDBFF)) {\n            this.utf16_lead_surrogate = code_unit;\n            return null;\n        }\n        // 7. If code unit is in the range U+DC00 to U+DFFF, inclusive,\n        // return error.\n        if (inRange(code_unit, 0xDC00, 0xDFFF))\n            return decoderError(this.fatal);\n        // 8. Return code point code unit.\n        return code_unit;\n    };\n    return UTF16Decoder;\n}());\nexport { UTF16Decoder };\n//# sourceMappingURL=UTF16Decoder.js.map", "import { finished } from \"../../encoding/finished\";\nimport { end_of_stream } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\nimport { convertCodeUnitToBytes } from \"./converCodeUnitToBytes\";\n/**\n * @constructor\n * @implements {Encoder}\n * @param {boolean} utf16_be True if big-endian, false if little-endian.\n * @param {{fatal: boolean}} options\n */\nvar UTF16Encoder = /** @class */ (function () {\n    function UTF16Encoder(utf16_be, options) {\n        this.utf16_be = utf16_be;\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    UTF16Encoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is in the range U+0000 to U+FFFF, inclusive,\n        // return the sequence resulting of converting code point to\n        // bytes using utf-16be encoder flag.\n        if (inRange(code_point, 0x0000, 0xFFFF))\n            return convertCodeUnitToBytes(code_point, this.utf16_be);\n        // 3. Let lead be ((code point − 0x10000) >> 10) + 0xD800,\n        // converted to bytes using utf-16be encoder flag.\n        var lead = convertCodeUnitToBytes(((code_point - 0x10000) >> 10) + 0xD800, this.utf16_be);\n        // 4. Let trail be ((code point − 0x10000) & 0x3FF) + 0xDC00,\n        // converted to bytes using utf-16be encoder flag.\n        var trail = convertCodeUnitToBytes(((code_point - 0x10000) & 0x3FF) + 0xDC00, this.utf16_be);\n        // 5. Return a byte sequence of lead followed by trail.\n        return lead.concat(trail);\n    };\n    return UTF16Encoder;\n}());\nexport { UTF16Encoder };\n//# sourceMappingURL=UTF16Encoder.js.map", "import { decoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { end_of_stream } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */\nvar UTF8Decoder = /** @class */ (function () {\n    function UTF8Decoder(options) {\n        this.fatal = options.fatal;\n        // utf-8's decoder's has an associated utf-8 code point, utf-8\n        // bytes seen, and utf-8 bytes needed (all initially 0), a utf-8\n        // lower boundary (initially 0x80), and a utf-8 upper boundary\n        // (initially 0xBF).\n        /** @type {number} */ this.utf8_code_point = 0,\n            /** @type {number} */ this.utf8_bytes_seen = 0,\n            /** @type {number} */ this.utf8_bytes_needed = 0,\n            /** @type {number} */ this.utf8_lower_boundary = 0x80,\n            /** @type {number} */ this.utf8_upper_boundary = 0xBF;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    UTF8Decoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream and utf-8 bytes needed is not 0,\n        // set utf-8 bytes needed to 0 and return error.\n        if (bite === end_of_stream && this.utf8_bytes_needed !== 0) {\n            this.utf8_bytes_needed = 0;\n            return decoderError(this.fatal);\n        }\n        // 2. If byte is end-of-stream, return finished.\n        if (bite === end_of_stream)\n            return finished;\n        // 3. If utf-8 bytes needed is 0, based on byte:\n        if (this.utf8_bytes_needed === 0) {\n            // 0x00 to 0x7F\n            if (inRange(bite, 0x00, 0x7F)) {\n                // Return a code point whose value is byte.\n                return bite;\n            }\n            // 0xC2 to 0xDF\n            else if (inRange(bite, 0xC2, 0xDF)) {\n                // 1. Set utf-8 bytes needed to 1.\n                this.utf8_bytes_needed = 1;\n                // 2. Set UTF-8 code point to byte & 0x1F.\n                this.utf8_code_point = bite & 0x1F;\n            }\n            // 0xE0 to 0xEF\n            else if (inRange(bite, 0xE0, 0xEF)) {\n                // 1. If byte is 0xE0, set utf-8 lower boundary to 0xA0.\n                if (bite === 0xE0)\n                    this.utf8_lower_boundary = 0xA0;\n                // 2. If byte is 0xED, set utf-8 upper boundary to 0x9F.\n                if (bite === 0xED)\n                    this.utf8_upper_boundary = 0x9F;\n                // 3. Set utf-8 bytes needed to 2.\n                this.utf8_bytes_needed = 2;\n                // 4. Set UTF-8 code point to byte & 0xF.\n                this.utf8_code_point = bite & 0xF;\n            }\n            // 0xF0 to 0xF4\n            else if (inRange(bite, 0xF0, 0xF4)) {\n                // 1. If byte is 0xF0, set utf-8 lower boundary to 0x90.\n                if (bite === 0xF0)\n                    this.utf8_lower_boundary = 0x90;\n                // 2. If byte is 0xF4, set utf-8 upper boundary to 0x8F.\n                if (bite === 0xF4)\n                    this.utf8_upper_boundary = 0x8F;\n                // 3. Set utf-8 bytes needed to 3.\n                this.utf8_bytes_needed = 3;\n                // 4. Set UTF-8 code point to byte & 0x7.\n                this.utf8_code_point = bite & 0x7;\n            }\n            // Otherwise\n            else {\n                // Return error.\n                return decoderError(this.fatal);\n            }\n            // Return continue.\n            return null;\n        }\n        // 4. If byte is not in the range utf-8 lower boundary to utf-8\n        // upper boundary, inclusive, run these substeps:\n        if (!inRange(bite, this.utf8_lower_boundary, this.utf8_upper_boundary)) {\n            // 1. Set utf-8 code point, utf-8 bytes needed, and utf-8\n            // bytes seen to 0, set utf-8 lower boundary to 0x80, and set\n            // utf-8 upper boundary to 0xBF.\n            this.utf8_code_point = this.utf8_bytes_needed = this.utf8_bytes_seen = 0;\n            this.utf8_lower_boundary = 0x80;\n            this.utf8_upper_boundary = 0xBF;\n            // 2. Prepend byte to stream.\n            stream.prepend(bite);\n            // 3. Return error.\n            return decoderError(this.fatal);\n        }\n        // 5. Set utf-8 lower boundary to 0x80 and utf-8 upper boundary\n        // to 0xBF.\n        this.utf8_lower_boundary = 0x80;\n        this.utf8_upper_boundary = 0xBF;\n        // 6. Set UTF-8 code point to (UTF-8 code point << 6) | (byte &\n        // 0x3F)\n        this.utf8_code_point = (this.utf8_code_point << 6) | (bite & 0x3F);\n        // 7. Increase utf-8 bytes seen by one.\n        this.utf8_bytes_seen += 1;\n        // 8. If utf-8 bytes seen is not equal to utf-8 bytes needed,\n        // continue.\n        if (this.utf8_bytes_seen !== this.utf8_bytes_needed)\n            return null;\n        // 9. Let code point be utf-8 code point.\n        var code_point = this.utf8_code_point;\n        // 10. Set utf-8 code point, utf-8 bytes needed, and utf-8 bytes\n        // seen to 0.\n        this.utf8_code_point = this.utf8_bytes_needed = this.utf8_bytes_seen = 0;\n        // 11. Return a code point whose value is code point.\n        return code_point;\n    };\n    return UTF8Decoder;\n}());\nexport { UTF8Decoder };\n//# sourceMappingURL=UTF8Decoder.js.map", "import { finished } from \"../../encoding/finished\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */\nvar UTF8Encoder = /** @class */ (function () {\n    function UTF8Encoder(options) {\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    UTF8Encoder.prototype.handler = function (stream, code_point) {\n        // 1. If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is an ASCII code point, return a byte whose\n        // value is code point.\n        if (isASCIICodePoint(code_point))\n            return code_point;\n        // 3. Set count and offset based on the range code point is in:\n        var count, offset;\n        // U+0080 to U+07FF, inclusive:\n        if (inRange(code_point, 0x0080, 0x07FF)) {\n            // 1 and 0xC0\n            count = 1;\n            offset = 0xC0;\n        }\n        // U+0800 to U+FFFF, inclusive:\n        else if (inRange(code_point, 0x0800, 0xFFFF)) {\n            // 2 and 0xE0\n            count = 2;\n            offset = 0xE0;\n        }\n        // U+10000 to U+10FFFF, inclusive:\n        else if (inRange(code_point, 0x10000, 0x10FFFF)) {\n            // 3 and 0xF0\n            count = 3;\n            offset = 0xF0;\n        }\n        // 4. Let bytes be a byte sequence whose first byte is (code\n        // point >> (6 × count)) + offset.\n        var bytes = [(code_point >> (6 * count)) + offset];\n        // 5. Run these substeps while count is greater than 0:\n        while (count > 0) {\n            // 1. Set temp to code point >> (6 × (count − 1)).\n            var temp = code_point >> (6 * (count - 1));\n            // 2. Append to bytes 0x80 | (temp & 0x3F).\n            bytes.push(0x80 | (temp & 0x3F));\n            // 3. Decrease count by one.\n            count -= 1;\n        }\n        // 6. Return bytes bytes, in order.\n        return bytes;\n    };\n    return UTF8Encoder;\n}());\nexport { UTF8Encoder };\n//# sourceMappingURL=UTF8Encoder.js.map", "import { finished } from \"../../encoding/finished\";\nimport { end_of_stream, isASCIIByte } from \"../../encoding/terminology\";\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */\nvar XUserDefinedDecoder = /** @class */ (function () {\n    function XUserDefinedDecoder(options) {\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream The stream of bytes being decoded.\n     * @param {number} bite The next byte read from the stream.\n     * @return {?(number|!Array.<number>)} The next code point(s)\n     *     decoded, or null if not enough data exists in the input\n     *     stream to decode a complete code point.\n     */\n    XUserDefinedDecoder.prototype.handler = function (stream, bite) {\n        // 1. If byte is end-of-stream, return finished.\n        if (bite === end_of_stream)\n            return finished;\n        // 2. If byte is an ASCII byte, return a code point whose value\n        // is byte.\n        if (isASCIIByte(bite))\n            return bite;\n        // 3. Return a code point whose value is 0xF780 + byte − 0x80.\n        return 0xF780 + bite - 0x80;\n    };\n    return XUserDefinedDecoder;\n}());\nexport { XUserDefinedDecoder };\n//# sourceMappingURL=XUserDefinedDecoder.js.map", "import { encoderError } from \"../../encoding/encodings\";\nimport { finished } from \"../../encoding/finished\";\nimport { end_of_stream, isASCIICodePoint } from \"../../encoding/terminology\";\nimport { inRange } from \"../../encoding/utilities\";\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */\nvar XUserDefinedEncoder = /** @class */ (function () {\n    function XUserDefinedEncoder(options) {\n        this.fatal = options.fatal;\n    }\n    /**\n     * @param {Stream} stream Input stream.\n     * @param {number} code_point Next code point read from the stream.\n     * @return {(number|!Array.<number>)} Byte(s) to emit.\n     */\n    XUserDefinedEncoder.prototype.handler = function (stream, code_point) {\n        // 1.If code point is end-of-stream, return finished.\n        if (code_point === end_of_stream)\n            return finished;\n        // 2. If code point is an ASCII code point, return a byte whose\n        // value is code point.\n        if (isASCIICodePoint(code_point))\n            return code_point;\n        // 3. If code point is in the range U+F780 to U+F7FF, inclusive,\n        // return a byte whose value is code point − 0xF780 + 0x80.\n        if (inRange(code_point, 0xF780, 0xF7FF))\n            return code_point - 0xF780 + 0x80;\n        // 4. Return error with code point.\n        return encoderError(code_point);\n    };\n    return XUserDefinedEncoder;\n}());\nexport { XUserDefinedEncoder };\n//# sourceMappingURL=XUserDefinedEncoder.js.map", "// This is free and unencumbered software released into the public domain.\n// See LICENSE.md for more information.\nimport { Big5Decoder, Big5Encoder } from '../coders/big5';\nimport { EUCJPDecoder, EUCJPEncoder } from '../coders/euc-jp';\nimport { EUCKRDecoder, EUCKREncoder } from '../coders/euc-kr';\nimport { GB18030Decoder, GB18030Encoder } from '../coders/gb18030';\nimport { ISO2022JPDecoder, ISO2022JPEncoder } from '../coders/iso-2022-jp';\nimport { ShiftJISDecoder, ShiftJISEncoder } from '../coders/shift-jis';\nimport { SingleByteDecoder, SingleByteEncoder } from '../coders/single-byte';\nimport { UTF16Decoder, UTF16Encoder } from '../coders/utf-16';\nimport { UTF8Decoder, UTF8Encoder } from '../coders/utf-8';\nimport { XUserDefinedDecoder, XUserDefinedEncoder } from '../coders/x-user-defined';\nimport { encodings } from './encodings';\nimport { index } from './indexes';\nimport { getEncodingIndexes } from './encoding-indexes-provider';\n//\n// Utilities\n//\n// import './encoding/utilities';\n//\n// Implementation of Encoding specification\n// https://encoding.spec.whatwg.org/\n//\n//\n// 4. Terminology\n//\n// import './encoding/terminology';\n//\n// 5. Encodings\n//\n// import \"./encoding/encodings\";\n//\n// 6. Indexes\n//\n// import './encoding/indexes';\nvar encodingIndexes = getEncodingIndexes();\n// Registry of of encoder/decoder factories, by encoding name.\n/** @type {Object.<string, function({fatal:boolean}): Encoder>} */\n// const encoders: Encoders = {};\n/** @type {Object.<string, function({fatal:boolean}): Decoder>} */\n// const decoders: Decoders = {};\n//\n// 10. Legacy single-byte encodings\n//\n// 10.1 single-byte decoder\n// 10.2 single-byte encoder\nexport var encoders = {\n    // 9.1 utf-8\n    // 9.1.1 utf-8 decoder\n    // 9.1.2 utf-8 encoder\n    /** @param {{fatal: boolean}} options */\n    'UTF-8': function (options) { return new UTF8Encoder(options); },\n    //\n    // 11. Legacy multi-byte Chinese (simplified) encodings\n    //\n    // 11.1 gbk\n    // 11.1.1 gbk decoder\n    // gbk's decoder is gb18030's decoder.\n    // 11.1.2 gbk encoder\n    // gbk's encoder is gb18030's encoder with its gbk flag set.\n    /** @param {{fatal: boolean}} options */\n    'GBK': function (options) { return new GB18030Encoder(options, true); },\n    // 11.2 gb18030\n    // 11.2.1 gb18030 decoder\n    // 11.2.2 gb18030 encoder\n    /** @param {{fatal: boolean}} options */\n    'gb18030': function (options) { return new GB18030Encoder(options); },\n    //\n    // 12. Legacy multi-byte Chinese (traditional) encodings\n    //\n    // 12.1 Big5\n    // 12.1.1 Big5 decoder\n    // 12.1.2 Big5 encoder\n    /** @param {{fatal: boolean}} options */\n    'Big5': function (options) { return new Big5Encoder(options); },\n    //\n    // 13. Legacy multi-byte Japanese encodings\n    //\n    // 13.1 euc-jp\n    // 13.1.1 euc-jp decoder\n    // 13.1.2 euc-jp encoder\n    /** @param {{fatal: boolean}} options */\n    'EUC-JP': function (options) { return new EUCJPEncoder(options); },\n    // 13.2 iso-2022-jp\n    // 13.2.1 iso-2022-jp decoder\n    // 13.2.2 iso-2022-jp encoder\n    /** @param {{fatal: boolean}} options */\n    'ISO-2022-JP': function (options) { return new ISO2022JPEncoder(options); },\n    // 13.3 Shift_JIS\n    // 13.3.1 Shift_JIS decoder\n    // 13.3.2 Shift_JIS encoder\n    /** @param {{fatal: boolean}} options */\n    'Shift_JIS': function (options) { return new ShiftJISEncoder(options); },\n    //\n    // 14. Legacy multi-byte Korean encodings\n    //\n    // 14.1 euc-kr\n    // 14.1.1 euc-kr decoder\n    // 14.1.2 euc-kr encoder\n    /** @param {{fatal: boolean}} options */\n    'EUC-KR': function (options) { return new EUCKREncoder(options); },\n    //\n    // 15. Legacy miscellaneous encodings\n    //\n    // 15.1 replacement\n    // Not needed - API throws RangeError\n    // 15.2 Common infrastructure for utf-16be and utf-16le\n    // 15.2.1 shared utf-16 decoder\n    // 15.2.2 shared utf-16 encoder\n    // 15.3 utf-16be\n    // 15.3.1 utf-16be decoder\n    /** @param {{fatal: boolean}} options */\n    'UTF-16BE': function (options) { return new UTF16Encoder(true, options); },\n    // 15.3.2 utf-16be encoder\n    // 15.4 utf-16le\n    // 15.4.1 utf-16le decoder\n    /** @param {{fatal: boolean}} options */\n    'UTF-16LE': function (options) { return new UTF16Encoder(false, options); },\n    // 15.4.2 utf-16le encoder\n    // 15.5 x-user-defined\n    // 15.5.1 x-user-defined decoder\n    // 15.5.2 x-user-defined encoder\n    /** @param {{fatal: boolean}} options */\n    'x-user-defined': function (options) { return new XUserDefinedEncoder(options); },\n};\nexport var decoders = {\n    /** @param {{fatal: boolean}} options */\n    'UTF-8': function (options) { return new UTF8Decoder(options); },\n    /** @param {{fatal: boolean}} options */\n    'GBK': function (options) { return new GB18030Decoder(options); },\n    /** @param {{fatal: boolean}} options */\n    'gb18030': function (options) { return new GB18030Decoder(options); },\n    /** @param {{fatal: boolean}} options */\n    'Big5': function (options) { return new Big5Decoder(options); },\n    /** @param {{fatal: boolean}} options */\n    'EUC-JP': function (options) { return new EUCJPDecoder(options); },\n    /** @param {{fatal: boolean}} options */\n    'ISO-2022-JP': function (options) { return new ISO2022JPDecoder(options); },\n    /** @param {{fatal: boolean}} options */\n    'Shift_JIS': function (options) { return new ShiftJISDecoder(options); },\n    /** @param {{fatal: boolean}} options */\n    'EUC-KR': function (options) { return new EUCKRDecoder(options); },\n    /** @param {{fatal: boolean}} options */\n    'UTF-16BE': function (options) { return new UTF16Decoder(true, options); },\n    /** @param {{fatal: boolean}} options */\n    'UTF-16LE': function (options) { return new UTF16Decoder(false, options); },\n    /** @param {{fatal: boolean}} options */\n    'x-user-defined': function (options) { return new XUserDefinedDecoder(options); },\n};\nif (encodingIndexes) {\n    encodings.forEach(function (category) {\n        if (category.heading !== 'Legacy single-byte encodings')\n            return;\n        category.encodings.forEach(function (encoding) {\n            var name = encoding.name;\n            var idx = index(name.toLowerCase());\n            /** @param {{fatal: boolean}} options */\n            decoders[name] = function (options) {\n                return new SingleByteDecoder(idx, options);\n            };\n            /** @param {{fatal: boolean}} options */\n            encoders[name] = function (options) {\n                return new SingleByteEncoder(idx, options);\n            };\n        });\n    });\n}\n//# sourceMappingURL=encoding-factory.js.map", "import { end_of_stream } from \"../encoding/terminology\";\n/**\n * A stream represents an ordered sequence of tokens.\n */\nvar Stream = /** @class */ (function () {\n    /**\n     *\n     * @constructor\n     * @param {!(Array.<number>|Uint8Array)} tokens Array of tokens that provide\n     * the stream.\n     */\n    function Stream(tokens) {\n        /** @type {!Array.<number>} */\n        this.tokens = Array.from(tokens);\n        // Reversed as push/pop is more efficient than shift/unshift.\n        this.tokens.reverse();\n    }\n    /**\n     * @return {boolean} True if end-of-stream has been hit.\n     */\n    Stream.prototype.endOfStream = function () {\n        return !this.tokens.length;\n    };\n    /**\n     * When a token is read from a stream, the first token in the\n     * stream must be returned and subsequently removed, and\n     * end-of-stream must be returned otherwise.\n     *\n     * @return {number} Get the next token from the stream, or\n     * end_of_stream.\n     */\n    Stream.prototype.read = function () {\n        if (!this.tokens.length)\n            return end_of_stream;\n        return this.tokens.pop();\n    };\n    /**\n     * When one or more tokens are prepended to a stream, those tokens\n     * must be inserted, in given order, before the first token in the\n     * stream.\n     *\n     * @param {(number|!Array.<number>)} token The token(s) to prepend to the\n     * stream.\n     */\n    Stream.prototype.prepend = function (token) {\n        if (Array.isArray(token)) {\n            var tokens = (token);\n            while (tokens.length)\n                this.tokens.push(tokens.pop());\n        }\n        else {\n            this.tokens.push(token);\n        }\n    };\n    /**\n     * When one or more tokens are pushed to a stream, those tokens\n     * must be inserted, in given order, after the last token in the\n     * stream.\n     *\n     * @param {(number|!Array.<number>)} token The tokens(s) to push to the\n     * stream.\n     */\n    Stream.prototype.push = function (token) {\n        if (Array.isArray(token)) {\n            var tokens = (token);\n            while (tokens.length)\n                this.tokens.unshift(tokens.shift());\n        }\n        else {\n            this.tokens.unshift(token);\n        }\n    };\n    return Stream;\n}());\nexport { Stream };\n//# sourceMappingURL=Stream.js.map", "import { DEFAULT_ENCODING } from \"../encoding/defaultEncoding\";\nimport { decoders } from \"../encoding/encoding-factory\";\nimport { getEncoding } from \"../encoding/encodings\";\nimport { finished } from \"../encoding/finished\";\nimport { end_of_stream } from \"../encoding/terminology\";\nimport { codePointsToString, includes, ToDictionary } from \"../encoding/utilities\";\nimport { Stream } from \"./Stream\";\n/**\n * @constructor\n * @param {string=} label The label of the encoding;\n *     defaults to 'utf-8'.\n * @param {Object=} options\n */\nvar TextDecoder = /** @class */ (function () {\n    function TextDecoder(label, options) {\n        label = label !== undefined ? String(label) : DEFAULT_ENCODING;\n        var optionsMap = ToDictionary(options);\n        // A TextDecoder object has an associated encoding, decoder,\n        // stream, ignore BOM flag (initially unset), BOM seen flag\n        // (initially unset), error mode (initially replacement), and do\n        // not flush flag (initially unset).\n        /** @private */\n        this._encoding = null;\n        /** @private @type {?Decoder} */\n        this._decoder = null;\n        /** @private @type {boolean} */\n        this._ignoreBOM = false;\n        /** @private @type {boolean} */\n        this._BOMseen = false;\n        /** @private @type {string} */\n        this._error_mode = 'replacement';\n        /** @private @type {boolean} */\n        this._do_not_flush = false;\n        // 1. Let encoding be the result of getting an encoding from\n        // label.\n        var encoding = getEncoding(label);\n        // 2. If encoding is failure or replacement, throw a RangeError.\n        if (encoding === null || encoding.name === 'replacement')\n            throw RangeError('Unknown encoding: ' + label);\n        if (!decoders[encoding.name]) {\n            throw Error('Decoder not present.' +\n                ' Did you forget to include encoding-indexes.js first?');\n        }\n        // 3. Let dec be a new TextDecoder object.\n        // const dec = this;\n        // no need to do this as this is a proper class \n        // now and TSC will handle transpilation to older platforms\n        // 4. Set dec's encoding to encoding.\n        this._encoding = encoding;\n        // 5. If options's fatal member is true, set dec's error mode to\n        // fatal.\n        if (Boolean(optionsMap['fatal']))\n            this._error_mode = 'fatal';\n        // 6. If options's ignoreBOM member is true, set dec's ignore BOM\n        // flag.\n        if (Boolean(optionsMap['ignoreBOM']))\n            this._ignoreBOM = true;\n        // For pre-ES5 runtimes:\n        // if (!Object.defineProperty) {\n        //   this.encoding = dec._encoding.name.toLowerCase();\n        //   this.fatal = dec._error_mode === 'fatal';\n        //   this.ignoreBOM = dec._ignoreBOM;\n        // }\n        // 7. Return dec.\n        // return dec;\n    }\n    Object.defineProperty(TextDecoder.prototype, \"encoding\", {\n        // if (Object.defineProperty) {\n        // The encoding attribute's getter must return encoding's name.\n        //   Object.defineProperty(TextDecoder.prototype, 'encoding', {\n        //     /** @this {TextDecoder} */\n        //     get: function () { return this._encoding.name.toLowerCase(); }\n        //   });\n        get: function () {\n            return this._encoding.name.toLowerCase();\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(TextDecoder.prototype, \"fatal\", {\n        // The fatal attribute's getter must return true if error mode\n        // is fatal, and false otherwise.\n        //   Object.defineProperty(TextDecoder.prototype, 'fatal', {\n        //     /** @this {TextDecoder} */\n        //     get: function () { return this._error_mode === 'fatal'; }\n        //   });\n        get: function () {\n            return this._error_mode === 'fatal';\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(TextDecoder.prototype, \"ignoreBOM\", {\n        // The ignoreBOM attribute's getter must return true if ignore\n        // BOM flag is set, and false otherwise.\n        //   Object.defineProperty(TextDecoder.prototype, 'ignoreBOM', {\n        //     /** @this {TextDecoder} */\n        //     get: function () { return this._ignoreBOM; }\n        //   });\n        get: function () {\n            return this._ignoreBOM;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    // }\n    /**\n     * @param {BufferSource=} input The buffer of bytes to decode.\n     * @param {Object=} options\n     * @return {string} The decoded string.\n     */\n    TextDecoder.prototype.decode = function (input, options) {\n        var bytes = getBytesFromInput(input);\n        var optionsMap = ToDictionary(options);\n        // 1. If the do not flush flag is unset, set decoder to a new\n        // encoding's decoder, set stream to a new stream, and unset the\n        // BOM seen flag.\n        if (!this._do_not_flush) {\n            this._decoder = decoders[this._encoding.name]({\n                fatal: this._error_mode === 'fatal'\n            });\n            this._BOMseen = false;\n        }\n        // 2. If options's stream is true, set the do not flush flag, and\n        // unset the do not flush flag otherwise.\n        this._do_not_flush = Boolean(optionsMap['stream']);\n        // 3. If input is given, push a copy of input to stream.\n        // TODO: Align with spec algorithm - maintain stream on instance.\n        var input_stream = new Stream(bytes);\n        // 4. Let output be a new stream.\n        var output = [];\n        /** @type {?(number|!Array.<number>)} */\n        var result;\n        // 5. While true:\n        while (true) {\n            // 1. Let token be the result of reading from stream.\n            var token = input_stream.read();\n            // 2. If token is end-of-stream and the do not flush flag is\n            // set, return output, serialized.\n            // TODO: Align with spec algorithm.\n            if (token === end_of_stream)\n                break;\n            // 3. Otherwise, run these subsubsteps:\n            // 1. Let result be the result of processing token for decoder,\n            // stream, output, and error mode.\n            result = this._decoder.handler(input_stream, token);\n            // 2. If result is finished, return output, serialized.\n            if (result === finished)\n                break;\n            if (result !== null) {\n                if (Array.isArray(result))\n                    output.push.apply(output, /**@type {!Array.<number>}*/ (result));\n                else\n                    output.push(result);\n            }\n            // 3. Otherwise, if result is error, throw a TypeError.\n            // (Thrown in handler)\n            // 4. Otherwise, do nothing.\n        }\n        // TODO: Align with spec algorithm.\n        if (!this._do_not_flush) {\n            do {\n                result = this._decoder.handler(input_stream, input_stream.read());\n                if (result === finished)\n                    break;\n                if (!result)\n                    continue;\n                if (Array.isArray(result))\n                    output.push.apply(output, /**@type {!Array.<number>}*/ (result));\n                else\n                    output.push(result);\n            } while (!input_stream.endOfStream());\n            this._decoder = null;\n        }\n        return this.serializeStream(output);\n    };\n    // A TextDecoder object also has an associated serialize stream\n    // algorithm...\n    /**\n     * @param {!Array.<number>} stream\n     * @return {string}\n     * @this {TextDecoder}\n     */\n    TextDecoder.prototype.serializeStream = function (stream) {\n        // 1. Let token be the result of reading from stream.\n        // (Done in-place on array, rather than as a stream)\n        // 2. If encoding is UTF-8, UTF-16BE, or UTF-16LE, and ignore\n        // BOM flag and BOM seen flag are unset, run these subsubsteps:\n        if (includes(['UTF-8', 'UTF-16LE', 'UTF-16BE'], this._encoding.name) &&\n            !this._ignoreBOM && !this._BOMseen) {\n            if (stream.length > 0 && stream[0] === 0xFEFF) {\n                // 1. If token is U+FEFF, set BOM seen flag.\n                this._BOMseen = true;\n                stream.shift();\n            }\n            else if (stream.length > 0) {\n                // 2. Otherwise, if token is not end-of-stream, set BOM seen\n                // flag and append token to stream.\n                this._BOMseen = true;\n            }\n            else {\n                // 3. Otherwise, if token is not end-of-stream, append token\n                // to output.\n                // (no-op)\n            }\n        }\n        // 4. Otherwise, return output.\n        return codePointsToString(stream);\n    };\n    return TextDecoder;\n}());\nexport { TextDecoder };\nfunction isBufferInstance(input) {\n    try {\n        return input instanceof ArrayBuffer;\n    }\n    catch (e) {\n        console.error(e);\n        return false;\n    }\n}\nfunction getBytesFromInput(input) {\n    if (typeof input !== 'object')\n        return new Uint8Array(0);\n    if (isBufferInstance(input)) {\n        return new Uint8Array(input);\n    }\n    if ('buffer' in input && isBufferInstance(input.buffer)) {\n        return new Uint8Array(input.buffer, input.byteOffset, input.byteLength);\n    }\n    return new Uint8Array(0);\n}\n//# sourceMappingURL=TextDecoder.js.map", "import { DEFAULT_ENCODING } from \"../encoding/defaultEncoding\";\nimport { encoders } from \"../encoding/encoding-factory\";\nimport { getEncoding } from \"../encoding/encodings\";\nimport { finished } from \"../encoding/finished\";\nimport { end_of_stream } from \"../encoding/terminology\";\nimport { stringToCodePoints, ToDictionary } from \"../encoding/utilities\";\nimport { getGlobalScope } from \"../helper/getGlobalScope\";\nimport { Stream } from \"./Stream\";\n/**\n * @constructor\n * @param {string=} label The label of the encoding. NONSTANDARD.\n * @param {Object=} options NONSTANDARD.\n */\nvar TextEncoder = /** @class */ (function () {\n    function TextEncoder(label, options) {\n        var optionsMap = ToDictionary(options);\n        // A TextEncoder object has an associated encoding and encoder.\n        /** @private */\n        this._encoding = null;\n        /** @private @type {?Encoder} */\n        this._encoder = null;\n        // Non-standard\n        /** @private @type {boolean} */\n        this._do_not_flush = false;\n        /** @private @type {string} */\n        this._fatal = Boolean(optionsMap['fatal']) ? 'fatal' : 'replacement';\n        // 1. Let enc be a new TextEncoder object.\n        // const enc = this;\n        // no need to do this as this is a proper class \n        // now and TSC will handle transpilation to older platforms\n        // 2. Set enc's encoding to UTF-8's encoder.\n        if (Boolean(optionsMap['NONSTANDARD_allowLegacyEncoding'])) {\n            // NONSTANDARD behavior.\n            label = !!label ? String(label) : DEFAULT_ENCODING;\n            var encoding = getEncoding(label);\n            if (encoding === null || encoding.name === 'replacement')\n                throw RangeError('Unknown encoding: ' + label);\n            if (!encoders[encoding.name]) {\n                throw Error('Encoder not present.' +\n                    ' Did you forget to include encoding-indexes.js first?');\n            }\n            this._encoding = encoding;\n            // EXPERIMENTAL_CODE\n            // } else if ([\"iso-8859-1\", \"ISO-8859-1\", \"latin-1\", \"latin1\", \"LATIN-1\", \"LATIN1\"].indexOf(label) !== -1) {\n            //   this._encoding = getEncoding('iso-8859-1');\n        }\n        else {\n            // Standard behavior.\n            this._encoding = getEncoding('utf-8');\n            var glo = getGlobalScope() || {};\n            if (label !== undefined && 'console' in glo) {\n                console.warn('TextEncoder constructor called with encoding label, '\n                    + 'which is ignored.');\n            }\n        }\n        // For pre-ES5 runtimes:\n        // if (!Object.defineProperty)\n        // this.encoding = enc._encoding.name.toLowerCase();\n        // 3. Return enc.\n        // return enc;\n    }\n    Object.defineProperty(TextEncoder.prototype, \"encoding\", {\n        // if(Object.defineProperty) {\n        //  // The encoding attribute's getter must return encoding's name.\n        //   Object.defineProperty(TextEncoder.prototype, 'encoding', {\n        //     /** @this {TextEncoder} */\n        //     get: function () { return this._encoding.name.toLowerCase(); }\n        //   });\n        // }\n        get: function () {\n            return this._encoding.name.toLowerCase();\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * @param {string=} opt_string The string to encode.\n     * @param {Object=} options\n     * @return {!Uint8Array} Encoded bytes, as a Uint8Array.\n     */\n    TextEncoder.prototype.encode = function (opt_string, options) {\n        opt_string = opt_string === undefined ? '' : String(opt_string);\n        var optionsMap = ToDictionary(options);\n        // NOTE: This option is nonstandard. None of the encodings\n        // permitted for encoding (i.e. UTF-8, UTF-16) are stateful when\n        // the input is a USVString so streaming is not necessary.\n        if (!this._do_not_flush)\n            this._encoder = encoders[this._encoding.name]({\n                fatal: this._fatal === 'fatal'\n            });\n        this._do_not_flush = Boolean(optionsMap['stream']);\n        // 1. Convert input to a stream.\n        var input = new Stream(stringToCodePoints(opt_string));\n        // 2. Let output be a new stream\n        var output = [];\n        /** @type {?(number|!Array.<number>)} */\n        var result;\n        // 3. While true, run these substeps:\n        while (true) {\n            // 1. Let token be the result of reading from input.\n            var token = input.read();\n            if (token === end_of_stream)\n                break;\n            // 2. Let result be the result of processing token for encoder,\n            // input, output.\n            result = this._encoder.handler(input, token);\n            if (result === finished)\n                break;\n            if (Array.isArray(result))\n                output.push.apply(output, /**@type {!Array.<number>}*/ (result));\n            else\n                output.push(result);\n        }\n        // TODO: Align with spec algorithm.\n        if (!this._do_not_flush) {\n            while (true) {\n                result = this._encoder.handler(input, input.read());\n                if (result === finished)\n                    break;\n                if (Array.isArray(result))\n                    output.push.apply(output, /**@type {!Array.<number>}*/ (result));\n                else\n                    output.push(result);\n            }\n            this._encoder = null;\n        }\n        // 3. If result is finished, convert output into a byte sequence,\n        // and then return a Uint8Array object wrapping an ArrayBuffer\n        // containing output.\n        return new Uint8Array(output);\n    };\n    return TextEncoder;\n}());\nexport { TextEncoder };\n//# sourceMappingURL=TextEncoder.js.map", "import { TextDecoder } from './common/TextDecoder';\nimport { TextEncoder } from './common/TextEncoder';\n// Polyfills browser\nif (typeof window !== 'undefined') {\n    var checkUndefined = function (key) { return !(key in window)\n        || typeof window[key] === 'undefined'\n        || window[key] === null; };\n    if (checkUndefined('TextDecoder'))\n        window['TextDecoder'] = TextDecoder;\n    if (checkUndefined('TextEncoder'))\n        window['TextEncoder'] = TextEncoder;\n}\nexport { TextDecoder, TextEncoder };\n//# sourceMappingURL=encoding.js.map"], "names": ["states"], "mappings": ";;;;;;IAAA,cAAqB,IAAI,gBAAgB,GAAG,OAAO;;ICAnD;IACA;IACA;IACA;IACA;IACO,SAAS,YAAY,CAAC,KAAK,EAAE,cAAc,EAAE;IACpD,IAAI,IAAI,cAAc,KAAK,KAAK,CAAC,EAAE,EAAE,cAAc,GAAG,SAAS,CAAC,EAAE;IAClE,IAAI,IAAI,KAAK;IACb,QAAQ,MAAM,SAAS,CAAC,eAAe,CAAC,CAAC;IACzC,IAAI,OAAO,cAAc,IAAI,MAAM,CAAC;IACpC,CAAC;IACD;IACA;IACA;IACA;IACO,SAAS,YAAY,CAAC,UAAU,EAAE;IACzC,IAAI,MAAM,SAAS,CAAC,iBAAiB,GAAG,UAAU,GAAG,wBAAwB,CAAC,CAAC;IAC/E,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,WAAW,CAAC,KAAK,EAAE;IACnC;IACA,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IACtD;IACA;IACA;IACA,IAAI,IAAI,QAAQ,IAAI,iBAAiB,EAAE;IACvC,QAAQ,OAAO,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC3C,KAAK;IACL,IAAI,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,SAAS,GAAG;IAChB,IAAI;IACJ,QAAQ,SAAS,EAAE;IACnB,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,MAAM,CAAC;IAC9D,gBAAgB,IAAI,EAAE,OAAO;IAC7B,aAAa;IACb,SAAS;IACT,QAAQ,OAAO,EAAE,cAAc;IAC/B,KAAK;IACL,IAAI;IACJ,QAAQ,SAAS,EAAE;IACnB,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC;IAC9D,gBAAgB,IAAI,EAAE,QAAQ;IAC9B,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,aAAa;IACjC,oBAAoB,YAAY;IAChC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,oBAAoB,IAAI;IACxB,oBAAoB,QAAQ;IAC5B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,YAAY;IAClC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,aAAa;IACjC,oBAAoB,YAAY;IAChC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,oBAAoB,IAAI;IACxB,oBAAoB,QAAQ;IAC5B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,YAAY;IAClC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,aAAa;IACjC,oBAAoB,YAAY;IAChC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,oBAAoB,IAAI;IACxB,oBAAoB,QAAQ;IAC5B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,YAAY;IAClC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,oBAAoB;IACxC,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,YAAY;IAClC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,QAAQ;IAC5B,oBAAoB,UAAU;IAC9B,oBAAoB,aAAa;IACjC,oBAAoB,aAAa;IACjC,oBAAoB,kBAAkB;IACtC,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,cAAc;IAClC,oBAAoB,cAAc;IAClC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,YAAY;IAClC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,iBAAiB;IACrC,oBAAoB,UAAU;IAC9B,oBAAoB,UAAU;IAC9B,oBAAoB,OAAO;IAC3B,oBAAoB,QAAQ;IAC5B,oBAAoB,YAAY;IAChC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,oBAAoB,cAAc;IAClC,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,YAAY;IAClC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,aAAa;IACjC,oBAAoB,kBAAkB;IACtC,oBAAoB,QAAQ;IAC5B,oBAAoB,YAAY;IAChC,oBAAoB,cAAc;IAClC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,oBAAoB,QAAQ;IAC5B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,YAAY;IAClC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,SAAS,CAAC;IAClE,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,aAAa;IACjC,oBAAoB,aAAa;IACjC,oBAAoB,YAAY;IAChC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,IAAI;IACxB,oBAAoB,QAAQ;IAC5B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,aAAa;IACnC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,WAAW,CAAC;IAClE,gBAAgB,IAAI,EAAE,aAAa;IACnC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,WAAW,CAAC;IAClE,gBAAgB,IAAI,EAAE,aAAa;IACnC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,aAAa;IACjC,oBAAoB,aAAa;IACjC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,aAAa;IACjC,oBAAoB,IAAI;IACxB,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,aAAa;IACnC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,aAAa,CAAC;IACvC,gBAAgB,IAAI,EAAE,aAAa;IACnC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACtE,gBAAgB,IAAI,EAAE,QAAQ;IAC9B,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;IAC7C,gBAAgB,IAAI,EAAE,QAAQ;IAC9B,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,CAAC;IAC1E,gBAAgB,IAAI,EAAE,WAAW;IACjC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,SAAS;IAC7B,oBAAoB,aAAa;IACjC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,SAAS;IAC7B,oBAAoB,aAAa;IACjC,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,aAAa;IACnC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,gBAAgB;IACpC,oBAAoB,QAAQ;IAC5B,oBAAoB,OAAO;IAC3B,oBAAoB,QAAQ;IAC5B,oBAAoB,YAAY;IAChC,oBAAoB,cAAc;IAClC,oBAAoB,UAAU;IAC9B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,OAAO;IAC3B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,oBAAoB,IAAI;IACxB,oBAAoB,QAAQ;IAC5B,oBAAoB,aAAa;IACjC,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,YAAY;IAClC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,QAAQ;IAC5B,oBAAoB,aAAa;IACjC,oBAAoB,YAAY;IAChC,oBAAoB,YAAY;IAChC,oBAAoB,WAAW;IAC/B,oBAAoB,UAAU;IAC9B,oBAAoB,YAAY;IAChC,oBAAoB,iBAAiB;IACrC,oBAAoB,IAAI;IACxB,oBAAoB,QAAQ;IAC5B,oBAAoB,cAAc;IAClC,oBAAoB,UAAU;IAC9B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC;IAC9D,gBAAgB,IAAI,EAAE,cAAc;IACpC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;IAC7D,gBAAgB,IAAI,EAAE,gBAAgB;IACtC,aAAa;IACb,SAAS;IACT,QAAQ,OAAO,EAAE,8BAA8B;IAC/C,KAAK;IACL,IAAI;IACJ,QAAQ,SAAS,EAAE;IACnB,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,SAAS;IAC7B,oBAAoB,UAAU;IAC9B,oBAAoB,iBAAiB;IACrC,oBAAoB,QAAQ;IAC5B,oBAAoB,SAAS;IAC7B,oBAAoB,YAAY;IAChC,oBAAoB,KAAK;IACzB,oBAAoB,WAAW;IAC/B,oBAAoB,OAAO;IAC3B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,KAAK;IAC3B,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,SAAS,CAAC;IACnC,gBAAgB,IAAI,EAAE,SAAS;IAC/B,aAAa;IACb,SAAS;IACT,QAAQ,OAAO,EAAE,kDAAkD;IACnE,KAAK;IACL,IAAI;IACJ,QAAQ,SAAS,EAAE;IACnB,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC;IAC/E,gBAAgB,IAAI,EAAE,MAAM;IAC5B,aAAa;IACb,SAAS;IACT,QAAQ,OAAO,EAAE,mDAAmD;IACpE,KAAK;IACL,IAAI;IACJ,QAAQ,SAAS,EAAE;IACnB,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,qBAAqB,EAAE,QAAQ,EAAE,UAAU,CAAC;IACrE,gBAAgB,IAAI,EAAE,QAAQ;IAC9B,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;IACtD,gBAAgB,IAAI,EAAE,aAAa;IACnC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,YAAY;IAChC,oBAAoB,OAAO;IAC3B,oBAAoB,UAAU;IAC9B,oBAAoB,WAAW;IAC/B,oBAAoB,WAAW;IAC/B,oBAAoB,MAAM;IAC1B,oBAAoB,aAAa;IACjC,oBAAoB,QAAQ;IAC5B,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,WAAW;IACjC,aAAa;IACb,SAAS;IACT,QAAQ,OAAO,EAAE,sCAAsC;IACvD,KAAK;IACL,IAAI;IACJ,QAAQ,SAAS,EAAE;IACnB,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,SAAS;IAC7B,oBAAoB,eAAe;IACnC,oBAAoB,QAAQ;IAC5B,oBAAoB,YAAY;IAChC,oBAAoB,QAAQ;IAC5B,oBAAoB,gBAAgB;IACpC,oBAAoB,gBAAgB;IACpC,oBAAoB,SAAS;IAC7B,oBAAoB,UAAU;IAC9B,oBAAoB,aAAa;IACjC,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,QAAQ;IAC9B,aAAa;IACb,SAAS;IACT,QAAQ,OAAO,EAAE,oCAAoC;IACrD,KAAK;IACL,IAAI;IACJ,QAAQ,SAAS,EAAE;IACnB,YAAY;IACZ,gBAAgB,MAAM,EAAE;IACxB,oBAAoB,aAAa;IACjC,oBAAoB,YAAY;IAChC,oBAAoB,aAAa;IACjC,oBAAoB,iBAAiB;IACrC,oBAAoB,aAAa;IACjC,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,aAAa;IACnC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,UAAU,CAAC;IACpC,gBAAgB,IAAI,EAAE,UAAU;IAChC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;IAC9C,gBAAgB,IAAI,EAAE,UAAU;IAChC,aAAa;IACb,YAAY;IACZ,gBAAgB,MAAM,EAAE,CAAC,gBAAgB,CAAC;IAC1C,gBAAgB,IAAI,EAAE,gBAAgB;IACtC,aAAa;IACb,SAAS;IACT,QAAQ,OAAO,EAAE,gCAAgC;IACjD,KAAK;IACL,CAAC,CAAC;IACF;IACA;IACA,IAAI,iBAAiB,GAAG,EAAE,CAAC;IAC3B,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;IACtC,IAAI,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;IACnD,QAAQ,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;IACjD,YAAY,iBAAiB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;IAChD,SAAS,CAAC,CAAC;IACX,KAAK,CAAC,CAAC;IACP,CAAC,CAAC;;ICvaF;IACA;IACO,IAAI,QAAQ,GAAG,CAAC,CAAC;;ICFjB,SAAS,WAAW,CAAC,MAAM,EAAE;IACpC,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;IACrD;;ICFA;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;IACrC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;IAChC,CAAC;IACD;IACA;IACA;IACA;IACA;IACO,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE;IACtC,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACtC,CAAC;IACD;IACA;IACA;IACA;IACO,SAAS,YAAY,CAAC,CAAC,EAAE;IAChC,IAAI,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI;IACrC,QAAQ,OAAO,EAAE,CAAC;IAClB,IAAI,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC;IACvB,QAAQ,OAAO,CAAC,CAAC;IACjB,IAAI,MAAM,SAAS,CAAC,0CAA0C,CAAC,CAAC;IAChE,CAAC;IACD;IACA;IACA;IACA;IACO,SAAS,kBAAkB,CAAC,MAAM,EAAE;IAC3C;IACA;IACA,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAC3B;IACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACrB;IACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;IACd;IACA,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf;IACA,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;IAClB;IACA,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChC;IACA;IACA,QAAQ,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE;IACtC;IACA,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACtB,SAAS;IACT;IACA,aAAa,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE;IAC7C;IACA,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3B,SAAS;IACT;IACA,aAAa,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE;IAC7C;IACA;IACA,YAAY,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;IAC7B,gBAAgB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/B,aAAa;IACb;IACA,iBAAiB;IACjB;IACA,gBAAgB,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C;IACA,gBAAgB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE;IAChD;IACA,oBAAoB,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtC;IACA,oBAAoB,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtC;IACA;IACA,oBAAoB,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD;IACA,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC3B,iBAAiB;IACjB;IACA;IACA,qBAAqB;IACrB,oBAAoB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnC,iBAAiB;IACjB,aAAa;IACb,SAAS;IACT;IACA,QAAQ,CAAC,IAAI,CAAC,CAAC;IACf,KAAK;IACL;IACA,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;IACD;IACA;IACA;IACA;IACO,SAAS,kBAAkB,CAAC,WAAW,EAAE;IAChD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;IACjD,QAAQ,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAChC,QAAQ,IAAI,EAAE,IAAI,MAAM,EAAE;IAC1B,YAAY,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IACzC,SAAS;IACT,aAAa;IACb,YAAY,EAAE,IAAI,OAAO,CAAC;IAC1B,YAAY,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,IAAI,MAAM,CAAC,CAAC;IACjF,SAAS;IACT,KAAK;IACL,IAAI,OAAO,CAAC,CAAC;IACb;;IC9GO,SAAS,cAAc,GAAG;IACjC,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW;IACrC,QAAQ,OAAO,MAAM,CAAC;IACtB,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW;IACrC,QAAQ,OAAO,MAAM,CAAC;IACtB,IAAI,IAAI,OAAO,IAAI,KAAK,WAAW;IACnC,QAAQ,OAAO,IAAI,CAAC;IACpB,IAAI,OAAO;IACX;;ICPA,IAAI,gBAAgB,CAAC;IACrB,SAAS,uBAAuB,GAAG;IACnC,IAAI,IAAI,OAAO,mBAAmB,KAAK,WAAW;IAClD,QAAQ,OAAO,mBAAmB,CAAC,eAAe,CAAC;IACnD,IAAI,IAAI,GAAG,GAAG,cAAc,EAAE,CAAC;IAC/B,IAAI,IAAI,CAAC,GAAG;IACZ,QAAQ,OAAO,IAAI,CAAC;IACpB,IAAI,IAAI,qBAAqB,IAAI,GAAG;IACpC,QAAQ,OAAO,MAAM,CAAC,qBAAqB,CAAC,CAAC,iBAAiB,CAAC,CAAC;IAChE,IAAI,IAAI,kBAAkB,IAAI,GAAG;IACjC,QAAQ,OAAO,MAAM,CAAC,iBAAiB,CAAC,CAAC;IACzC,IAAI,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,SAAS,kBAAkB,GAAG;IACrC,IAAI,IAAI,gBAAgB,EAAE;IAC1B,QAAQ,OAAO,gBAAgB,CAAC;IAChC,KAAK;IACL,IAAI,IAAI,OAAO,GAAG,uBAAuB,EAAE,CAAC;IAC5C,IAAI,IAAI,CAAC,OAAO,EAAE;IAClB,QAAQ,OAAO,IAAI,CAAC;IACpB,KAAK;IACL,IAAI,gBAAgB,GAAG,OAAO,CAAC;IAC/B,IAAI,OAAO,OAAO,CAAC;IACnB;;ICrBA;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE;IAClD,IAAI,IAAI,CAAC,KAAK;IACd,QAAQ,OAAO,IAAI,CAAC;IACpB,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IAClC,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,eAAe,CAAC,UAAU,EAAE,KAAK,EAAE;IACnD,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC5C,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC;IAC3C,CAAC;IACD;IACA;IACA;IACA;IACO,SAAS,KAAK,CAAC,IAAI,EAAE;IAC5B,IAAI,IAAI,eAAe,GAAG,kBAAkB,EAAE,CAAC;IAC/C,IAAI,IAAI,CAAC,eAAe,EAAE;IAC1B,QAAQ,MAAM,KAAK,CAAC,kBAAkB;IACtC,YAAY,uDAAuD,CAAC,CAAC;IACrE,KAAK;IACL,IAAI,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IACD;IACA;IACA;IACA;IACA;IACO,SAAS,8BAA8B,CAAC,OAAO,EAAE;IACxD;IACA;IACA,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,MAAM,MAAM,OAAO,GAAG,OAAO,CAAC;IACpE,QAAQ,OAAO,IAAI,CAAC;IACpB;IACA,IAAI,IAAI,OAAO,KAAK,IAAI;IACxB,QAAQ,OAAO,MAAM,CAAC;IACtB;IACA;IACA;IACA,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;IACnB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC;IAC9B,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACtC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;IACzC;IACA,QAAQ,IAAI,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE;IACjC,YAAY,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B,YAAY,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACzC,SAAS;IACT,aAAa;IACb,YAAY,MAAM;IAClB,SAAS;IACT,KAAK;IACL;IACA;IACA,IAAI,OAAO,iBAAiB,GAAG,OAAO,GAAG,MAAM,CAAC;IAChD,CAAC;IACD;IACA;IACA;IACA;IACA;IACO,SAAS,4BAA4B,CAAC,UAAU,EAAE;IACzD;IACA,IAAI,IAAI,UAAU,KAAK,MAAM;IAC7B,QAAQ,OAAO,IAAI,CAAC;IACpB;IACA;IACA;IACA,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;IACnB,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC;IAC3B,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACtC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;IACzC,QAAQ,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5B;IACA,QAAQ,IAAI,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IACxC,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;IACpC,YAAY,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B,YAAY,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACtC,SAAS;IACT,aAAa;IACb,YAAY,MAAM;IAClB,SAAS;IACT,KAAK;IACL;IACA;IACA,IAAI,OAAO,cAAc,GAAG,UAAU,GAAG,MAAM,CAAC;IAChD,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,uBAAuB,CAAC,UAAU,EAAE;IACpD;IACA;IACA,IAAI,eAAe,GAAG,eAAe;IACrC,QAAQ,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,UAAU,EAAE,OAAO,EAAE;IAC5D,YAAY,OAAO,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,UAAU,CAAC;IACpE,SAAS,CAAC,CAAC;IACX,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC;IACjC;IACA,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IACD,IAAI,eAAe,CAAC;IACpB;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,mBAAmB,CAAC,UAAU,EAAE;IAChD;IACA,IAAI,mBAAmB,GAAG,mBAAmB;IAC7C,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,UAAU,UAAU,EAAE,OAAO,EAAE;IACzD,YAAY,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,UAAU,CAAC;IACvE,SAAS,CAAC,CAAC;IACX,IAAI,IAAI,MAAM,GAAG,mBAAmB,CAAC;IACrC;IACA;IACA;IACA,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;IACtD,QAAQ,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;IACtD,QAAQ,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM,EAAE;IACxD,QAAQ,OAAO,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAC9C,KAAK;IACL;IACA,IAAI,OAAO,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IACD,IAAI,mBAAmB;;IC/IvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAAS,WAAW,CAAC,CAAC,EAAE;IAC/B,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IAClC,CAAC;IACD;IACA;IACA;IACA;IACO,IAAI,gBAAgB,GAAG,WAAW,CAAC;IAC1C;IACA;IACA;IACA;IACA,IAAW,IAAI,aAAa,GAAG,CAAC,CAAC;;ICnBjC;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,kBAAkB,YAAY;IAC7C,IAAI,SAAS,WAAW,CAAC,OAAO,EAAE;IAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA,8BAA8B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACpD,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IAC5D;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;IAC/D,YAAY,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAClC,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI;IAC7D,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;IACrC,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;IACtC,YAAY,IAAI,OAAO,GAAG,IAAI,CAAC;IAC/B,YAAY,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAClC;IACA;IACA,YAAY,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACnD;IACA;IACA;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACtE,gBAAgB,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC;IAChE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,YAAY,QAAQ,OAAO;IAC3B,gBAAgB,KAAK,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACnD,gBAAgB,KAAK,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACnD,gBAAgB,KAAK,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACnD,gBAAgB,KAAK,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACnD,aAAa;IACb;IACA;IACA,YAAY,IAAI,UAAU,GAAG,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI;IACtD,gBAAgB,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1D;IACA;IACA,YAAY,IAAI,UAAU,KAAK,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;IACxD,gBAAgB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC;IACA,YAAY,IAAI,UAAU,KAAK,IAAI;IACnC,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD;IACA,YAAY,OAAO,UAAU,CAAC;IAC9B,SAAS;IACT;IACA;IACA,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC;IAC7B,YAAY,OAAO,IAAI,CAAC;IACxB;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IACvC,YAAY,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAClC,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA,QAAQ,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,KAAK,CAAC;IACN,IAAI,OAAO,WAAW,CAAC;IACvB,CAAC,EAAE,CAAC;;ICxFJ;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,kBAAkB,YAAY;IAC7C,IAAI,SAAS,WAAW,CAAC,OAAO,EAAE;IAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IAClE;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA,QAAQ,IAAI,OAAO,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IACtD;IACA,QAAQ,IAAI,OAAO,KAAK,IAAI;IAC5B,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAC5C;IACA,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;IACpD;IACA,QAAQ,IAAI,IAAI,GAAG,IAAI;IACvB,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAC5C;IACA,QAAQ,IAAI,KAAK,GAAG,OAAO,GAAG,GAAG,CAAC;IAClC;IACA;IACA,QAAQ,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IAChD;IACA,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;IACtC,KAAK,CAAC;IACN,IAAI,OAAO,WAAW,CAAC;IACvB,CAAC,EAAE,CAAC;;ICxCJ;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,kBAAkB,YAAY;IAC9C,IAAI,SAAS,YAAY,CAAC,OAAO,EAAE;IACnC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA;IACA,+BAA+B,IAAI,CAAC,kBAAkB,GAAG,KAAK;IAC9D,kCAAkC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzD,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IAC7D;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;IAChE,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI;IAC9D,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IACnE,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC,YAAY,OAAO,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IACxC,SAAS;IACT;IACA;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IACnE,YAAY,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IAC3C,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;IACtC,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IACvC,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC;IACA,YAAY,IAAI,UAAU,GAAG,IAAI,CAAC;IAClC;IACA;IACA;IACA;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IACxE,gBAAgB,UAAU,GAAG,iBAAiB,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC;IAC5I,aAAa;IACb;IACA,YAAY,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAC5C;IACA;IACA,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1C,gBAAgB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC;IACA,YAAY,IAAI,UAAU,KAAK,IAAI;IACnC,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD;IACA,YAAY,OAAO,UAAU,CAAC;IAC9B,SAAS;IACT;IACA;IACA,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC;IAC7B,YAAY,OAAO,IAAI,CAAC;IACxB;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IACzE,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA,QAAQ,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,KAAK,CAAC;IACN,IAAI,OAAO,YAAY,CAAC;IACxB,CAAC,EAAE,CAAC;;ICtFJ;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,kBAAkB,YAAY;IAC9C,IAAI,SAAS,YAAY,CAAC,OAAO,EAAE;IACnC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IACnE;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA,QAAQ,IAAI,UAAU,KAAK,MAAM;IACjC,YAAY,OAAO,IAAI,CAAC;IACxB;IACA,QAAQ,IAAI,UAAU,KAAK,MAAM;IACjC,YAAY,OAAO,IAAI,CAAC;IACxB;IACA;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;IAC/C,YAAY,OAAO,CAAC,IAAI,EAAE,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;IACtD;IACA,QAAQ,IAAI,UAAU,KAAK,MAAM;IACjC,YAAY,UAAU,GAAG,MAAM,CAAC;IAChC;IACA;IACA,QAAQ,IAAI,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IACpE;IACA,QAAQ,IAAI,OAAO,KAAK,IAAI;IAC5B,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAC5C;IACA,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;IACnD;IACA,QAAQ,IAAI,KAAK,GAAG,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC;IACxC;IACA,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC7B,KAAK,CAAC;IACN,IAAI,OAAO,YAAY,CAAC;IACxB,CAAC,EAAE,CAAC;;IClDJ;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,kBAAkB,YAAY;IAC9C,IAAI,SAAS,YAAY,CAAC,OAAO,EAAE;IACnC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA,8BAA8B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACrD,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IAC7D;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;IAC7D,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC;IAC3D,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;IACtC,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IACvC,YAAY,IAAI,OAAO,GAAG,IAAI,CAAC;IAC/B,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC;IACA;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,gBAAgB,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;IAC9D;IACA;IACA,YAAY,IAAI,UAAU,GAAG,CAAC,OAAO,KAAK,IAAI;IAC9C,kBAAkB,IAAI,GAAG,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrE;IACA;IACA,YAAY,IAAI,OAAO,KAAK,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;IACrD,gBAAgB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC;IACA,YAAY,IAAI,UAAU,KAAK,IAAI;IACnC,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD;IACA,YAAY,OAAO,UAAU,CAAC;IAC9B,SAAS;IACT;IACA;IACA,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC;IAC7B,YAAY,OAAO,IAAI,CAAC;IACxB;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IACvC,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA,QAAQ,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,KAAK,CAAC;IACN,IAAI,OAAO,YAAY,CAAC;IACxB,CAAC,EAAE,CAAC;;ICrEJ;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,kBAAkB,YAAY;IAC9C,IAAI,SAAS,YAAY,CAAC,OAAO,EAAE;IACnC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IACnE;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA;IACA,QAAQ,IAAI,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnE;IACA,QAAQ,IAAI,OAAO,KAAK,IAAI;IAC5B,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAC5C;IACA,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;IACpD;IACA,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,IAAI,IAAI,CAAC;IAC3C;IACA,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC7B,KAAK,CAAC;IACN,IAAI,OAAO,YAAY,CAAC;IACxB,CAAC,EAAE,CAAC;;ICnCJ;IACA;IACA;IACA;IACA;IACA,IAAI,cAAc,kBAAkB,YAAY;IAChD,IAAI,SAAS,cAAc,CAAC,OAAO,EAAE;IACrC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA;IACA,8BAA8B,IAAI,CAAC,aAAa,GAAG,IAAI;IACvD,kCAAkC,IAAI,CAAC,cAAc,GAAG,IAAI;IAC5D,kCAAkC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC5D,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,cAAc,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IAC/D;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI;IACjE,YAAY,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;IACzE,YAAY,OAAO,QAAQ,CAAC;IAC5B,SAAS;IACT;IACA;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa;IAClC,aAAa,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI;IACxE,gBAAgB,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE;IAC9C,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IACtC,YAAY,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IACvC,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IACtC,YAAY,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,SAAS;IACT,QAAQ,IAAI,UAAU,CAAC;IACvB;IACA,QAAQ,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;IACzC;IACA,YAAY,UAAU,GAAG,IAAI,CAAC;IAC9B;IACA;IACA;IACA;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAC3C,gBAAgB,UAAU,GAAG,8BAA8B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,GAAG;IAClI,oBAAoB,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACnE,aAAa;IACb;IACA;IACA,YAAY,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IACzE;IACA;IACA,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IACtC,YAAY,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IACvC,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IACtC;IACA;IACA,YAAY,IAAI,UAAU,KAAK,IAAI,EAAE;IACrC,gBAAgB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACvC,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,aAAa;IACb;IACA,YAAY,OAAO,UAAU,CAAC;IAC9B,SAAS;IACT;IACA,QAAQ,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;IAC1C;IACA;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAC3C,gBAAgB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC1C,gBAAgB,OAAO,IAAI,CAAC;IAC5B,aAAa;IACb;IACA;IACA,YAAY,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;IACxD,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IACtC,YAAY,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IACvC,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA,QAAQ,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;IACzC;IACA;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAC3C,gBAAgB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC3C,gBAAgB,OAAO,IAAI,CAAC;IAC5B,aAAa;IACb;IACA;IACA,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;IAC1C,YAAY,IAAI,OAAO,GAAG,IAAI,CAAC;IAC/B,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IACtC;IACA;IACA,YAAY,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACnD;IACA;IACA;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACtE,gBAAgB,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC;IAChE;IACA;IACA,YAAY,UAAU,GAAG,OAAO,KAAK,IAAI,GAAG,IAAI;IAChD,gBAAgB,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7D;IACA;IACA,YAAY,IAAI,UAAU,KAAK,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;IACxD,gBAAgB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC;IACA,YAAY,IAAI,UAAU,KAAK,IAAI;IACnC,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD;IACA,YAAY,OAAO,UAAU,CAAC;IAC9B,SAAS;IACT;IACA;IACA,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC;IAC7B,YAAY,OAAO,IAAI,CAAC;IACxB;IACA,QAAQ,IAAI,IAAI,KAAK,IAAI;IACzB,YAAY,OAAO,MAAM,CAAC;IAC1B;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IACvC,YAAY,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IACtC,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA,QAAQ,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,KAAK,CAAC;IACN,IAAI,OAAO,cAAc,CAAC;IAC1B,CAAC,EAAE,CAAC;;ICzIJ;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,cAAc,kBAAkB,YAAY;IAChD,IAAI,SAAS,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE;IAC/C,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAG,SAAS,CAAC,EAAE;IAC1D,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,cAAc,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IACrE;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA,QAAQ,IAAI,UAAU,KAAK,MAAM;IACjC,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAC5C;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,KAAK,MAAM;IAClD,YAAY,OAAO,IAAI,CAAC;IACxB;IACA;IACA,QAAQ,IAAI,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IACpE;IACA,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE;IAC9B;IACA,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;IACxD;IACA,YAAY,IAAI,KAAK,GAAG,OAAO,GAAG,GAAG,CAAC;IACtC;IACA,YAAY,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACpD;IACA,YAAY,OAAO,CAAC,IAAI,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;IAC1C,SAAS;IACT;IACA,QAAQ,IAAI,IAAI,CAAC,QAAQ;IACzB,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAC5C;IACA;IACA,QAAQ,OAAO,GAAG,4BAA4B,CAAC,UAAU,CAAC,CAAC;IAC3D;IACA,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;IACxD;IACA,QAAQ,OAAO,GAAG,OAAO,GAAG,KAAK,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;IAClD;IACA,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;IACnD;IACA,QAAQ,OAAO,GAAG,OAAO,GAAG,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;IAC7C;IACA,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;IAC7C;IACA,QAAQ,IAAI,KAAK,GAAG,OAAO,GAAG,KAAK,GAAG,EAAE,CAAC;IACzC;IACA;IACA,QAAQ,OAAO,CAAC,KAAK,GAAG,IAAI;IAC5B,YAAY,KAAK,GAAG,IAAI;IACxB,YAAY,KAAK,GAAG,IAAI;IACxB,YAAY,KAAK,GAAG,IAAI,CAAC,CAAC;IAC1B,KAAK,CAAC;IACN,IAAI,OAAO,cAAc,CAAC;IAC1B,CAAC,EAAE,CAAC;;ICxEJ,IAAI,MAAM,CAAC;IACX,CAAC,UAAU,MAAM,EAAE;IACnB,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IAC1C,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IAC1C,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC;IAChD,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC;IAChD,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC;IAClD,IAAI,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;IACtD,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;IAC5C,CAAC,EAAE,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5B,IAAI,gBAAgB,kBAAkB,YAAY;IAClD;IACA;IACA;IACA;IACA;IACA,IAAI,SAAS,gBAAgB,CAAC,OAAO,EAAE;IACvC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA;IACA;IACA;IACA,8BAA8B,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,KAAK;IACzE,kCAAkC,IAAI,CAAC,8BAA8B,GAAG,MAAM,CAAC,KAAK;IACpF,kCAAkC,IAAI,CAAC,cAAc,GAAG,IAAI;IAC5D,mCAAmC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACtE,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,gBAAgB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IACjE;IACA,QAAQ,QAAQ,IAAI,CAAC,uBAAuB;IAC5C,YAAY,QAAQ;IACpB,YAAY,KAAK,MAAM,CAAC,KAAK;IAC7B;IACA;IACA;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,EAAE;IACnC;IACA;IACA,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,WAAW,CAAC;IACtE,oBAAoB,OAAO,IAAI,CAAC;IAChC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;IAC9D,uBAAuB,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;IACvD;IACA;IACA,oBAAoB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvD,oBAAoB,OAAO,IAAI,CAAC;IAChC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,IAAI,KAAK,aAAa,EAAE;IAC5C;IACA,oBAAoB,OAAO,QAAQ,CAAC;IACpC,iBAAiB;IACjB;IACA;IACA,gBAAgB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACnD,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,YAAY,KAAK,MAAM,CAAC,KAAK;IAC7B;IACA;IACA;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,EAAE;IACnC;IACA;IACA,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,WAAW,CAAC;IACtE,oBAAoB,OAAO,IAAI,CAAC;IAChC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,EAAE;IACnC;IACA;IACA,oBAAoB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvD,oBAAoB,OAAO,MAAM,CAAC;IAClC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,EAAE;IACnC;IACA;IACA,oBAAoB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvD,oBAAoB,OAAO,MAAM,CAAC;IAClC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;IAC/E,uBAAuB,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;IACxE;IACA;IACA,oBAAoB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvD,oBAAoB,OAAO,IAAI,CAAC;IAChC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,IAAI,KAAK,aAAa,EAAE;IAC5C;IACA,oBAAoB,OAAO,QAAQ,CAAC;IACpC,iBAAiB;IACjB;IACA;IACA,gBAAgB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACnD,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,YAAY,KAAK,MAAM,CAAC,QAAQ;IAChC;IACA;IACA;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,EAAE;IACnC;IACA;IACA,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,WAAW,CAAC;IACtE,oBAAoB,OAAO,IAAI,CAAC;IAChC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAC/C;IACA;IACA,oBAAoB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvD,oBAAoB,OAAO,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IAChD,iBAAiB;IACjB;IACA,gBAAgB,IAAI,IAAI,KAAK,aAAa,EAAE;IAC5C;IACA,oBAAoB,OAAO,QAAQ,CAAC;IACpC,iBAAiB;IACjB;IACA;IACA,gBAAgB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACnD,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,YAAY,KAAK,MAAM,CAAC,QAAQ;IAChC;IACA;IACA;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,EAAE;IACnC;IACA;IACA,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,WAAW,CAAC;IACtE,oBAAoB,OAAO,IAAI,CAAC;IAChC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAC/C;IACA;IACA;IACA,oBAAoB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvD,oBAAoB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/C,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,SAAS,CAAC;IACpE,oBAAoB,OAAO,IAAI,CAAC;IAChC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,IAAI,KAAK,aAAa,EAAE;IAC5C;IACA,oBAAoB,OAAO,QAAQ,CAAC;IACpC,iBAAiB;IACjB;IACA;IACA,gBAAgB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACnD,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,YAAY,KAAK,MAAM,CAAC,SAAS;IACjC;IACA;IACA;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,EAAE;IACnC;IACA;IACA,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,WAAW,CAAC;IACtE,oBAAoB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpD,iBAAiB;IACjB;IACA,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAC/C;IACA,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC;IACnE;IACA,oBAAoB,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;IAClF;IACA;IACA,oBAAoB,IAAI,UAAU,GAAG,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IAClF;IACA,oBAAoB,IAAI,UAAU,KAAK,IAAI;IAC3C,wBAAwB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxD;IACA,oBAAoB,OAAO,UAAU,CAAC;IACtC,iBAAiB;IACjB;IACA,gBAAgB,IAAI,IAAI,KAAK,aAAa,EAAE;IAC5C;IACA;IACA,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC;IACnE,oBAAoB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACzC,oBAAoB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpD,iBAAiB;IACjB;IACA;IACA;IACA,gBAAgB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC;IAC/D,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,YAAY,KAAK,MAAM,CAAC,WAAW;IACnC;IACA;IACA;IACA;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;IACpD,oBAAoB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/C,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAAC;IACjE,oBAAoB,OAAO,IAAI,CAAC;IAChC,iBAAiB;IACjB;IACA,gBAAgB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC;IACA;IACA;IACA,gBAAgB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACnD,gBAAgB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,8BAA8B,CAAC;IACnF,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,YAAY,KAAK,MAAM,CAAC,MAAM;IAC9B;IACA;IACA;IACA,gBAAgB,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;IAC/C,gBAAgB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC3C;IACA,gBAAgB,IAAI,KAAK,GAAG,IAAI,CAAC;IACjC;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;IAClD,oBAAoB,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IACzC;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;IAClD,oBAAoB,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IACzC;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;IAClD,oBAAoB,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC;IAC5C;IACA;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC;IACrE,oBAAoB,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC;IAC5C;IACA,gBAAgB,IAAI,KAAK,KAAK,IAAI,EAAE;IACpC;IACA;IACA,oBAAoB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACxF;IACA,oBAAoB,IAAI,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC;IACjE;IACA,oBAAoB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACtD;IACA;IACA,oBAAoB,OAAO,CAAC,WAAW,GAAG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1E,iBAAiB;IACjB;IACA,gBAAgB,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7C;IACA;IACA;IACA,gBAAgB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACnD,gBAAgB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,8BAA8B,CAAC;IACnF,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,SAAS;IACT,KAAK,CAAC;IACN,IAAI,OAAO,gBAAgB,CAAC;IAC5B,CAAC,EAAE,CAAC;;ICvQJ,IAAIA,QAAM,CAAC;IACX,CAAC,UAAU,MAAM,EAAE;IACnB,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IAC1C,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IAC1C,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;IAC9C,CAAC,EAAEA,QAAM,KAAKA,QAAM,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5B;IACA;IACA;IACA;IACA;IACA,IAAI,gBAAgB,kBAAkB,YAAY;IAClD,IAAI,SAAS,gBAAgB,CAAC,OAAO,EAAE;IACvC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA;IACA;IACA,8BAA8B,IAAI,CAAC,eAAe,GAAGA,QAAM,CAAC,KAAK,CAAC;IAClE,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,gBAAgB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IACvE;IACA;IACA;IACA;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,KAAK,EAAE;IACnD,YAAY,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACvC,YAAY,IAAI,CAAC,eAAe,GAAGA,QAAM,CAAC,KAAK,CAAC;IAChD,YAAY,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACtC,SAAS;IACT;IACA;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa,IAAI,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,KAAK;IACjF,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,KAAK;IAClD,YAAY,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,KAAK;IACjD,aAAa,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;IAC3D,gBAAgB,UAAU,KAAK,MAAM,CAAC,EAAE;IACxC,YAAY,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;IACxC,SAAS;IACT;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,KAAK;IACjD,YAAY,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,KAAK;IACjD,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC;IAC1C,gBAAgB,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;IAC9D,iBAAiB,UAAU,IAAI,MAAM,IAAI,UAAU,IAAI,MAAM,CAAC,CAAC,EAAE;IACjE;IACA;IACA,YAAY,IAAI,gBAAgB,CAAC,UAAU,CAAC;IAC5C,gBAAgB,OAAO,UAAU,CAAC;IAClC;IACA,YAAY,IAAI,UAAU,KAAK,MAAM;IACrC,gBAAgB,OAAO,IAAI,CAAC;IAC5B;IACA,YAAY,IAAI,UAAU,KAAK,MAAM;IACrC,gBAAgB,OAAO,IAAI,CAAC;IAC5B,SAAS;IACT;IACA;IACA;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,KAAK,EAAE;IACnD,YAAY,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACvC,YAAY,IAAI,CAAC,eAAe,GAAGA,QAAM,CAAC,KAAK,CAAC;IAChD,YAAY,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACtC,SAAS;IACT;IACA;IACA;IACA;IACA,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;IAC3D,YAAY,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,KAAK,EAAE;IACnD,YAAY,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACvC,YAAY,IAAI,CAAC,eAAe,GAAGA,QAAM,CAAC,KAAK,CAAC;IAChD,YAAY,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACtC,SAAS;IACT;IACA,QAAQ,IAAI,UAAU,KAAK,MAAM;IACjC,YAAY,UAAU,GAAG,MAAM,CAAC;IAChC;IACA;IACA,QAAQ,IAAI,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IACpE;IACA,QAAQ,IAAI,OAAO,KAAK,IAAI;IAC5B,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAC5C;IACA;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,eAAe,KAAKA,QAAM,CAAC,OAAO,EAAE;IACrD,YAAY,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACvC,YAAY,IAAI,CAAC,eAAe,GAAGA,QAAM,CAAC,OAAO,CAAC;IAClD,YAAY,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACtC,SAAS;IACT;IACA,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;IACnD;IACA,QAAQ,IAAI,KAAK,GAAG,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC;IACxC;IACA,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC7B,KAAK,CAAC;IACN,IAAI,OAAO,gBAAgB,CAAC;IAC5B,CAAC,EAAE,CAAC;;IClHJ;IACA;IACA;IACA;IACA;IACA,IAAI,eAAe,kBAAkB,YAAY;IACjD,IAAI,SAAS,eAAe,CAAC,OAAO,EAAE;IACtC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA;IACA,8BAA8B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IACzD,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,eAAe,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IAChE;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;IACpE,YAAY,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IACvC,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI;IAClE,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;IAC1C,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;IAC3C,YAAY,IAAI,OAAO,GAAG,IAAI,CAAC;IAC/B,YAAY,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IACvC;IACA;IACA,YAAY,IAAI,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC;IACrD;IACA;IACA,YAAY,IAAI,WAAW,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC;IAC1D;IACA;IACA;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACtE,gBAAgB,OAAO,GAAG,CAAC,IAAI,GAAG,WAAW,IAAI,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC;IACrE;IACA;IACA,YAAY,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC;IAC7C,gBAAgB,OAAO,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC;IAC/C;IACA;IACA,YAAY,IAAI,UAAU,GAAG,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI;IACtD,gBAAgB,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7D;IACA;IACA,YAAY,IAAI,UAAU,KAAK,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;IACxD,gBAAgB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC;IACA,YAAY,IAAI,UAAU,KAAK,IAAI;IACnC,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD;IACA,YAAY,OAAO,UAAU,CAAC;IAC9B,SAAS;IACT;IACA;IACA,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;IAC9C,YAAY,OAAO,IAAI,CAAC;IACxB;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,YAAY,OAAO,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IACxC;IACA;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IACpE,YAAY,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IACvC,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA,QAAQ,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,KAAK,CAAC;IACN,IAAI,OAAO,eAAe,CAAC;IAC3B,CAAC,EAAE,CAAC;;ICrFJ;IACA;IACA;IACA;IACA;IACA,IAAI,eAAe,kBAAkB,YAAY;IACjD,IAAI,SAAS,eAAe,CAAC,OAAO,EAAE;IACtC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,eAAe,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IACtE;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC,IAAI,UAAU,KAAK,MAAM;IACjE,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA,QAAQ,IAAI,UAAU,KAAK,MAAM;IACjC,YAAY,OAAO,IAAI,CAAC;IACxB;IACA,QAAQ,IAAI,UAAU,KAAK,MAAM;IACjC,YAAY,OAAO,IAAI,CAAC;IACxB;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;IAC/C,YAAY,OAAO,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC;IAC9C;IACA,QAAQ,IAAI,UAAU,KAAK,MAAM;IACjC,YAAY,UAAU,GAAG,MAAM,CAAC;IAChC;IACA,QAAQ,IAAI,OAAO,GAAG,uBAAuB,CAAC,UAAU,CAAC,CAAC;IAC1D;IACA,QAAQ,IAAI,OAAO,KAAK,IAAI;IAC5B,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IAC5C;IACA,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;IAC7C;IACA;IACA,QAAQ,IAAI,WAAW,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC;IACtD;IACA,QAAQ,IAAI,KAAK,GAAG,OAAO,GAAG,GAAG,CAAC;IAClC;IACA;IACA,QAAQ,IAAI,MAAM,GAAG,CAAC,KAAK,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC;IAClD;IACA;IACA,QAAQ,OAAO,CAAC,IAAI,GAAG,WAAW,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;IACpD,KAAK,CAAC;IACN,IAAI,OAAO,eAAe,CAAC;IAC3B,CAAC,EAAE,CAAC;;ICzDJ;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,iBAAiB,kBAAkB,YAAY;IACnD,IAAI,SAAS,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE;IAC/C,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAC3B,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,iBAAiB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IAClE;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa;IAClC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC;IAC7B,YAAY,OAAO,IAAI,CAAC;IACxB;IACA;IACA,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IACjD;IACA,QAAQ,IAAI,CAAC,UAAU;IACvB,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C;IACA,QAAQ,OAAO,UAAU,CAAC;IAC1B,KAAK,CAAC;IACN,IAAI,OAAO,iBAAiB,CAAC;IAC7B,CAAC,EAAE,CAAC;;ICnCJ;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,iBAAiB,kBAAkB,YAAY;IACnD,IAAI,SAAS,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE;IAC/C,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAC3B,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,iBAAiB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IACxE;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA;IACA,QAAQ,IAAI,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9D;IACA,QAAQ,IAAI,OAAO,KAAK,IAAI;IAC5B,YAAY,YAAY,CAAC,UAAU,CAAC,CAAC;IACrC;IACA,QAAQ,OAAO,OAAO,GAAG,IAAI,CAAC;IAC9B,KAAK,CAAC;IACN,IAAI,OAAO,iBAAiB,CAAC;IAC7B,CAAC,EAAE,CAAC;;ICtCJ;IACA;IACA;IACA;IACA;IACO,SAAS,sBAAsB,CAAC,SAAS,EAAE,OAAO,EAAE;IAC3D;IACA,IAAI,IAAI,KAAK,GAAG,SAAS,IAAI,CAAC,CAAC;IAC/B;IACA,IAAI,IAAI,KAAK,GAAG,SAAS,GAAG,MAAM,CAAC;IACnC;IACA;IACA,IAAI,IAAI,OAAO;IACf,QAAQ,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC9B;IACA,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC1B;;ICXA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,kBAAkB,YAAY;IAC9C,IAAI,SAAS,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;IAC7C,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,+BAA+B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC3D,+BAA+B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAChE,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IAC7D;IACA;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,KAAK,IAAI,CAAC,eAAe,KAAK,IAAI;IACpE,YAAY,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,EAAE;IACjD,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI;IACnE,YAAY,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE;IAChD,YAAY,OAAO,QAAQ,CAAC;IAC5B,SAAS;IACT;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;IAC3C,YAAY,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IACxC,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA,QAAQ,IAAI,SAAS,CAAC;IACtB,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;IAC3B;IACA;IACA,YAAY,SAAS,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,IAAI,CAAC;IAC3D,SAAS;IACT,aAAa;IACb;IACA;IACA,YAAY,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC;IAC3D,SAAS;IACT;IACA,QAAQ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IACpC;IACA;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE;IAChD,YAAY,IAAI,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC;IAC3D,YAAY,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAC7C;IACA;IACA;IACA,YAAY,IAAI,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;IACpD,gBAAgB,OAAO,OAAO,GAAG,CAAC,cAAc,GAAG,MAAM,IAAI,KAAK;IAClE,qBAAqB,SAAS,GAAG,MAAM,CAAC,CAAC;IACzC,aAAa;IACb;IACA;IACA;IACA,YAAY,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7E,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;IAChD,YAAY,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;IAClD,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;IAC9C,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C;IACA,QAAQ,OAAO,SAAS,CAAC;IACzB,KAAK,CAAC;IACN,IAAI,OAAO,YAAY,CAAC;IACxB,CAAC,EAAE,CAAC;;ICxFJ;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,kBAAkB,YAAY;IAC9C,IAAI,SAAS,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;IAC7C,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IACnE;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;IAC/C,YAAY,OAAO,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrE;IACA;IACA,QAAQ,IAAI,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,UAAU,GAAG,OAAO,KAAK,EAAE,IAAI,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClG;IACA;IACA,QAAQ,IAAI,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,UAAU,GAAG,OAAO,IAAI,KAAK,IAAI,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrG;IACA,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAClC,KAAK,CAAC;IACN,IAAI,OAAO,YAAY,CAAC;IACxB,CAAC,EAAE,CAAC;;ICnCJ;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,kBAAkB,YAAY;IAC7C,IAAI,SAAS,WAAW,CAAC,OAAO,EAAE;IAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC;IACA;IACA;IACA;IACA,8BAA8B,IAAI,CAAC,eAAe,GAAG,CAAC;IACtD,kCAAkC,IAAI,CAAC,eAAe,GAAG,CAAC;IAC1D,kCAAkC,IAAI,CAAC,iBAAiB,GAAG,CAAC;IAC5D,kCAAkC,IAAI,CAAC,mBAAmB,GAAG,IAAI;IACjE,kCAAkC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAClE,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IAC5D;IACA;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;IACpE,YAAY,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;IACvC,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa;IAClC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA,QAAQ,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;IAC1C;IACA,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAC3C;IACA,gBAAgB,OAAO,IAAI,CAAC;IAC5B,aAAa;IACb;IACA,iBAAiB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAChD;IACA,gBAAgB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;IAC3C;IACA,gBAAgB,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC;IACnD,aAAa;IACb;IACA,iBAAiB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAChD;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI;IACjC,oBAAoB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpD;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI;IACjC,oBAAoB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpD;IACA,gBAAgB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;IAC3C;IACA,gBAAgB,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,GAAG,CAAC;IAClD,aAAa;IACb;IACA,iBAAiB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;IAChD;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI;IACjC,oBAAoB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpD;IACA,gBAAgB,IAAI,IAAI,KAAK,IAAI;IACjC,oBAAoB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpD;IACA,gBAAgB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;IAC3C;IACA,gBAAgB,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,GAAG,CAAC;IAClD,aAAa;IACb;IACA,iBAAiB;IACjB;IACA,gBAAgB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,aAAa;IACb;IACA,YAAY,OAAO,IAAI,CAAC;IACxB,SAAS;IACT;IACA;IACA,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,EAAE;IAChF;IACA;IACA;IACA,YAAY,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;IACrF,YAAY,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAC5C,YAAY,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAC5C;IACA,YAAY,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC;IACA,YAAY,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,SAAS;IACT;IACA;IACA,QAAQ,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACxC,QAAQ,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACxC;IACA;IACA,QAAQ,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;IAC3E;IACA,QAAQ,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;IAClC;IACA;IACA,QAAQ,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,iBAAiB;IAC3D,YAAY,OAAO,IAAI,CAAC;IACxB;IACA,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;IAC9C;IACA;IACA,QAAQ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;IACjF;IACA,QAAQ,OAAO,UAAU,CAAC;IAC1B,KAAK,CAAC;IACN,IAAI,OAAO,WAAW,CAAC;IACvB,CAAC,EAAE,CAAC;;ICxHJ;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,kBAAkB,YAAY;IAC7C,IAAI,SAAS,WAAW,CAAC,OAAO,EAAE;IAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IAClE;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA,QAAQ,IAAI,KAAK,EAAE,MAAM,CAAC;IAC1B;IACA,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;IACjD;IACA,YAAY,KAAK,GAAG,CAAC,CAAC;IACtB,YAAY,MAAM,GAAG,IAAI,CAAC;IAC1B,SAAS;IACT;IACA,aAAa,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;IACtD;IACA,YAAY,KAAK,GAAG,CAAC,CAAC;IACtB,YAAY,MAAM,GAAG,IAAI,CAAC;IAC1B,SAAS;IACT;IACA,aAAa,IAAI,OAAO,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE;IACzD;IACA,YAAY,KAAK,GAAG,CAAC,CAAC;IACtB,YAAY,MAAM,GAAG,IAAI,CAAC;IAC1B,SAAS;IACT;IACA;IACA,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC,UAAU,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC;IAC3D;IACA,QAAQ,OAAO,KAAK,GAAG,CAAC,EAAE;IAC1B;IACA,YAAY,IAAI,IAAI,GAAG,UAAU,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD;IACA,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;IAC7C;IACA,YAAY,KAAK,IAAI,CAAC,CAAC;IACvB,SAAS;IACT;IACA,QAAQ,OAAO,KAAK,CAAC;IACrB,KAAK,CAAC;IACN,IAAI,OAAO,WAAW,CAAC;IACvB,CAAC,EAAE,CAAC;;IC3DJ;IACA;IACA;IACA;IACA;IACA,IAAI,mBAAmB,kBAAkB,YAAY;IACrD,IAAI,SAAS,mBAAmB,CAAC,OAAO,EAAE;IAC1C,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,mBAAmB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;IACpE;IACA,QAAQ,IAAI,IAAI,KAAK,aAAa;IAClC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC;IAC7B,YAAY,OAAO,IAAI,CAAC;IACxB;IACA,QAAQ,OAAO,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IACpC,KAAK,CAAC;IACN,IAAI,OAAO,mBAAmB,CAAC;IAC/B,CAAC,EAAE,CAAC;;IC1BJ;IACA;IACA;IACA;IACA;IACA,IAAI,mBAAmB,kBAAkB,YAAY;IACrD,IAAI,SAAS,mBAAmB,CAAC,OAAO,EAAE;IAC1C,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,KAAK;IACL;IACA;IACA;IACA;IACA;IACA,IAAI,mBAAmB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;IAC1E;IACA,QAAQ,IAAI,UAAU,KAAK,aAAa;IACxC,YAAY,OAAO,QAAQ,CAAC;IAC5B;IACA;IACA,QAAQ,IAAI,gBAAgB,CAAC,UAAU,CAAC;IACxC,YAAY,OAAO,UAAU,CAAC;IAC9B;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;IAC/C,YAAY,OAAO,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC;IAC9C;IACA,QAAQ,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;IACxC,KAAK,CAAC;IACN,IAAI,OAAO,mBAAmB,CAAC;IAC/B,CAAC,EAAE,CAAC;;IClCJ;IAeA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,eAAe,GAAG,kBAAkB,EAAE,CAAC;IAC3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,IAAI,QAAQ,GAAG;IACtB;IACA;IACA;IACA;IACA,IAAI,OAAO,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE;IACpE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,KAAK,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE;IAC3E;IACA;IACA;IACA;IACA,IAAI,SAAS,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE;IACzE;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,MAAM,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE;IACnE;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,QAAQ,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE;IACtE;IACA;IACA;IACA;IACA,IAAI,aAAa,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE;IAC/E;IACA;IACA;IACA;IACA,IAAI,WAAW,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE;IAC5E;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,QAAQ,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE;IACtE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,UAAU,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;IAC9E;IACA;IACA;IACA;IACA,IAAI,UAAU,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE;IAC/E;IACA;IACA;IACA;IACA;IACA,IAAI,gBAAgB,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE;IACrF,CAAC,CAAC;IACK,IAAI,QAAQ,GAAG;IACtB;IACA,IAAI,OAAO,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE;IACpE;IACA,IAAI,KAAK,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE;IACrE;IACA,IAAI,SAAS,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE;IACzE;IACA,IAAI,MAAM,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE;IACnE;IACA,IAAI,QAAQ,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE;IACtE;IACA,IAAI,aAAa,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE;IAC/E;IACA,IAAI,WAAW,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE;IAC5E;IACA,IAAI,QAAQ,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE;IACtE;IACA,IAAI,UAAU,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;IAC9E;IACA,IAAI,UAAU,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE;IAC/E;IACA,IAAI,gBAAgB,EAAE,UAAU,OAAO,EAAE,EAAE,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE;IACrF,CAAC,CAAC;IACF,IAAI,eAAe,EAAE;IACrB,IAAI,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;IAC1C,QAAQ,IAAI,QAAQ,CAAC,OAAO,KAAK,8BAA8B;IAC/D,YAAY,OAAO;IACnB,QAAQ,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;IACvD,YAAY,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;IACrC,YAAY,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAChD;IACA,YAAY,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,OAAO,EAAE;IAChD,gBAAgB,OAAO,IAAI,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC3D,aAAa,CAAC;IACd;IACA,YAAY,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,OAAO,EAAE;IAChD,gBAAgB,OAAO,IAAI,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC3D,aAAa,CAAC;IACd,SAAS,CAAC,CAAC;IACX,KAAK,CAAC,CAAC;IACP;;ICrKA;IACA;IACA;IACA,IAAI,MAAM,kBAAkB,YAAY;IACxC;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,SAAS,MAAM,CAAC,MAAM,EAAE;IAC5B;IACA,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC;IACA,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IAC9B,KAAK;IACL;IACA;IACA;IACA,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY;IAC/C,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IACnC,KAAK,CAAC;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;IACxC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC/B,YAAY,OAAO,aAAa,CAAC;IACjC,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IACjC,KAAK,CAAC;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,KAAK,EAAE;IAChD,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;IAClC,YAAY,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;IACjC,YAAY,OAAO,MAAM,CAAC,MAAM;IAChC,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IAC/C,SAAS;IACT,aAAa;IACb,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpC,SAAS;IACT,KAAK,CAAC;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,KAAK,EAAE;IAC7C,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;IAClC,YAAY,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;IACjC,YAAY,OAAO,MAAM,CAAC,MAAM;IAChC,gBAAgB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;IACpD,SAAS;IACT,aAAa;IACb,YAAY,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACvC,SAAS;IACT,KAAK,CAAC;IACN,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC,EAAE,CAAC;;IClEJ;IACA;IACA;IACA;IACA;IACA;AACG,QAAC,WAAW,kBAAkB,YAAY;IAC7C,IAAI,SAAS,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;IACzC,QAAQ,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC;IACvE,QAAQ,IAAI,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAC/C;IACA;IACA;IACA;IACA;IACA,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC9B;IACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC7B;IACA,QAAQ,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAChC;IACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC9B;IACA,QAAQ,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC;IACzC;IACA,QAAQ,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IACnC;IACA;IACA,QAAQ,IAAI,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;IAC1C;IACA,QAAQ,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,aAAa;IAChE,YAAY,MAAM,UAAU,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAC;IAC3D,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACtC,YAAY,MAAM,KAAK,CAAC,sBAAsB;IAC9C,gBAAgB,uDAAuD,CAAC,CAAC;IACzE,SAAS;IACT;IACA;IACA;IACA;IACA;IACA,QAAQ,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAClC;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACxC,YAAY,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;IACvC;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IAC5C,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK;IACL,IAAI,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,EAAE;IAC7D;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,GAAG,EAAE,YAAY;IACzB,YAAY,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IACrD,SAAS;IACT,QAAQ,UAAU,EAAE,IAAI;IACxB,QAAQ,YAAY,EAAE,IAAI;IAC1B,KAAK,CAAC,CAAC;IACP,IAAI,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;IAC1D;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,GAAG,EAAE,YAAY;IACzB,YAAY,OAAO,IAAI,CAAC,WAAW,KAAK,OAAO,CAAC;IAChD,SAAS;IACT,QAAQ,UAAU,EAAE,IAAI;IACxB,QAAQ,YAAY,EAAE,IAAI;IAC1B,KAAK,CAAC,CAAC;IACP,IAAI,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE;IAC9D;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,GAAG,EAAE,YAAY;IACzB,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC;IACnC,SAAS;IACT,QAAQ,UAAU,EAAE,IAAI;IACxB,QAAQ,YAAY,EAAE,IAAI;IAC1B,KAAK,CAAC,CAAC;IACP;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE;IAC7D,QAAQ,IAAI,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAC7C,QAAQ,IAAI,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAC/C;IACA;IACA;IACA,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;IACjC,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC1D,gBAAgB,KAAK,EAAE,IAAI,CAAC,WAAW,KAAK,OAAO;IACnD,aAAa,CAAC,CAAC;IACf,YAAY,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAClC,SAAS;IACT;IACA;IACA,QAAQ,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3D;IACA;IACA,QAAQ,IAAI,YAAY,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7C;IACA,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC;IACxB;IACA,QAAQ,IAAI,MAAM,CAAC;IACnB;IACA,QAAQ,OAAO,IAAI,EAAE;IACrB;IACA,YAAY,IAAI,KAAK,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC;IAC5C;IACA;IACA;IACA,YAAY,IAAI,KAAK,KAAK,aAAa;IACvC,gBAAgB,MAAM;IACtB;IACA;IACA;IACA,YAAY,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAChE;IACA,YAAY,IAAI,MAAM,KAAK,QAAQ;IACnC,gBAAgB,MAAM;IACtB,YAAY,IAAI,MAAM,KAAK,IAAI,EAAE;IACjC,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;IACzC,oBAAoB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,gCAAgC,MAAM,EAAE,CAAC;IACrF;IACA,oBAAoB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,aAAa;IACb;IACA;IACA;IACA,SAAS;IACT;IACA,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;IACjC,YAAY,GAAG;IACf,gBAAgB,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;IAClF,gBAAgB,IAAI,MAAM,KAAK,QAAQ;IACvC,oBAAoB,MAAM;IAC1B,gBAAgB,IAAI,CAAC,MAAM;IAC3B,oBAAoB,SAAS;IAC7B,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;IACzC,oBAAoB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,gCAAgC,MAAM,EAAE,CAAC;IACrF;IACA,oBAAoB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,aAAa,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE;IAClD,YAAY,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACjC,SAAS;IACT,QAAQ,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC5C,KAAK,CAAC;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,MAAM,EAAE;IAC9D;IACA;IACA;IACA;IACA,QAAQ,IAAI,QAAQ,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC5E,YAAY,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IAChD,YAAY,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;IAC3D;IACA,gBAAgB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrC,gBAAgB,MAAM,CAAC,KAAK,EAAE,CAAC;IAC/B,aAAa;IACb,iBAAiB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;IACxC;IACA;IACA,gBAAgB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrC,aAKa;IACb,SAAS;IACT;IACA,QAAQ,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAC1C,KAAK,CAAC;IACN,IAAI,OAAO,WAAW,CAAC;IACvB,CAAC,EAAE,EAAE;IAEL,SAAS,gBAAgB,CAAC,KAAK,EAAE;IACjC,IAAI,IAAI;IACR,QAAQ,OAAO,KAAK,YAAY,WAAW,CAAC;IAC5C,KAAK;IACL,IAAI,OAAO,CAAC,EAAE;IACd,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACzB,QAAQ,OAAO,KAAK,CAAC;IACrB,KAAK;IACL,CAAC;IACD,SAAS,iBAAiB,CAAC,KAAK,EAAE;IAClC,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ;IACjC,QAAQ,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;IACjC,IAAI,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;IACjC,QAAQ,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IACrC,KAAK;IACL,IAAI,IAAI,QAAQ,IAAI,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;IAC7D,QAAQ,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;IAChF,KAAK;IACL,IAAI,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7B;;IC/NA;IACA;IACA;IACA;IACA;AACG,QAAC,WAAW,kBAAkB,YAAY;IAC7C,IAAI,SAAS,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;IACzC,QAAQ,IAAI,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAC/C;IACA;IACA,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC9B;IACA,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC7B;IACA;IACA,QAAQ,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IACnC;IACA,QAAQ,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,GAAG,aAAa,CAAC;IAC7E;IACA;IACA;IACA;IACA;IACA,QAAQ,IAAI,OAAO,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC,EAAE;IACpE;IACA,YAAY,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC;IAC/D,YAAY,IAAI,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;IAC9C,YAAY,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,aAAa;IACpE,gBAAgB,MAAM,UAAU,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAC;IAC/D,YAAY,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC1C,gBAAgB,MAAM,KAAK,CAAC,sBAAsB;IAClD,oBAAoB,uDAAuD,CAAC,CAAC;IAC7E,aAAa;IACb,YAAY,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IACtC;IACA;IACA;IACA,SAAS;IACT,aAAa;IACb;IACA,YAAY,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAClD,YAAY,IAAI,GAAG,GAAG,cAAc,EAAE,IAAI,EAAE,CAAC;IAC7C,YAAY,IAAI,KAAK,KAAK,SAAS,IAAI,SAAS,IAAI,GAAG,EAAE;IACzD,gBAAgB,OAAO,CAAC,IAAI,CAAC,sDAAsD;IACnF,sBAAsB,mBAAmB,CAAC,CAAC;IAC3C,aAAa;IACb,SAAS;IACT;IACA;IACA;IACA;IACA;IACA,KAAK;IACL,IAAI,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,EAAE;IAC7D;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,GAAG,EAAE,YAAY;IACzB,YAAY,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IACrD,SAAS;IACT,QAAQ,UAAU,EAAE,IAAI;IACxB,QAAQ,YAAY,EAAE,IAAI;IAC1B,KAAK,CAAC,CAAC;IACP;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,UAAU,EAAE,OAAO,EAAE;IAClE,QAAQ,UAAU,GAAG,UAAU,KAAK,SAAS,GAAG,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;IACxE,QAAQ,IAAI,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IAC/C;IACA;IACA;IACA,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa;IAC/B,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC1D,gBAAgB,KAAK,EAAE,IAAI,CAAC,MAAM,KAAK,OAAO;IAC9C,aAAa,CAAC,CAAC;IACf,QAAQ,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3D;IACA,QAAQ,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;IAC/D;IACA,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC;IACxB;IACA,QAAQ,IAAI,MAAM,CAAC;IACnB;IACA,QAAQ,OAAO,IAAI,EAAE;IACrB;IACA,YAAY,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IACrC,YAAY,IAAI,KAAK,KAAK,aAAa;IACvC,gBAAgB,MAAM;IACtB;IACA;IACA,YAAY,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzD,YAAY,IAAI,MAAM,KAAK,QAAQ;IACnC,gBAAgB,MAAM;IACtB,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;IACrC,gBAAgB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,gCAAgC,MAAM,EAAE,CAAC;IACjF;IACA,gBAAgB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpC,SAAS;IACT;IACA,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;IACjC,YAAY,OAAO,IAAI,EAAE;IACzB,gBAAgB,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACpE,gBAAgB,IAAI,MAAM,KAAK,QAAQ;IACvC,oBAAoB,MAAM;IAC1B,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;IACzC,oBAAoB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,gCAAgC,MAAM,EAAE,CAAC;IACrF;IACA,oBAAoB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,aAAa;IACb,YAAY,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACjC,SAAS;IACT;IACA;IACA;IACA,QAAQ,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACtC,KAAK,CAAC;IACN,IAAI,OAAO,WAAW,CAAC;IACvB,CAAC,EAAE;;IClIH;IACA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IACnC,IAAI,IAAI,cAAc,GAAG,UAAU,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,MAAM,CAAC;IACjE,WAAW,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW;IAC7C,WAAW,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC;IACnC,IAAI,IAAI,cAAc,CAAC,aAAa,CAAC;IACrC,QAAQ,MAAM,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;IAC5C,IAAI,IAAI,cAAc,CAAC,aAAa,CAAC;IACrC,QAAQ,MAAM,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;IAC5C;;;;;;;;;;;;;"}