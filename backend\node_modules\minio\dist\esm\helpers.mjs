import * as fs from "fs";
import * as path from "path";
import * as querystring from 'query-string';
import * as errors from "./errors.mjs";
import { getEncryptionHeaders, isEmpty, isEmptyObject, isNumber, isObject, isString, isValidBucketName, isValidObjectName } from "./internal/helper.mjs";
import { RETENTION_MODES } from "./internal/type.mjs";
export { ENCRYPTION_TYPES, LEGAL_HOLD_STATUS, RETENTION_MODES, RETENTION_VALIDITY_UNITS } from "./internal/type.mjs";
export const DEFAULT_REGION = 'us-east-1';
export const PRESIGN_EXPIRY_DAYS_MAX = 24 * 60 * 60 * 7; // 7 days in seconds

export class CopySourceOptions {
  constructor({
    Bucket,
    Object,
    VersionID = '',
    MatchETag = '',
    NoMatchETag = '',
    MatchModifiedSince = null,
    MatchUnmodifiedSince = null,
    MatchRange = false,
    Start = 0,
    End = 0,
    Encryption = undefined
  }) {
    this.Bucket = Bucket;
    this.Object = Object;
    this.VersionID = VersionID;
    this.MatchETag = MatchETag;
    this.NoMatchETag = NoMatchETag;
    this.MatchModifiedSince = MatchModifiedSince;
    this.MatchUnmodifiedSince = MatchUnmodifiedSince;
    this.MatchRange = MatchRange;
    this.Start = Start;
    this.End = End;
    this.Encryption = Encryption;
  }
  validate() {
    if (!isValidBucketName(this.Bucket)) {
      throw new errors.InvalidBucketNameError('Invalid Source bucket name: ' + this.Bucket);
    }
    if (!isValidObjectName(this.Object)) {
      throw new errors.InvalidObjectNameError(`Invalid Source object name: ${this.Object}`);
    }
    if (this.MatchRange && this.Start !== -1 && this.End !== -1 && this.Start > this.End || this.Start < 0) {
      throw new errors.InvalidObjectNameError('Source start must be non-negative, and start must be at most end.');
    } else if (this.MatchRange && !isNumber(this.Start) || !isNumber(this.End)) {
      throw new errors.InvalidObjectNameError('MatchRange is specified. But Invalid Start and End values are specified.');
    }
    return true;
  }
  getHeaders() {
    const headerOptions = {};
    headerOptions['x-amz-copy-source'] = encodeURI(this.Bucket + '/' + this.Object);
    if (!isEmpty(this.VersionID)) {
      headerOptions['x-amz-copy-source'] = `${encodeURI(this.Bucket + '/' + this.Object)}?versionId=${this.VersionID}`;
    }
    if (!isEmpty(this.MatchETag)) {
      headerOptions['x-amz-copy-source-if-match'] = this.MatchETag;
    }
    if (!isEmpty(this.NoMatchETag)) {
      headerOptions['x-amz-copy-source-if-none-match'] = this.NoMatchETag;
    }
    if (!isEmpty(this.MatchModifiedSince)) {
      headerOptions['x-amz-copy-source-if-modified-since'] = this.MatchModifiedSince;
    }
    if (!isEmpty(this.MatchUnmodifiedSince)) {
      headerOptions['x-amz-copy-source-if-unmodified-since'] = this.MatchUnmodifiedSince;
    }
    return headerOptions;
  }
}

/**
 * @deprecated use nodejs fs module
 */
export function removeDirAndFiles(dirPath, removeSelf = true) {
  if (removeSelf) {
    return fs.rmSync(dirPath, {
      recursive: true,
      force: true
    });
  }
  fs.readdirSync(dirPath).forEach(item => {
    fs.rmSync(path.join(dirPath, item), {
      recursive: true,
      force: true
    });
  });
}
export class CopyDestinationOptions {
  constructor({
    Bucket,
    Object,
    Encryption,
    UserMetadata,
    UserTags,
    LegalHold,
    RetainUntilDate,
    Mode,
    MetadataDirective,
    Headers
  }) {
    this.Bucket = Bucket;
    this.Object = Object;
    this.Encryption = Encryption ?? undefined; // null input will become undefined, easy for runtime assert
    this.UserMetadata = UserMetadata;
    this.UserTags = UserTags;
    this.LegalHold = LegalHold;
    this.Mode = Mode; // retention mode
    this.RetainUntilDate = RetainUntilDate;
    this.MetadataDirective = MetadataDirective;
    this.Headers = Headers;
  }
  getHeaders() {
    const replaceDirective = 'REPLACE';
    const headerOptions = {};
    const userTags = this.UserTags;
    if (!isEmpty(userTags)) {
      headerOptions['X-Amz-Tagging-Directive'] = replaceDirective;
      headerOptions['X-Amz-Tagging'] = isObject(userTags) ? querystring.stringify(userTags) : isString(userTags) ? userTags : '';
    }
    if (this.Mode) {
      headerOptions['X-Amz-Object-Lock-Mode'] = this.Mode; // GOVERNANCE or COMPLIANCE
    }

    if (this.RetainUntilDate) {
      headerOptions['X-Amz-Object-Lock-Retain-Until-Date'] = this.RetainUntilDate; // needs to be UTC.
    }

    if (this.LegalHold) {
      headerOptions['X-Amz-Object-Lock-Legal-Hold'] = this.LegalHold; // ON or OFF
    }

    if (this.UserMetadata) {
      for (const [key, value] of Object.entries(this.UserMetadata)) {
        headerOptions[`X-Amz-Meta-${key}`] = value.toString();
      }
    }
    if (this.MetadataDirective) {
      headerOptions[`X-Amz-Metadata-Directive`] = this.MetadataDirective;
    }
    if (this.Encryption) {
      const encryptionHeaders = getEncryptionHeaders(this.Encryption);
      for (const [key, value] of Object.entries(encryptionHeaders)) {
        headerOptions[key] = value;
      }
    }
    if (this.Headers) {
      for (const [key, value] of Object.entries(this.Headers)) {
        headerOptions[key] = value;
      }
    }
    return headerOptions;
  }
  validate() {
    if (!isValidBucketName(this.Bucket)) {
      throw new errors.InvalidBucketNameError('Invalid Destination bucket name: ' + this.Bucket);
    }
    if (!isValidObjectName(this.Object)) {
      throw new errors.InvalidObjectNameError(`Invalid Destination object name: ${this.Object}`);
    }
    if (!isEmpty(this.UserMetadata) && !isObject(this.UserMetadata)) {
      throw new errors.InvalidObjectNameError(`Destination UserMetadata should be an object with key value pairs`);
    }
    if (!isEmpty(this.Mode) && ![RETENTION_MODES.GOVERNANCE, RETENTION_MODES.COMPLIANCE].includes(this.Mode)) {
      throw new errors.InvalidObjectNameError(`Invalid Mode specified for destination object it should be one of [GOVERNANCE,COMPLIANCE]`);
    }
    if (this.Encryption !== undefined && isEmptyObject(this.Encryption)) {
      throw new errors.InvalidObjectNameError(`Invalid Encryption configuration for destination object `);
    }
    return true;
  }
}

/**
 * maybe this should be a generic type for Records, leave it for later refactor
 */
export class SelectResults {
  constructor({
    records,
    // parsed data as stream
    response,
    // original response stream
    stats,
    // stats as xml
    progress // stats as xml
  }) {
    this.records = records;
    this.response = response;
    this.stats = stats;
    this.progress = progress;
  }
  setStats(stats) {
    this.stats = stats;
  }
  getStats() {
    return this.stats;
  }
  setProgress(progress) {
    this.progress = progress;
  }
  getProgress() {
    return this.progress;
  }
  setResponse(response) {
    this.response = response;
  }
  getResponse() {
    return this.response;
  }
  setRecords(records) {
    this.records = records;
  }
  getRecords() {
    return this.records;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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