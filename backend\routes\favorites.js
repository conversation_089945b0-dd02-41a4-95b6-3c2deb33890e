import express from 'express';
import prisma from '../lib/db.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取用户收藏列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 12 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const favorites = await prisma.userFavorite.findMany({
      where: {
        userId: req.user.userId
      },
      include: {
        recipe: {
          include: {
            author: {
              select: {
                id: true,
                username: true,
                role: true
              }
            },
            _count: {
              select: {
                comments: true,
                favorites: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: parseInt(limit)
    });

    // 获取总数
    const total = await prisma.userFavorite.count({
      where: {
        userId: req.user.userId
      }
    });

    // 提取菜谱数据
    const recipes = favorites.map(fav => fav.recipe);

    res.json({
      recipes,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('获取收藏列表错误:', error);
    res.status(500).json({ 
      error: '获取收藏列表失败' 
    });
  }
});

// 检查菜谱是否已收藏
router.get('/check/:recipeId', authenticateToken, async (req, res) => {
  try {
    const { recipeId } = req.params;

    const favorite = await prisma.userFavorite.findUnique({
      where: {
        userId_recipeId: {
          userId: req.user.userId,
          recipeId: recipeId
        }
      }
    });

    res.json({
      isFavorited: !!favorite
    });

  } catch (error) {
    console.error('检查收藏状态错误:', error);
    res.status(500).json({ 
      error: '检查收藏状态失败' 
    });
  }
});

// 添加收藏
router.post('/:recipeId', authenticateToken, async (req, res) => {
  try {
    const { recipeId } = req.params;

    // 检查菜谱是否存在
    const recipe = await prisma.recipe.findUnique({
      where: { id: recipeId }
    });

    if (!recipe) {
      return res.status(404).json({ 
        error: '菜谱不存在' 
      });
    }

    // 检查是否已收藏
    const existingFavorite = await prisma.userFavorite.findUnique({
      where: {
        userId_recipeId: {
          userId: req.user.userId,
          recipeId: recipeId
        }
      }
    });

    if (existingFavorite) {
      return res.status(400).json({ 
        error: '已经收藏过此菜谱' 
      });
    }

    // 添加收藏
    await prisma.userFavorite.create({
      data: {
        userId: req.user.userId,
        recipeId: recipeId
      }
    });

    res.status(201).json({
      message: '收藏成功'
    });

  } catch (error) {
    console.error('添加收藏错误:', error);
    res.status(500).json({ 
      error: '收藏失败' 
    });
  }
});

// 取消收藏
router.delete('/:recipeId', authenticateToken, async (req, res) => {
  try {
    const { recipeId } = req.params;

    // 检查收藏是否存在
    const favorite = await prisma.userFavorite.findUnique({
      where: {
        userId_recipeId: {
          userId: req.user.userId,
          recipeId: recipeId
        }
      }
    });

    if (!favorite) {
      return res.status(404).json({ 
        error: '未收藏此菜谱' 
      });
    }

    // 删除收藏
    await prisma.userFavorite.delete({
      where: {
        userId_recipeId: {
          userId: req.user.userId,
          recipeId: recipeId
        }
      }
    });

    res.json({
      message: '取消收藏成功'
    });

  } catch (error) {
    console.error('取消收藏错误:', error);
    res.status(500).json({ 
      error: '取消收藏失败' 
    });
  }
});

export default router;
