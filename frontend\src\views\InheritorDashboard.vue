<template>
  <div class="inheritor-dashboard">
    <div class="dashboard-header">
      <div class="header-content">
        <div class="inheritor-info">
          <el-avatar :size="80" :src="userStore.user?.avatar">
            {{ userStore.user?.username?.charAt(0) }}
          </el-avatar>
          <div class="info-text">
            <h1>{{ userStore.user?.username }}</h1>
            <el-tag type="warning" size="large">
              <el-icon><Crown /></el-icon>
              传承人
            </el-tag>
            <p class="description">传承中华美食文化，分享地道家乡味道</p>
          </div>
        </div>
        
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalRecipes }}</div>
            <div class="stat-label">创建菜谱</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalViews }}</div>
            <div class="stat-label">总浏览量</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalFavorites }}</div>
            <div class="stat-label">总收藏数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalComments }}</div>
            <div class="stat-label">总评论数</div>
          </div>
        </div>
      </div>
    </div>

    <div class="dashboard-content">
      <div class="content-header">
        <h2>我的菜谱管理</h2>
        <el-button type="primary" @click="$router.push('/recipes/create')">
          <el-icon><Plus /></el-icon>
          创建新菜谱
        </el-button>
      </div>

      <div class="recipes-section">
        <div class="filters">
          <el-input
            v-model="searchQuery"
            placeholder="搜索菜谱..."
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 150px" @change="fetchRecipes">
            <el-option label="全部" value="" />
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
          </el-select>
        </div>

        <div v-loading="loading" class="recipes-grid">
          <div v-if="recipes.length === 0 && !loading" class="empty-state">
            <el-empty description="还没有创建任何菜谱">
              <el-button type="primary" @click="$router.push('/recipes/create')">
                创建第一个菜谱
              </el-button>
            </el-empty>
          </div>
          
          <div v-for="recipe in recipes" :key="recipe.id" class="recipe-card">
            <div class="recipe-image">
              <img :src="recipe.image || '/placeholder-dish.jpg'" :alt="recipe.name" />
              <div class="recipe-status">
                <el-tag :type="recipe.isPublished ? 'success' : 'info'" size="small">
                  {{ recipe.isPublished ? '已发布' : '草稿' }}
                </el-tag>
              </div>
            </div>
            
            <div class="recipe-content">
              <h3 class="recipe-title">{{ recipe.name }}</h3>
              <p class="recipe-dialect">{{ recipe.dialectName }}</p>
              <p class="recipe-region">{{ recipe.region }}</p>
              
              <div class="recipe-stats">
                <span><el-icon><View /></el-icon> {{ recipe.viewCount }}</span>
                <span><el-icon><Star /></el-icon> {{ recipe._count?.favorites || 0 }}</span>
                <span><el-icon><ChatDotRound /></el-icon> {{ recipe._count?.comments || 0 }}</span>
              </div>
              
              <div class="recipe-actions">
                <el-button size="small" @click="viewRecipe(recipe.id)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button size="small" type="primary" @click="editRecipe(recipe.id)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button 
                  size="small" 
                  :type="recipe.isPublished ? 'warning' : 'success'"
                  @click="togglePublishStatus(recipe)"
                >
                  {{ recipe.isPublished ? '取消发布' : '发布' }}
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="deleteRecipe(recipe)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination" v-if="pagination.total > 0">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[12, 24, 48]"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { getRecipes, deleteRecipe as deleteRecipeApi, updateRecipe } from '../api/recipes.js'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const recipes = ref([])

const stats = reactive({
  totalRecipes: 0,
  totalViews: 0,
  totalFavorites: 0,
  totalComments: 0
})

const pagination = reactive({
  page: 1,
  limit: 12,
  total: 0
})

// 检查用户权限
const checkPermission = () => {
  if (!userStore.isLoggedIn || userStore.user?.role !== 'INHERITOR') {
    ElMessage.error('只有传承人可以访问此页面')
    router.push('/')
    return false
  }
  return true
}

const fetchRecipes = async () => {
  if (!checkPermission()) return

  try {
    loading.value = true
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      authorId: userStore.user.id  // 只获取当前用户的菜谱
    }

    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    if (statusFilter.value === 'published') {
      params.isPublished = 'true'
    } else if (statusFilter.value === 'draft') {
      params.isPublished = 'false'
    }

    console.log('获取菜谱参数:', params)
    const response = await getRecipes(params)  // 使用通用的getRecipes而不是getMyRecipes
    console.log('菜谱响应:', response)

    recipes.value = response.recipes || []
    pagination.total = response.pagination?.total || 0

    // 计算统计数据
    calculateStats()
  } catch (error) {
    console.error('获取菜谱列表失败:', error)
    ElMessage.error('获取菜谱列表失败')
  } finally {
    loading.value = false
  }
}

const calculateStats = () => {
  stats.totalRecipes = recipes.value.length
  stats.totalViews = recipes.value.reduce((sum, recipe) => sum + (recipe.viewCount || 0), 0)
  stats.totalFavorites = recipes.value.reduce((sum, recipe) => sum + (recipe._count?.favorites || 0), 0)
  stats.totalComments = recipes.value.reduce((sum, recipe) => sum + (recipe._count?.comments || 0), 0)
}

const handleSearch = () => {
  pagination.page = 1
  fetchRecipes()
}

const handlePageChange = (page) => {
  pagination.page = page
  fetchRecipes()
}

const handleSizeChange = (size) => {
  pagination.limit = size
  pagination.page = 1
  fetchRecipes()
}

const viewRecipe = (id) => {
  router.push(`/recipes/${id}`)
}

const editRecipe = (id) => {
  router.push(`/recipes/${id}/edit`)
}

const togglePublishStatus = async (recipe) => {
  try {
    const newStatus = !recipe.isPublished
    await updateRecipe(recipe.id, { isPublished: newStatus })
    
    recipe.isPublished = newStatus
    ElMessage.success(newStatus ? '菜谱已发布' : '菜谱已取消发布')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteRecipe = (recipe) => {
  ElMessageBox.confirm(
    `确定要删除菜谱"${recipe.name}"吗？此操作不可恢复。`,
    '确认删除',
    {
      type: 'warning',
      confirmButtonText: '确定删除',
      cancelButtonText: '取消'
    }
  ).then(async () => {
    try {
      await deleteRecipeApi(recipe.id)
      ElMessage.success('菜谱删除成功')
      fetchRecipes()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

onMounted(() => {
  if (checkPermission()) {
    fetchRecipes()
  }
})
</script>

<style scoped>
.inheritor-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-header {
  background: white;
  padding: 40px 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.inheritor-info {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.info-text h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.description {
  color: #909399;
  margin: 10px 0 0 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
  font-size: 0.9rem;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.content-header h2 {
  color: white;
  margin: 0;
}

.recipes-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.recipe-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.recipe-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.recipe-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-status {
  position: absolute;
  top: 10px;
  right: 10px;
}

.recipe-content {
  padding: 15px;
}

.recipe-title {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 5px;
  color: #303133;
}

.recipe-dialect {
  color: #409eff;
  margin-bottom: 5px;
}

.recipe-region {
  color: #909399;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.recipe-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  color: #909399;
  font-size: 0.9rem;
}

.recipe-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.recipe-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
}

.pagination {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .inheritor-info {
    flex-direction: column;
    text-align: center;
  }
  
  .content-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .recipes-grid {
    grid-template-columns: 1fr;
  }
  
  .recipe-actions {
    justify-content: center;
  }
}
</style>
