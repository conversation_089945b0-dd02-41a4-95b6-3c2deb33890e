<template>
  <div class="admin-view">
    <div class="container">
      <div class="header">
        <h1>管理后台</h1>
        <p>系统管理和内容审核</p>
      </div>

      <!-- 标签页导航 -->
      <el-tabs v-model="activeTab" class="admin-tabs">
        <el-tab-pane label="菜谱管理" name="recipes">
          <div class="tab-header">
            <h2>菜谱状态管理</h2>
            <p>管理所有菜谱的发布状态</p>
          </div>

          <el-table :data="recipes" v-loading="recipesLoading" stripe>
            <el-table-column type="index" label="#" width="60" />
            <el-table-column label="图片" width="80">
              <template #default="scope">
                <el-image
                  :src="scope.row.image || '/placeholder-dish.jpg'"
                  class="table-image"
                  fit="cover"
                />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="菜名" min-width="160" />
            <el-table-column prop="dialectName" label="方言名称" min-width="140" />
            <el-table-column prop="region" label="地区" width="120" />
            <el-table-column label="作者" width="120">
              <template #default="scope">
                <div class="author-info">
                  <span>{{ scope.row.author?.username }}</span>
                  <el-tag v-if="scope.row.author?.role === 'INHERITOR'" type="warning" size="small">
                    传承人
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="viewCount" label="浏览量" width="100" />
            <el-table-column prop="isPublished" label="发布状态" width="120">
              <template #default="scope">
                <el-tag :type="scope.row.isPublished ? 'success' : 'info'">
                  {{ scope.row.isPublished ? '已发布' : '草稿' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button
                  size="small"
                  :type="scope.row.isPublished ? 'warning' : 'success'"
                  @click="togglePublishStatus(scope.row)"
                >
                  {{ scope.row.isPublished ? '取消发布' : '发布' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 菜谱分页 -->
          <div class="pagination" v-if="recipesPagination.total > 0">
            <el-pagination
              v-model:current-page="recipesPagination.page"
              v-model:page-size="recipesPagination.limit"
              :total="recipesPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50]"
              @size-change="handleRecipesSizeChange"
              @current-change="handleRecipesPageChange"
            />
          </div>
        </el-tab-pane>

        <!-- 传承人申请管理 -->
        <el-tab-pane label="传承人申请" name="applications">
          <div class="tab-header">
            <h2>传承人申请审核</h2>
            <p>审核用户的传承人申请</p>
          </div>

          <!-- 申请状态筛选 -->
          <div class="filters">
            <el-select v-model="applicationStatusFilter" placeholder="申请状态" style="width: 150px" @change="fetchApplications">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="PENDING" />
              <el-option label="已通过" value="APPROVED" />
              <el-option label="已拒绝" value="REJECTED" />
            </el-select>
          </div>

          <el-table :data="applications" v-loading="applicationsLoading" stripe>
            <el-table-column type="index" label="#" width="60" />
            <el-table-column prop="user.username" label="用户名" width="120" />
            <el-table-column prop="realName" label="真实姓名" width="120" />
            <el-table-column prop="phone" label="联系电话" width="130" />
            <el-table-column prop="region" label="地区" width="100" />
            <el-table-column prop="specialties" label="专业技能" min-width="150" show-overflow-tooltip />
            <el-table-column prop="status" label="申请状态" width="100">
              <template #default="scope">
                <el-tag :type="getApplicationStatusType(scope.row.status)">
                  {{ getApplicationStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="appliedAt" label="申请时间" width="120">
              <template #default="scope">
                {{ formatDate(scope.row.appliedAt) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button
                  v-if="scope.row.status === 'PENDING'"
                  size="small"
                  type="primary"
                  @click="showApplicationDetail(scope.row)"
                >
                  查看详情
                </el-button>
                <el-button
                  v-if="scope.row.status === 'PENDING'"
                  size="small"
                  type="success"
                  @click="approveApplication(scope.row)"
                >
                  通过
                </el-button>
                <el-button
                  v-if="scope.row.status === 'PENDING'"
                  size="small"
                  type="danger"
                  @click="rejectApplication(scope.row)"
                >
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 申请分页 -->
          <div class="pagination" v-if="applicationsPagination.total > 0">
            <el-pagination
              v-model:current-page="applicationsPagination.page"
              v-model:page-size="applicationsPagination.limit"
              :total="applicationsPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50]"
              @size-change="handleApplicationsSizeChange"
              @current-change="handleApplicationsPageChange"
            />
          </div>
        </el-tab-pane>

        <!-- 用户管理 -->
        <el-tab-pane label="用户管理" name="users">
          <div class="tab-header">
            <h2>用户管理</h2>
            <p>管理系统用户信息</p>
          </div>

          <!-- 用户角色筛选 -->
          <div class="filters">
            <el-select v-model="userRoleFilter" placeholder="用户角色" style="width: 150px" @change="fetchUsers">
              <el-option label="全部" value="" />
              <el-option label="普通用户" value="USER" />
              <el-option label="传承人" value="INHERITOR" />
              <el-option label="管理员" value="ADMIN" />
            </el-select>
          </div>

          <el-table :data="users" v-loading="usersLoading" stripe>
            <el-table-column type="index" label="#" width="60" />
            <el-table-column prop="username" label="用户名" width="150" />
            <el-table-column prop="email" label="邮箱" width="200" />
            <el-table-column prop="role" label="角色" width="120">
              <template #default="scope">
                <el-tag :type="getRoleType(scope.row.role)">
                  {{ getRoleText(scope.row.role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isActive" label="账号状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
                  {{ scope.row.isActive ? '正常' : '已禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="注册时间" width="120">
              <template #default="scope">
                {{ formatDate(scope.row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column label="统计" width="200">
              <template #default="scope">
                <div class="user-stats">
                  <span>菜谱: {{ scope.row._count?.recipes || 0 }}</span>
                  <span>收藏: {{ scope.row._count?.favorites || 0 }}</span>
                  <span>评论: {{ scope.row._count?.comments || 0 }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280">
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  @click="viewUserDetail(scope.row)"
                >
                  查看详情
                </el-button>
                <el-button
                  v-if="scope.row.role !== 'ADMIN'"
                  size="small"
                  :type="scope.row.isActive ? 'warning' : 'success'"
                  @click="toggleUserStatus(scope.row)"
                >
                  {{ scope.row.isActive ? '禁用' : '启用' }}
                </el-button>
                <el-button
                  v-if="scope.row.role !== 'ADMIN'"
                  size="small"
                  type="danger"
                  @click="handleDeleteUser(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 用户分页 -->
          <div class="pagination" v-if="usersPagination.total > 0">
            <el-pagination
              v-model:current-page="usersPagination.page"
              v-model:page-size="usersPagination.limit"
              :total="usersPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50]"
              @size-change="handleUsersSizeChange"
              @current-change="handleUsersPageChange"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRecipes, updateRecipe } from '../api/recipes.js'
import { getApplications, reviewApplication } from '../api/applications.js'
import { getUsers, updateUserStatus, deleteUser } from '../api/users.js'

// 标签页状态
const activeTab = ref('recipes')

// 菜谱管理
const recipesLoading = ref(false)
const recipes = ref([])
const recipesPagination = reactive({ page: 1, limit: 10, total: 0 })

// 传承人申请管理
const applicationsLoading = ref(false)
const applications = ref([])
const applicationsPagination = reactive({ page: 1, limit: 10, total: 0 })
const applicationStatusFilter = ref('')

// 用户管理
const usersLoading = ref(false)
const users = ref([])
const usersPagination = reactive({ page: 1, limit: 10, total: 0 })
const userRoleFilter = ref('')

// 菜谱管理方法
const fetchRecipes = async () => {
  try {
    recipesLoading.value = true
    const res = await getRecipes({
      page: recipesPagination.page,
      limit: recipesPagination.limit
    })
    recipes.value = res.recipes || []
    recipesPagination.total = res.pagination?.total || 0
  } catch (e) {
    ElMessage.error(e.error || '获取菜谱列表失败')
  } finally {
    recipesLoading.value = false
  }
}

const togglePublishStatus = async (recipe) => {
  try {
    const newStatus = !recipe.isPublished
    await updateRecipe(recipe.id, { isPublished: newStatus })
    recipe.isPublished = newStatus
    ElMessage.success(newStatus ? '菜谱已发布' : '菜谱已取消发布')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleRecipesPageChange = (page) => {
  recipesPagination.page = page
  fetchRecipes()
}

const handleRecipesSizeChange = (size) => {
  recipesPagination.limit = size
  recipesPagination.page = 1
  fetchRecipes()
}

// 传承人申请管理方法
const fetchApplications = async () => {
  try {
    applicationsLoading.value = true
    const res = await getApplications({
      page: applicationsPagination.page,
      limit: applicationsPagination.limit,
      status: applicationStatusFilter.value
    })
    applications.value = res.applications || []
    applicationsPagination.total = res.pagination?.total || 0
  } catch (e) {
    ElMessage.error(e.error || '获取申请列表失败')
  } finally {
    applicationsLoading.value = false
  }
}

const getApplicationStatusText = (status) => {
  switch (status) {
    case 'PENDING':
      return '待审核'
    case 'APPROVED':
      return '已通过'
    case 'REJECTED':
      return '已拒绝'
    default:
      return '未知状态'
  }
}

const getApplicationStatusType = (status) => {
  switch (status) {
    case 'PENDING':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return 'info'
  }
}

const showApplicationDetail = (application) => {
  ElMessageBox.alert(
    `
    <div style="text-align: left;">
      <p><strong>真实姓名：</strong>${application.realName}</p>
      <p><strong>联系电话：</strong>${application.phone}</p>
      <p><strong>所在地区：</strong>${application.region}</p>
      <p><strong>专业技能：</strong>${application.specialties}</p>
      <p><strong>相关经验：</strong>${application.experience}</p>
      <p><strong>证书资质：</strong>${application.certifications || '无'}</p>
      <p><strong>个人简介：</strong>${application.introduction}</p>
    </div>
    `,
    '申请详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭'
    }
  )
}

const approveApplication = async (application) => {
  try {
    await reviewApplication(application.id, {
      status: 'APPROVED',
      reviewComment: '申请通过'
    })
    ElMessage.success('申请已通过')
    fetchApplications()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const rejectApplication = (application) => {
  ElMessageBox.prompt('请输入拒绝理由', '拒绝申请', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: '请输入拒绝理由...'
  }).then(async ({ value }) => {
    try {
      await reviewApplication(application.id, {
        status: 'REJECTED',
        reviewComment: value || '申请不符合要求'
      })
      ElMessage.success('申请已拒绝')
      fetchApplications()
    } catch (error) {
      ElMessage.error('操作失败')
    }
  }).catch(() => {})
}

const handleApplicationsPageChange = (page) => {
  applicationsPagination.page = page
  fetchApplications()
}

const handleApplicationsSizeChange = (size) => {
  applicationsPagination.limit = size
  applicationsPagination.page = 1
  fetchApplications()
}

// 用户管理方法
const fetchUsers = async () => {
  try {
    usersLoading.value = true
    const res = await getUsers({
      page: usersPagination.page,
      limit: usersPagination.limit,
      role: userRoleFilter.value
    })
    users.value = res.users || []
    usersPagination.total = res.pagination?.total || 0
  } catch (e) {
    ElMessage.error(e.error || '获取用户列表失败')
  } finally {
    usersLoading.value = false
  }
}

const getRoleText = (role) => {
  switch (role) {
    case 'USER':
      return '普通用户'
    case 'INHERITOR':
      return '传承人'
    case 'ADMIN':
      return '管理员'
    default:
      return '未知角色'
  }
}

const getRoleType = (role) => {
  switch (role) {
    case 'USER':
      return ''
    case 'INHERITOR':
      return 'warning'
    case 'ADMIN':
      return 'danger'
    default:
      return 'info'
  }
}

const viewUserDetail = (user) => {
  const statusText = user.isActive ? '正常' : '已禁用'

  ElMessageBox.alert(
    `
    <div style="text-align: left;">
      <p><strong>用户名：</strong>${user.username}</p>
      <p><strong>邮箱：</strong>${user.email || '未设置'}</p>
      <p><strong>角色：</strong>${getRoleText(user.role)}</p>
      <p><strong>账号状态：</strong><span style="color: ${user.isActive ? '#67c23a' : '#f56c6c'}">${statusText}</span></p>
      <p><strong>注册时间：</strong>${formatDate(user.createdAt)}</p>
      <p><strong>菜谱数量：</strong>${user._count?.recipes || 0}</p>
      <p><strong>收藏数量：</strong>${user._count?.favorites || 0}</p>
      <p><strong>评论数量：</strong>${user._count?.comments || 0}</p>
    </div>
    `,
    '用户详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭'
    }
  )
}

// 禁用/启用用户账号
const toggleUserStatus = async (user) => {
  const action = user.isActive ? '禁用' : '启用'
  const confirmText = user.isActive
    ? `确定要禁用用户 "${user.username}" 的账号吗？禁用后该用户将无法登录系统。`
    : `确定要启用用户 "${user.username}" 的账号吗？`

  try {
    await ElMessageBox.confirm(confirmText, `${action}用户账号`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateUserStatus(user.id, !user.isActive)
    user.isActive = !user.isActive
    ElMessage.success(`用户账号已${action}`)

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.error || `${action}用户账号失败`)
    }
  }
}

// 删除用户
const handleDeleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复，将同时删除该用户的所有数据。`,
      '删除用户',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await deleteUser(user.id)
    ElMessage.success('用户已删除')
    fetchUsers()

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.error || '删除用户失败')
    }
  }
}

const handleUsersPageChange = (page) => {
  usersPagination.page = page
  fetchUsers()
}

const handleUsersSizeChange = (size) => {
  usersPagination.limit = size
  usersPagination.page = 1
  fetchUsers()
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 监听标签页切换
watch(activeTab, (newTab) => {
  if (newTab === 'recipes' && recipes.value.length === 0) {
    fetchRecipes()
  } else if (newTab === 'applications' && applications.value.length === 0) {
    fetchApplications()
  } else if (newTab === 'users' && users.value.length === 0) {
    fetchUsers()
  }
})

onMounted(() => {
  fetchRecipes()
})
</script>

<style scoped>
.admin-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.header {
  margin-bottom: 20px;
  text-align: center;
}

.header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.header p {
  color: #909399;
  margin: 0;
}

.table-image {
  width: 50px;
  height: 40px;
  border-radius: 4px;
}

.author-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.admin-tabs {
  margin-top: 20px;
}

.tab-header {
  margin-bottom: 20px;
}

.tab-header h2 {
  font-size: 1.5rem;
  color: #303133;
  margin-bottom: 8px;
}

.tab-header p {
  color: #909399;
  font-size: 0.9rem;
}

.filters {
  margin-bottom: 20px;
  display: flex;
  gap: 15px;
  align-items: center;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.85rem;
}

.user-stats span {
  color: #606266;
}
</style>