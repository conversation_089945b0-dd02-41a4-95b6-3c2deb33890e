{"version": 3, "file": "encoding-factory.js", "sourceRoot": "", "sources": ["../../../src/encoding/encoding-factory.ts"], "names": [], "mappings": ";AAAA,0EAA0E;AAC1E,uCAAuC;;AAEvC,uCAA0D;AAC1D,2CAA8D;AAC9D,2CAA8D;AAC9D,6CAAmE;AACnE,qDAA2E;AAC3E,iDAAuE;AACvE,qDAA6E;AAC7E,2CAA8D;AAC9D,yCAA2D;AAC3D,2DAAoF;AAGpF,yCAAwC;AACxC,qCAAkC;AAClC,yEAAiE;AAGjE,EAAE;AACF,YAAY;AACZ,EAAE;AAEF,iCAAiC;AAEjC,EAAE;AACF,2CAA2C;AAC3C,oCAAoC;AACpC,EAAE;AAEF,EAAE;AACF,iBAAiB;AACjB,EAAE;AAEF,mCAAmC;AAEnC,EAAE;AACF,eAAe;AACf,EAAE;AAEF,iCAAiC;AAEjC,EAAE;AACF,aAAa;AACb,EAAE;AAEF,+BAA+B;AAE/B,IAAM,eAAe,GAAG,8CAAkB,EAAE,CAAC;AAwB7C,8DAA8D;AAC9D,kEAAkE;AAClE,iCAAiC;AAEjC,kEAAkE;AAClE,iCAAiC;AAE/B,EAAE;AACF,mCAAmC;AACnC,EAAE;AAEF,2BAA2B;AAE3B,2BAA2B;AAEhB,QAAA,QAAQ,GAAa;IAEhC,YAAY;IAEZ,sBAAsB;IAEtB,sBAAsB;IAEtB,wCAAwC;IACxC,OAAO,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,mBAAW,CAAC,OAAO,CAAC,EAAxB,CAAwB;IAEnE,EAAE;IACF,uDAAuD;IACvD,EAAE;IAEF,WAAW;IAEX,qBAAqB;IACrB,sCAAsC;IAEtC,qBAAqB;IACrB,4DAA4D;IAC5D,wCAAwC;IACxC,KAAK,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,wBAAc,CAAC,OAAO,EAAE,IAAI,CAAC,EAAjC,CAAiC;IAE1E,eAAe;IACf,yBAAyB;IAEzB,yBAAyB;IAEzB,wCAAwC;IACxC,SAAS,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,wBAAc,CAAC,OAAO,CAAC,EAA3B,CAA2B;IAIxE,EAAE;IACF,wDAAwD;IACxD,EAAE;IAEF,YAAY;IAEZ,sBAAsB;IAEtB,sBAAsB;IAEtB,wCAAwC;IACxC,MAAM,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,kBAAW,CAAC,OAAO,CAAC,EAAxB,CAAwB;IAGlE,EAAE;IACF,2CAA2C;IAC3C,EAAE;IAEF,cAAc;IAEd,wBAAwB;IAExB,wBAAwB;IAExB,wCAAwC;IACxC,QAAQ,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,qBAAY,CAAC,OAAO,CAAC,EAAzB,CAAyB;IAErE,mBAAmB;IAEnB,6BAA6B;IAE7B,6BAA6B;IAE7B,wCAAwC;IACxC,aAAa,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,8BAAgB,CAAC,OAAO,CAAC,EAA7B,CAA6B;IAE9E,iBAAiB;IAEjB,2BAA2B;IAE3B,2BAA2B;IAE3B,wCAAwC;IACxC,WAAW,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,2BAAe,CAAC,OAAO,CAAC,EAA5B,CAA4B;IAE3E,EAAE;IACF,yCAAyC;IACzC,EAAE;IAEF,cAAc;IAEd,wBAAwB;IAExB,wBAAwB;IAExB,wCAAwC;IACxC,QAAQ,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,qBAAY,CAAC,OAAO,CAAC,EAAzB,CAAyB;IAGrE,EAAE;IACF,qCAAqC;IACrC,EAAE;IAEF,mBAAmB;IAEnB,qCAAqC;IAErC,uDAAuD;IAEvD,+BAA+B;IAE/B,+BAA+B;IAE/B,gBAAgB;IAChB,0BAA0B;IAC1B,wCAAwC;IACxC,UAAU,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,qBAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EAA/B,CAA+B;IAC7E,0BAA0B;IAE1B,gBAAgB;IAChB,0BAA0B;IAC1B,wCAAwC;IACxC,UAAU,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,qBAAY,CAAC,KAAK,EAAE,OAAO,CAAC,EAAhC,CAAgC;IAC9E,0BAA0B;IAE1B,sBAAsB;IAEtB,gCAAgC;IAEhC,gCAAgC;IAEhC,wCAAwC;IACxC,gBAAgB,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,oCAAmB,CAAC,OAAO,CAAC,EAAhC,CAAgC;CACrF,CAAA;AAIY,QAAA,QAAQ,GAAa;IAChC,wCAAwC;IACxC,OAAO,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,mBAAW,CAAC,OAAO,CAAC,EAAxB,CAAwB;IACnE,wCAAwC;IACxC,KAAK,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,wBAAc,CAAC,OAAO,CAAC,EAA3B,CAA2B;IACpE,wCAAwC;IACxC,SAAS,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,wBAAc,CAAC,OAAO,CAAC,EAA3B,CAA2B;IACxE,wCAAwC;IACxC,MAAM,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,kBAAW,CAAC,OAAO,CAAC,EAAxB,CAAwB;IAClE,wCAAwC;IACxC,QAAQ,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,qBAAY,CAAC,OAAO,CAAC,EAAzB,CAAyB;IACrE,wCAAwC;IACxC,aAAa,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,8BAAgB,CAAC,OAAO,CAAC,EAA7B,CAA6B;IAC9E,wCAAwC;IACxC,WAAW,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,2BAAe,CAAC,OAAO,CAAC,EAA5B,CAA4B;IAC3E,wCAAwC;IACxC,QAAQ,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,qBAAY,CAAC,OAAO,CAAC,EAAzB,CAAyB;IACrE,wCAAwC;IACxC,UAAU,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,qBAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EAA/B,CAA+B;IAC7E,wCAAwC;IACxC,UAAU,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,qBAAY,CAAC,KAAK,EAAE,OAAO,CAAC,EAAhC,CAAgC;IAC9E,wCAAwC;IACxC,gBAAgB,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,oCAAmB,CAAC,OAAO,CAAC,EAAhC,CAAgC;CACrF,CAAA;AAGD,IAAI,eAAe,EAAE;IACnB,qBAAS,CAAC,OAAO,CAAC,UAAU,QAAQ;QAClC,IAAI,QAAQ,CAAC,OAAO,KAAK,8BAA8B;YACrD,OAAO;QACT,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ;YAC3C,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,IAAM,GAAG,GAAG,eAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACtC,wCAAwC;YACxC,gBAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,OAA4B;gBACrD,OAAO,IAAI,+BAAiB,CAAC,GAAe,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC,CAAC;YACF,wCAAwC;YACxC,gBAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,OAA4B;gBACrD,OAAO,IAAI,+BAAiB,CAAC,GAAe,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;CACJ"}