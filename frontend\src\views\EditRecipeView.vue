<template>
  <div class="edit-recipe">
    <div class="page-header">
      <h1>编辑菜谱</h1>
      <p>修改您的菜谱信息</p>
    </div>

    <div v-if="loading" class="loading">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else-if="recipe" class="form-container">
      <!-- 这里复用CreateRecipeView的表单，但是预填充数据 -->
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <!-- 基本信息 -->
        <el-card class="form-section" header="基本信息">
          <el-form-item label="菜谱名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入菜谱名称" />
          </el-form-item>
          
          <el-form-item label="方言名称" prop="dialectName">
            <el-input v-model="form.dialectName" placeholder="请输入方言名称" />
          </el-form-item>
          
          <el-form-item label="方言发音">
            <div class="audio-upload">
              <div v-if="form.dialectAudio" class="audio-preview">
                <audio :src="form.dialectAudio" controls></audio>
                <el-button size="small" @click="form.dialectAudio = ''">删除</el-button>
              </div>
              <el-upload
                v-else
                class="upload-demo"
                :action="audioUploadUrl"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleAudioSuccess"
                :on-error="handleAudioError"
                :before-upload="beforeAudioUpload"
                name="audio"
              >
                <el-button type="primary">
                  <el-icon><Microphone /></el-icon>
                  上传发音
                </el-button>
              </el-upload>
            </div>
          </el-form-item>
          
          <el-form-item label="所属地区" prop="region">
            <el-select v-model="form.region" placeholder="请选择地区" style="width: 100%">
              <el-option label="潮州" value="潮州" />
              <el-option label="汕头" value="汕头" />
              <el-option label="揭阳" value="揭阳" />
              <el-option label="汕尾" value="汕尾" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="菜谱描述">
            <el-input 
              v-model="form.description" 
              type="textarea" 
              :rows="3"
              placeholder="简单介绍这道菜的特色和背景"
            />
          </el-form-item>
        </el-card>

        <!-- 菜谱图片 -->
        <el-card class="form-section" header="菜谱图片">
          <el-form-item>
            <div class="image-upload">
              <div v-if="form.image" class="image-preview">
                <img :src="form.image" alt="菜谱图片" />
                <div class="image-actions">
                  <el-button size="small" @click="form.image = ''">删除</el-button>
                </div>
              </div>
              <el-upload
                v-else
                class="upload-demo"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleImageSuccess"
                :on-error="handleImageError"
                :before-upload="beforeImageUpload"
                name="image"
              >
                <el-button type="primary">
                  <el-icon><Plus /></el-icon>
                  上传图片
                </el-button>
              </el-upload>
            </div>
          </el-form-item>
        </el-card>

        <!-- 食材配料 -->
        <el-card class="form-section" header="食材配料">
          <el-form-item prop="ingredients">
            <div class="ingredients-list">
              <div 
                v-for="(ingredient, index) in form.ingredients" 
                :key="index"
                class="ingredient-item"
              >
                <el-input 
                  v-model="ingredient.name" 
                  placeholder="食材名称"
                  style="width: 200px"
                />
                <el-input 
                  v-model="ingredient.amount" 
                  placeholder="用量"
                  style="width: 150px"
                />
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="removeIngredient(index)"
                  :disabled="form.ingredients.length <= 1"
                >
                  删除
                </el-button>
              </div>
              <el-button type="primary" size="small" @click="addIngredient">
                <el-icon><Plus /></el-icon>
                添加食材
              </el-button>
            </div>
          </el-form-item>
        </el-card>

        <!-- 制作步骤 -->
        <el-card class="form-section" header="制作步骤">
          <el-form-item prop="steps">
            <div class="steps-list">
              <div 
                v-for="(step, index) in form.steps" 
                :key="index"
                class="step-item"
              >
                <div class="step-number">{{ index + 1 }}</div>
                <el-input 
                  v-model="step.description" 
                  type="textarea"
                  :rows="2"
                  :placeholder="`第${index + 1}步制作方法`"
                />
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="removeStep(index)"
                  :disabled="form.steps.length <= 1"
                >
                  删除
                </el-button>
              </div>
              <el-button type="primary" size="small" @click="addStep">
                <el-icon><Plus /></el-icon>
                添加步骤
              </el-button>
            </div>
          </el-form-item>
        </el-card>

        <!-- 其他信息 -->
        <el-card class="form-section" header="其他信息">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="难度等级">
                <el-rate v-model="form.difficulty" :max="5" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="制作时间">
                <el-input v-model="form.cookTime" placeholder="分钟">
                  <template #append>分钟</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="份量">
                <el-input v-model="form.servings" placeholder="人份">
                  <template #append>人份</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 文化传承信息 -->
        <el-card class="form-section" header="文化传承信息（可选）">
          <p class="section-description">
            🏮 作为传承人，您可以分享这道菜背后的文化故事，让更多人了解传统饮食文化的魅力
          </p>

          <el-tabs type="border-card">
            <el-tab-pane label="🏛️ 历史渊源">
              <el-form-item>
                <el-input
                  v-model="form.culturalHistory"
                  type="textarea"
                  :rows="4"
                  placeholder="请介绍这道菜的历史渊源，比如：起源于哪个朝代、有什么历史典故..."
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-tab-pane>

            <el-tab-pane label="🗺️ 地域特色">
              <el-form-item>
                <el-input
                  v-model="form.regionalFeature"
                  type="textarea"
                  :rows="4"
                  placeholder="请介绍这道菜的地域特色，比如：为什么属于这个地方、当地的独特做法..."
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-tab-pane>

            <el-tab-pane label="🎭 文化寓意">
              <el-form-item>
                <el-input
                  v-model="form.culturalMeaning"
                  type="textarea"
                  :rows="4"
                  placeholder="请介绍这道菜的文化寓意，比如：在节日中的意义、象征什么寓意..."
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-tab-pane>

            <el-tab-pane label="👴 传承故事">
              <el-form-item>
                <el-input
                  v-model="form.inheritanceStory"
                  type="textarea"
                  :rows="4"
                  placeholder="请分享您学习这道菜的故事，比如：师父是如何教您的、有什么特别的经历..."
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <!-- 提交按钮 -->
        <div class="form-actions">
          <el-button size="large" @click="$router.back()">取消</el-button>
          <el-button 
            type="primary" 
            size="large" 
            @click="handleSubmit(false)"
            :loading="submitting"
          >
            保存草稿
          </el-button>
          <el-button 
            type="success" 
            size="large" 
            @click="handleSubmit(true)"
            :loading="submitting"
          >
            发布菜谱
          </el-button>
        </div>
      </el-form>
    </div>

    <div v-else class="error">
      <el-result icon="error" title="菜谱不存在" sub-title="您要编辑的菜谱不存在或已被删除">
        <template #extra>
          <el-button type="primary" @click="$router.push('/inheritor/dashboard')">
            返回仪表板
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { getRecipeById, updateRecipe } from '../api/recipes.js'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const formRef = ref(null)
const loading = ref(true)
const submitting = ref(false)
const recipe = ref(null)

// 检查权限
if (!userStore.isLoggedIn || (userStore.user?.role !== 'INHERITOR' && userStore.user?.role !== 'ADMIN')) {
  ElMessage.error('只有传承人和管理员可以编辑菜谱')
  router.push('/')
}

const form = reactive({
  name: '',
  dialectName: '',
  dialectAudio: '',
  region: '',
  description: '',
  image: '',
  ingredients: [{ name: '', amount: '' }],
  steps: [{ description: '' }],
  difficulty: 1,
  cookTime: '',
  servings: '',
  // 文化传承字段
  culturalHistory: '',
  regionalFeature: '',
  culturalMeaning: '',
  inheritanceStory: ''
})

const rules = {
  name: [{ required: true, message: '请输入菜谱名称', trigger: 'blur' }],
  dialectName: [{ required: true, message: '请输入方言名称', trigger: 'blur' }],
  region: [{ required: true, message: '请选择所属地区', trigger: 'change' }]
}

// 上传相关
const uploadUrl = 'http://localhost:3000/api/upload/image'
const audioUploadUrl = 'http://localhost:3000/api/upload/audio'
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${userStore.token}`
}))

// 获取菜谱数据
const fetchRecipe = async () => {
  try {
    loading.value = true
    const response = await getRecipeById(route.params.id)
    recipe.value = response.recipe
    
    // 检查权限
    if (recipe.value.authorId !== userStore.user.id && userStore.user.role !== 'ADMIN') {
      ElMessage.error('您没有权限编辑此菜谱')
      router.push('/inheritor/dashboard')
      return
    }
    
    // 填充表单数据
    Object.assign(form, {
      name: recipe.value.name || '',
      dialectName: recipe.value.dialectName || '',
      dialectAudio: recipe.value.dialectAudio || '',
      region: recipe.value.region || '',
      description: recipe.value.description || '',
      image: recipe.value.image || '',
      ingredients: recipe.value.ingredients ? JSON.parse(recipe.value.ingredients) : [{ name: '', amount: '' }],
      steps: recipe.value.steps ? JSON.parse(recipe.value.steps) : [{ description: '' }],
      difficulty: recipe.value.difficulty || 1,
      cookTime: recipe.value.cookTime?.toString() || '',
      servings: recipe.value.servings?.toString() || '',
      // 文化传承字段
      culturalHistory: recipe.value.culturalHistory || '',
      regionalFeature: recipe.value.regionalFeature || '',
      culturalMeaning: recipe.value.culturalMeaning || '',
      inheritanceStory: recipe.value.inheritanceStory || ''
    })
  } catch (error) {
    console.error('获取菜谱失败:', error)
    ElMessage.error('获取菜谱失败')
  } finally {
    loading.value = false
  }
}

// 表单操作方法（复用CreateRecipeView的逻辑）
const addIngredient = () => {
  form.ingredients.push({ name: '', amount: '' })
}

const removeIngredient = (index) => {
  if (form.ingredients.length > 1) {
    form.ingredients.splice(index, 1)
  }
}

const addStep = () => {
  form.steps.push({ description: '' })
}

const removeStep = (index) => {
  if (form.steps.length > 1) {
    form.steps.splice(index, 1)
  }
}

// 上传处理方法
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleImageSuccess = (response) => {
  console.log('图片上传响应:', response)
  if (response.file && response.file.url) {
    form.image = response.file.url
    ElMessage.success('图片上传成功')
  } else if (response.url) {
    form.image = response.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败：未获取到文件URL')
  }
}

const handleImageError = () => {
  ElMessage.error('图片上传失败')
}

const beforeAudioUpload = (file) => {
  const isAudio = file.type === 'audio/mpeg' || file.type === 'audio/wav' || file.type === 'audio/mp3'
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isAudio) {
    ElMessage.error('上传音频只能是 MP3/WAV 格式!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('上传音频大小不能超过 5MB!')
    return false
  }
  return true
}

const handleAudioSuccess = (response) => {
  console.log('音频上传响应:', response)
  if (response.file && response.file.url) {
    form.dialectAudio = response.file.url
    ElMessage.success('音频上传成功')
  } else if (response.url) {
    form.dialectAudio = response.url
    ElMessage.success('音频上传成功')
  } else {
    ElMessage.error('音频上传失败：未获取到文件URL')
  }
}

const handleAudioError = () => {
  ElMessage.error('音频上传失败')
}

const handleSubmit = async (isPublished = false) => {
  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      ...form,
      isPublished,
      ingredients: JSON.stringify(form.ingredients.filter(item => item.name.trim())),
      steps: JSON.stringify(form.steps.filter(item => item.description.trim())),
      cookTime: form.cookTime ? parseInt(form.cookTime) : null,
      servings: form.servings ? parseInt(form.servings) : null
    }

    console.log('编辑提交的表单数据:', form)
    console.log('编辑提交的最终数据:', submitData)
    await updateRecipe(route.params.id, submitData)
    
    ElMessage.success('菜谱更新成功！')
    router.push('/inheritor/dashboard')
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error(error.error || '更新失败')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  fetchRecipe()
})
</script>

<style scoped>
/* 复用CreateRecipeView的样式 */
.edit-recipe {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #909399;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 20px;
}

.loading, .error {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px;
}

.image-upload {
  text-align: center;
}

.image-preview {
  position: relative;
  display: inline-block;
}

.image-preview img {
  width: 200px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
}

.image-actions {
  position: absolute;
  top: 10px;
  right: 10px;
}

.audio-upload {
  text-align: center;
}

.audio-preview {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}

.ingredients-list, .steps-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ingredient-item {
  display: flex;
  gap: 10px;
  align-items: center;
}

.step-item {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.step-number {
  background: #409eff;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 5px;
}

.form-actions {
  text-align: center;
  padding: 30px 0;
  display: flex;
  gap: 20px;
  justify-content: center;
}
</style>
