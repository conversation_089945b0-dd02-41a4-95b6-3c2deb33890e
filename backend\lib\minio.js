import { Client } from 'minio'
import dotenv from 'dotenv'

dotenv.config()

// MinIO客户端配置
const minioClient = new Client({
  endPoint: process.env.MINIO_ENDPOINT || 'localhost',
  port: parseInt(process.env.MINIO_PORT) || 9000,
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || 'admin',
  secretKey: process.env.MINIO_SECRET_KEY || 'admin1234'
})

// 存储桶名称
const BUCKET_NAME = process.env.MINIO_BUCKET || 'digital-hometown'

// 初始化存储桶
export async function initializeBucket() {
  try {
    const exists = await minioClient.bucketExists(BUCKET_NAME)
    if (!exists) {
      await minioClient.makeBucket(BUCKET_NAME, 'us-east-1')
      console.log(`✅ MinIO存储桶 "${BUCKET_NAME}" 创建成功`)
      
      // 设置存储桶策略为公开读取
      const policy = {
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Principal: { AWS: ['*'] },
            Action: ['s3:GetObject'],
            Resource: [`arn:aws:s3:::${BUCKET_NAME}/*`]
          }
        ]
      }
      
      await minioClient.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy))
      console.log(`✅ MinIO存储桶 "${BUCKET_NAME}" 策略设置完成`)
    } else {
      console.log(`✅ MinIO存储桶 "${BUCKET_NAME}" 已存在`)
    }
  } catch (error) {
    console.error('❌ MinIO存储桶初始化失败:', error.message)
    throw error
  }
}

// 上传文件到MinIO
export async function uploadToMinio(file, folder = '') {
  try {
    const fileName = `${folder}${Date.now()}-${Math.round(Math.random() * 1E9)}-${file.originalname}`
    const objectName = folder ? `${folder}/${fileName}` : fileName
    
    // 上传文件
    await minioClient.putObject(BUCKET_NAME, objectName, file.buffer, file.size, {
      'Content-Type': file.mimetype
    })
    
    // 生成访问URL
    const baseUrl = `${process.env.MINIO_USE_SSL === 'true' ? 'https' : 'http'}://${process.env.MINIO_ENDPOINT || 'localhost'}:${process.env.MINIO_PORT || 9000}`
    const fileUrl = `${baseUrl}/${BUCKET_NAME}/${objectName}`
    
    return {
      fileName: objectName,
      originalName: file.originalname,
      size: file.size,
      url: fileUrl,
      bucket: BUCKET_NAME
    }
  } catch (error) {
    console.error('MinIO上传失败:', error)
    throw new Error('文件上传到MinIO失败')
  }
}

// 删除MinIO中的文件
export async function deleteFromMinio(objectName) {
  try {
    await minioClient.removeObject(BUCKET_NAME, objectName)
    console.log(`✅ 文件 ${objectName} 已从MinIO删除`)
    return true
  } catch (error) {
    console.error('MinIO删除文件失败:', error)
    throw new Error('从MinIO删除文件失败')
  }
}

// 获取文件访问URL
export function getMinioFileUrl(objectName) {
  const baseUrl = `${process.env.MINIO_USE_SSL === 'true' ? 'https' : 'http'}://${process.env.MINIO_ENDPOINT || 'localhost'}:${process.env.MINIO_PORT || 9000}`
  return `${baseUrl}/${BUCKET_NAME}/${objectName}`
}

// 测试MinIO连接
export async function testMinioConnection() {
  try {
    await minioClient.listBuckets()
    console.log('✅ MinIO连接测试成功')
    return true
  } catch (error) {
    console.error('❌ MinIO连接测试失败:', error.message)
    return false
  }
}

export { minioClient, BUCKET_NAME }
export default minioClient
