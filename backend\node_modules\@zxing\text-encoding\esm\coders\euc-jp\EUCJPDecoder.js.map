{"version": 3, "file": "EUCJPDecoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/euc-jp/EUCJPDecoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACxE,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAEnD;;;;GAIG;AACH;IAOE,sBAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAE3B,yDAAyD;QACzD,sDAAsD;QACtD,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,GAAG,KAAK;YACtD,qBAAqB,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC/C,CAAC;IAED;;;;;;OAMG;IACH,8BAAO,GAAP,UAAQ,MAAc,EAAE,IAAY;QAClC,+DAA+D;QAC/D,yCAAyC;QACzC,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YACtD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjC;QAED,8DAA8D;QAC9D,YAAY;QACZ,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI;YACpD,OAAO,QAAQ,CAAC;QAElB,6DAA6D;QAC7D,6DAA6D;QAC7D,6CAA6C;QAC7C,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;YACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;SAC7B;QAED,6DAA6D;QAC7D,gEAAgE;QAChE,gCAAgC;QAChC,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;YACzD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,IAAI,CAAC;SACb;QAED,8DAA8D;QAC9D,+CAA+C;QAC/C,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;YAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YAEvB,6BAA6B;YAC7B,IAAI,UAAU,GAAW,IAAI,CAAC;YAE9B,0DAA0D;YAC1D,8DAA8D;YAC9D,4DAA4D;YAC5D,wDAAwD;YACxD,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;gBAC1D,UAAU,GAAG,iBAAiB,CAC5B,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,EAClC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAa,CAAC,CAAC;aACxE;YAED,oCAAoC;YACpC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAEhC,0DAA0D;YAC1D,0BAA0B;YAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBAC5B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEvB,0CAA0C;YAC1C,IAAI,UAAU,KAAK,IAAI;gBACrB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAElC,oDAAoD;YACpD,OAAO,UAAU,CAAC;SACnB;QAED,+DAA+D;QAC/D,WAAW;QACX,IAAI,WAAW,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI,CAAC;QAEd,0DAA0D;QAC1D,0DAA0D;QAC1D,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;YAC/D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,IAAI,CAAC;SACb;QAED,mBAAmB;QACnB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IACH,mBAAC;AAAD,CAAC,AAvGD,IAuGC"}