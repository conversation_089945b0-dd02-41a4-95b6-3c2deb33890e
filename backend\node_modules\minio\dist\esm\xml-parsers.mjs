/*
 * MinIO Javascript Library for Amazon S3 Compatible Cloud Storage, (C) 2015 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import * as errors from "./errors.mjs";
import { parseXml, sanitizeETag, sanitizeObjectKey, toArray } from "./internal/helper.mjs";

// parse XML response for bucket notification
export function parseBucketNotification(xml) {
  var result = {
    TopicConfiguration: [],
    QueueConfiguration: [],
    CloudFunctionConfiguration: []
  };
  // Parse the events list
  var genEvents = function (events) {
    var result = [];
    if (events) {
      toArray(events).forEach(s3event => {
        result.push(s3event);
      });
    }
    return result;
  };
  // Parse all filter rules
  var genFilterRules = function (filters) {
    var result = [];
    if (filters) {
      filters = toArray(filters);
      if (filters[0].S3Key) {
        filters[0].S3Key = toArray(filters[0].S3Key);
        if (filters[0].S3Key[0].FilterRule) {
          toArray(filters[0].S3Key[0].FilterRule).forEach(rule => {
            var Name = toArray(rule.Name)[0];
            var Value = toArray(rule.Value)[0];
            result.push({
              Name,
              Value
            });
          });
        }
      }
    }
    return result;
  };
  var xmlobj = parseXml(xml);
  xmlobj = xmlobj.NotificationConfiguration;

  // Parse all topic configurations in the xml
  if (xmlobj.TopicConfiguration) {
    toArray(xmlobj.TopicConfiguration).forEach(config => {
      var Id = toArray(config.Id)[0];
      var Topic = toArray(config.Topic)[0];
      var Event = genEvents(config.Event);
      var Filter = genFilterRules(config.Filter);
      result.TopicConfiguration.push({
        Id,
        Topic,
        Event,
        Filter
      });
    });
  }
  // Parse all topic configurations in the xml
  if (xmlobj.QueueConfiguration) {
    toArray(xmlobj.QueueConfiguration).forEach(config => {
      var Id = toArray(config.Id)[0];
      var Queue = toArray(config.Queue)[0];
      var Event = genEvents(config.Event);
      var Filter = genFilterRules(config.Filter);
      result.QueueConfiguration.push({
        Id,
        Queue,
        Event,
        Filter
      });
    });
  }
  // Parse all QueueConfiguration arrays
  if (xmlobj.CloudFunctionConfiguration) {
    toArray(xmlobj.CloudFunctionConfiguration).forEach(config => {
      var Id = toArray(config.Id)[0];
      var CloudFunction = toArray(config.CloudFunction)[0];
      var Event = genEvents(config.Event);
      var Filter = genFilterRules(config.Filter);
      result.CloudFunctionConfiguration.push({
        Id,
        CloudFunction,
        Event,
        Filter
      });
    });
  }
  return result;
}

// parse XML response for list objects v2 in a bucket
export function parseListObjectsV2(xml) {
  var result = {
    objects: [],
    isTruncated: false
  };
  var xmlobj = parseXml(xml);
  if (!xmlobj.ListBucketResult) {
    throw new errors.InvalidXMLError('Missing tag: "ListBucketResult"');
  }
  xmlobj = xmlobj.ListBucketResult;
  if (xmlobj.IsTruncated) {
    result.isTruncated = xmlobj.IsTruncated;
  }
  if (xmlobj.NextContinuationToken) {
    result.nextContinuationToken = xmlobj.NextContinuationToken;
  }
  if (xmlobj.Contents) {
    toArray(xmlobj.Contents).forEach(content => {
      var name = sanitizeObjectKey(toArray(content.Key)[0]);
      var lastModified = new Date(content.LastModified);
      var etag = sanitizeETag(content.ETag);
      var size = content.Size;
      result.objects.push({
        name,
        lastModified,
        etag,
        size
      });
    });
  }
  if (xmlobj.CommonPrefixes) {
    toArray(xmlobj.CommonPrefixes).forEach(commonPrefix => {
      result.objects.push({
        prefix: sanitizeObjectKey(toArray(commonPrefix.Prefix)[0]),
        size: 0
      });
    });
  }
  return result;
}

// parse XML response for list objects v2 with metadata in a bucket
export function parseListObjectsV2WithMetadata(xml) {
  var result = {
    objects: [],
    isTruncated: false
  };
  var xmlobj = parseXml(xml);
  if (!xmlobj.ListBucketResult) {
    throw new errors.InvalidXMLError('Missing tag: "ListBucketResult"');
  }
  xmlobj = xmlobj.ListBucketResult;
  if (xmlobj.IsTruncated) {
    result.isTruncated = xmlobj.IsTruncated;
  }
  if (xmlobj.NextContinuationToken) {
    result.nextContinuationToken = xmlobj.NextContinuationToken;
  }
  if (xmlobj.Contents) {
    toArray(xmlobj.Contents).forEach(content => {
      var name = sanitizeObjectKey(content.Key);
      var lastModified = new Date(content.LastModified);
      var etag = sanitizeETag(content.ETag);
      var size = content.Size;
      var metadata;
      if (content.UserMetadata != null) {
        metadata = toArray(content.UserMetadata)[0];
      } else {
        metadata = null;
      }
      result.objects.push({
        name,
        lastModified,
        etag,
        size,
        metadata
      });
    });
  }
  if (xmlobj.CommonPrefixes) {
    toArray(xmlobj.CommonPrefixes).forEach(commonPrefix => {
      result.objects.push({
        prefix: sanitizeObjectKey(toArray(commonPrefix.Prefix)[0]),
        size: 0
      });
    });
  }
  return result;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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