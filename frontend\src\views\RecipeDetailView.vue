<template>
  <div class="recipe-detail">
    <!-- 英雄区域 -->
    <div class="recipe-hero" v-if="recipe">
      <div class="hero-background">
        <img :src="recipe.image || '/placeholder-dish.jpg'" :alt="recipe.name" class="hero-bg-image" />
        <div class="hero-overlay"></div>
      </div>
      <div class="hero-content">
        <div class="container">
          <div class="hero-info">
            <div class="recipe-badge" v-if="recipe.author?.role === 'INHERITOR'">
              <span>传承人作品</span>
            </div>
            <h1 class="recipe-name">{{ recipe.name }}</h1>
            <h2 class="recipe-dialect">{{ recipe.dialectName }}</h2>
            <p class="recipe-region">
              <el-icon><Location /></el-icon>
              {{ recipe.region }}
            </p>

            <!-- 音频播放 -->
            <div class="audio-section" v-if="recipe.dialectAudio">
              <div class="audio-label">
                <el-icon><Microphone /></el-icon>
                方言发音
              </div>
              <audio controls class="audio-player">
                <source :src="recipe.dialectAudio" type="audio/mpeg">
                您的浏览器不支持音频播放
              </audio>
            </div>

            <div class="recipe-meta-cards">
              <div class="meta-card" v-if="recipe.difficulty">
                <div class="meta-icon">
                  <el-icon><Star /></el-icon>
                </div>
                <div class="meta-content">
                  <div class="meta-label">难度</div>
                  <div class="meta-value">{{ getDifficultyText(recipe.difficulty) }}</div>
                </div>
              </div>
              <div class="meta-card" v-if="recipe.cookTime">
                <div class="meta-icon">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="meta-content">
                  <div class="meta-label">时间</div>
                  <div class="meta-value">{{ recipe.cookTime }}分钟</div>
                </div>
              </div>
              <div class="meta-card" v-if="recipe.servings">
                <div class="meta-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="meta-content">
                  <div class="meta-label">份量</div>
                  <div class="meta-value">{{ recipe.servings }}人份</div>
                </div>
              </div>
              <div class="meta-card">
                <div class="meta-icon">
                  <el-icon><View /></el-icon>
                </div>
                <div class="meta-content">
                  <div class="meta-label">浏览</div>
                  <div class="meta-value">{{ recipe.viewCount }}</div>
                </div>
              </div>
            </div>

            <div class="recipe-author-card">
              <div class="author-info">
                <span class="author-label">作者</span>
                <span class="author-name">{{ recipe.author?.username }}</span>
                <span class="author-role" v-if="recipe.author?.role === 'INHERITOR'">传承人</span>
              </div>
              <div class="recipe-actions">
                <FavoriteButton
                  :recipe-id="recipe.id"
                  @favorite-changed="handleFavoriteChanged"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" v-loading="loading">
      <div class="container" v-if="recipe">
        <div class="content-grid">

          <!-- 左侧内容 -->
          <div class="content-left">
            <!-- 食材卡片 -->
            <div class="content-card ingredients-card">
              <h3 class="card-title">
                <el-icon><ShoppingCart /></el-icon>
                所需食材
              </h3>
              <div class="ingredients-grid">
                <div
                  v-for="(ingredient, index) in parsedIngredients"
                  :key="index"
                  class="ingredient-item"
                >
                  <div class="ingredient-name">{{ ingredient.name }}</div>
                  <div class="ingredient-amount">{{ ingredient.amount }}</div>
                </div>
              </div>
            </div>

            <!-- 制作步骤卡片 -->
            <div class="content-card steps-card">
              <h3 class="card-title">
                <el-icon><Document /></el-icon>
                制作步骤
              </h3>
              <div class="steps-list">
                <div
                  v-for="(step, index) in parsedSteps"
                  :key="index"
                  class="step-item"
                >
                  <div class="step-number">{{ index + 1 }}</div>
                  <div class="step-content">{{ step.description }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧内容 -->
          <div class="content-right">
            <!-- 菜谱介绍 -->
            <div class="content-card description-card" v-if="recipe.description">
              <h3 class="card-title">
                <el-icon><InfoFilled /></el-icon>
                菜谱介绍
              </h3>
              <div class="description-content">
                <p>{{ recipe.description }}</p>
              </div>
            </div>

            <!-- 文化传承信息 -->
            <div class="content-card cultural-card" v-if="hasCulturalContent">
              <h3 class="card-title">
                <el-icon><Document /></el-icon>
                文化传承
              </h3>
              <div class="cultural-content">
                <el-tabs type="border-card" class="cultural-tabs">
                  <el-tab-pane
                    v-if="recipe.culturalHistory"
                    label="🏛️ 历史渊源"
                  >
                    <div class="cultural-text">
                      <p>{{ recipe.culturalHistory }}</p>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane
                    v-if="recipe.regionalFeature"
                    label="🗺️ 地域特色"
                  >
                    <div class="cultural-text">
                      <p>{{ recipe.regionalFeature }}</p>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane
                    v-if="recipe.culturalMeaning"
                    label="🎭 文化寓意"
                  >
                    <div class="cultural-text">
                      <p>{{ recipe.culturalMeaning }}</p>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane
                    v-if="recipe.inheritanceStory"
                    label="👴 传承故事"
                  >
                    <div class="cultural-text">
                      <p>{{ recipe.inheritanceStory }}</p>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </div>
        </div>

        <!-- 评论区 -->
        <div class="comments-section">
          <div class="container">
            <CommentSection
              :recipe-id="recipe.id"
              @comment-added="handleCommentAdded"
              @comment-updated="handleCommentUpdated"
              @comment-deleted="handleCommentDeleted"
            />
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="!loading" class="not-found">
      <div class="container">
        <el-result
          icon="warning"
          title="菜谱不存在"
          sub-title="您访问的菜谱可能已被删除或不存在"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/recipes')">
              返回菜谱列表
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getRecipeById } from '../api/recipes.js'
import { ElMessage } from 'element-plus'
import {
  Location,
  Star,
  Clock,
  User,
  View,
  ShoppingCart,
  Document,
  InfoFilled,
  Microphone
} from '@element-plus/icons-vue'
import FavoriteButton from '../components/FavoriteButton.vue'
import CommentSection from '../components/CommentSection.vue'

const route = useRoute()
const loading = ref(false)
const recipe = ref(null)

// 解析JSON格式的食材和步骤
const parsedIngredients = computed(() => {
  if (!recipe.value?.ingredients) return []
  try {
    return typeof recipe.value.ingredients === 'string'
      ? JSON.parse(recipe.value.ingredients)
      : recipe.value.ingredients
  } catch (error) {
    console.error('解析食材数据失败:', error)
    return []
  }
})

const parsedSteps = computed(() => {
  if (!recipe.value?.steps) return []
  try {
    return typeof recipe.value.steps === 'string'
      ? JSON.parse(recipe.value.steps)
      : recipe.value.steps
  } catch (error) {
    console.error('解析步骤数据失败:', error)
    return []
  }
})

const getDifficultyText = (difficulty) => {
  const texts = ['', '简单', '容易', '中等', '困难', '专家']
  return texts[difficulty] || '未知'
}



// 判断是否有文化传承内容
const hasCulturalContent = computed(() => {
  if (!recipe.value) return false
  return !!(
    recipe.value.culturalHistory ||
    recipe.value.regionalFeature ||
    recipe.value.culturalMeaning ||
    recipe.value.inheritanceStory
  )
})

const fetchRecipe = async () => {
  try {
    loading.value = true
    const response = await getRecipeById(route.params.id)
    recipe.value = response.recipe
  } catch (error) {
    console.error('获取菜谱详情失败:', error)
    ElMessage.error('获取菜谱详情失败')
  } finally {
    loading.value = false
  }
}

const handleFavoriteChanged = (data) => {
  // 可以在这里更新本地的收藏数量等
  console.log('收藏状态改变:', data)
}

const handleCommentAdded = () => {
  // 评论添加后可以刷新菜谱信息以更新评论数量
  fetchRecipe()
}

const handleCommentUpdated = () => {
  // 评论更新后的处理
  console.log('评论已更新')
}

const handleCommentDeleted = () => {
  // 评论删除后可以刷新菜谱信息以更新评论数量
  fetchRecipe()
}

onMounted(() => {
  fetchRecipe()
})
</script>

<style scoped>
.recipe-detail {
  min-height: 100vh;
  background: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 英雄区域样式 */
.recipe-hero {
  position: relative;
  height: 60vh;
  min-height: 500px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

.hero-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  color: white;
}

.hero-info {
  width: 100%;
  max-width: 600px;
}

.recipe-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 8px 16px;
  margin-bottom: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.recipe-name {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 12px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  line-height: 1.1;
}

.recipe-dialect {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
}

.recipe-region {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.8);
}

.audio-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.audio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  margin-bottom: 12px;
  font-size: 1rem;
}

.audio-player {
  width: 100%;
  height: 40px;
  border-radius: 8px;
}

.recipe-meta-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.meta-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.meta-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.meta-icon {
  font-size: 1.5rem;
  margin-bottom: 8px;
  opacity: 0.8;
}

.meta-label {
  font-size: 0.85rem;
  opacity: 0.7;
  margin-bottom: 4px;
}

.meta-value {
  font-size: 1rem;
  font-weight: 600;
}

.recipe-author-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-label {
  font-size: 0.9rem;
  opacity: 0.7;
}

.author-name {
  font-weight: 600;
  font-size: 1rem;
}

.author-role {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 主要内容区域 */
.main-content {
  background: #f8fafc;
  padding: 60px 0;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: start;
}

.content-left,
.content-right {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.content-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.4rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.card-title .el-icon {
  font-size: 1.5rem;
  color: #667eea;
}

/* 食材卡片 */
.ingredients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.ingredient-item {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.ingredient-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.ingredient-name {
  display: block;
  font-weight: 600;
  color: #334155;
  margin-bottom: 4px;
  font-size: 1rem;
}

.ingredient-amount {
  display: block;
  color: #64748b;
  font-size: 0.9rem;
}

/* 制作步骤 */
.steps-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.step-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.step-number {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
}

.step-content {
  flex: 1;
  line-height: 1.6;
  color: #334155;
  font-size: 1rem;
}

/* 描述内容 */
.description-content {
  line-height: 1.8;
  color: #475569;
  font-size: 1rem;
}

.description-content p {
  margin: 0;
}

/* 文化传承 */
.cultural-tabs {
  border: none;
}

.cultural-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.cultural-tabs :deep(.el-tabs__nav-wrap) {
  background: #f8fafc;
  border-radius: 12px;
  padding: 4px;
}

.cultural-tabs :deep(.el-tabs__item) {
  border: none;
  border-radius: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;
}

.cultural-tabs :deep(.el-tabs__item.is-active) {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cultural-text {
  line-height: 1.8;
  color: #475569;
  font-size: 1rem;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.cultural-text p {
  margin: 0;
}

/* 评论区 */
.comments-section {
  background: white;
  padding: 60px 0;
  border-top: 1px solid #e2e8f0;
}

/* 错误页面 */
.not-found {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .recipe-meta-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .recipe-hero {
    height: 50vh;
    min-height: 400px;
  }

  .recipe-name {
    font-size: 2.2rem;
  }

  .recipe-dialect {
    font-size: 1.2rem;
  }

  .content-card {
    padding: 20px;
  }

  .ingredients-grid {
    grid-template-columns: 1fr;
  }

  .recipe-meta-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .meta-card {
    padding: 12px;
  }

  .recipe-author-card {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .step-item {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .step-number {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .recipe-name {
    font-size: 1.8rem;
  }

  .recipe-meta-cards {
    grid-template-columns: 1fr;
  }

  .main-content {
    padding: 40px 0;
  }

  .content-left,
  .content-right {
    gap: 20px;
  }
}
</style>
