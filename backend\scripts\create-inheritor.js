import bcrypt from 'bcryptjs';
import prisma from '../lib/db.js';

async function createInheritor() {
  try {
    // 检查是否已存在传承人账户
    const existingInheritor = await prisma.user.findFirst({
      where: {
        role: 'INHERITOR'
      }
    });

    if (existingInheritor) {
      console.log('传承人账户已存在:', existingInheritor.username);
      return;
    }

    // 创建传承人账户
    const hashedPassword = await bcrypt.hash('inheritor123', 10);
    
    const inheritor = await prisma.user.create({
      data: {
        username: '川菜传承人',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'INHERITOR'
      }
    });

    console.log('传承人账户创建成功:');
    console.log('用户名: 川菜传承人');
    console.log('邮箱: <EMAIL>');
    console.log('密码: inheritor123');
    console.log('角色: INHERITOR');
    
  } catch (error) {
    console.error('创建传承人账户失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createInheritor();
