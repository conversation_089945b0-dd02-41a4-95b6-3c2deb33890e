<template>
  <div class="profile-view">
    <div class="container">
      <div class="profile-header">
        <h1>个人中心</h1>
      </div>

      <div class="profile-content">
        <!-- 个人信息卡片 -->
        <div class="profile-card">
          <div class="profile-info">
            <div class="avatar-section">
              <el-avatar :size="100" :src="userStore.user?.avatar">
                {{ userStore.user?.username?.charAt(0) }}
              </el-avatar>
              <el-button type="text" @click="showAvatarDialog = true">
                <el-icon><Camera /></el-icon>
                更换头像
              </el-button>
            </div>

            <div class="user-details">
              <h2>{{ userStore.user?.username }}</h2>
              <p class="user-role">
                <el-tag :type="getRoleType(userStore.user?.role)">
                  {{ getRoleText(userStore.user?.role) }}
                </el-tag>
              </p>

              <!-- 传承人申请状态提示 -->
              <div v-if="applicationStatus && userStore.user?.role === 'USER'" class="application-status">
                <el-alert
                  :title="`传承人申请状态：${getApplicationStatusText(applicationStatus.status)}`"
                  :type="getApplicationStatusType(applicationStatus.status)"
                  :closable="false"
                  show-icon
                >
                  <template v-if="applicationStatus.status === 'PENDING'">
                    <p>您的传承人申请正在审核中，请耐心等待管理员审核。</p>
                    <p class="apply-time">申请时间：{{ formatDate(applicationStatus.appliedAt) }}</p>
                  </template>
                  <template v-else-if="applicationStatus.status === 'APPROVED'">
                    <p>恭喜！您的传承人申请已通过审核。请重新登录以获得传承人权限。</p>
                    <p class="review-time">审核时间：{{ formatDate(applicationStatus.reviewedAt) }}</p>
                  </template>
                  <template v-else-if="applicationStatus.status === 'REJECTED'">
                    <p>很抱歉，您的传承人申请未通过审核。</p>
                    <p v-if="applicationStatus.reviewComment" class="review-comment">
                      审核意见：{{ applicationStatus.reviewComment }}
                    </p>
                    <p class="review-time">审核时间：{{ formatDate(applicationStatus.reviewedAt) }}</p>
                  </template>
                </el-alert>
              </div>

              <p class="user-email">{{ userStore.user?.email || '未设置邮箱' }}</p>
              <p class="join-date">
                加入时间：{{ formatDate(userStore.user?.createdAt) }}
              </p>
            </div>

            <div class="user-stats">
              <div class="stat-item">
                <span class="stat-number">{{ userStore.user?._count?.recipes || 0 }}</span>
                <span class="stat-label">创建菜谱</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ userStore.user?._count?.favorites || 0 }}</span>
                <span class="stat-label">收藏菜谱</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ userStore.user?._count?.comments || 0 }}</span>
                <span class="stat-label">评论数量</span>
              </div>
            </div>

            <div class="profile-actions">
              <el-button type="primary" @click="showEditDialog = true">
                <el-icon><Edit /></el-icon>
                编辑资料
              </el-button>
            </div>
          </div>
        </div>

        <!-- 功能导航 -->
        <div class="feature-nav">
          <div class="nav-item" @click="activeTab = 'favorites'">
            <el-icon><Star /></el-icon>
            <span>我的收藏</span>
          </div>
          <div class="nav-item" @click="activeTab = 'recipes'">
            <el-icon><Document /></el-icon>
            <span>我的菜谱</span>
          </div>
          <div class="nav-item" @click="activeTab = 'comments'">
            <el-icon><ChatDotRound /></el-icon>
            <span>我的评论</span>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
          <div v-if="activeTab === 'favorites'" class="tab-content">
            <h3>我的收藏</h3>
            <div v-loading="favoritesLoading">
              <div v-if="favoriteRecipes.length === 0 && !favoritesLoading" class="empty-state">
                <el-empty description="还没有收藏任何菜谱" />
              </div>
              <div v-else class="recipes-grid">
                <div
                  v-for="recipe in favoriteRecipes"
                  :key="recipe.id"
                  class="recipe-card"
                  @click="$router.push(`/recipes/${recipe.id}`)"
                >
                  <div class="recipe-image">
                    <img :src="recipe.image || '/placeholder-dish.jpg'" :alt="recipe.name" />
                  </div>
                  <div class="recipe-info">
                    <h4 class="recipe-name">{{ recipe.name }}</h4>
                    <p class="recipe-dialect">{{ recipe.dialectName }}</p>
                    <p class="recipe-region">{{ recipe.region }}</p>
                    <div class="recipe-stats">
                      <span><el-icon><View /></el-icon> {{ recipe.viewCount }}</span>
                      <span><el-icon><Star /></el-icon> {{ recipe._count?.favorites || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="pagination" v-if="favoritesPagination.total > 0">
                <el-pagination
                  v-model:current-page="favoritesPagination.page"
                  v-model:page-size="favoritesPagination.limit"
                  :total="favoritesPagination.total"
                  layout="total, prev, pager, next"
                  @current-change="handleFavoritesPageChange"
                />
              </div>
            </div>
          </div>
          <div v-if="activeTab === 'recipes'" class="tab-content">
            <h3>我的菜谱</h3>
            <p>我的菜谱功能开发中...</p>
          </div>
          <div v-if="activeTab === 'comments'" class="tab-content">
            <h3>我的评论</h3>
            <div v-loading="commentsLoading">
              <div v-if="userComments.length === 0 && !commentsLoading" class="empty-state">
                <el-empty description="还没有发表任何评论" />
              </div>
              <div v-else class="comments-list">
                <div
                  v-for="comment in userComments"
                  :key="comment.id"
                  class="user-comment-item"
                >
                  <div class="comment-recipe">
                    <img
                      :src="comment.recipe?.image || '/placeholder-dish.jpg'"
                      :alt="comment.recipe?.name"
                      class="recipe-thumb"
                      @click="$router.push(`/recipes/${comment.recipe?.id}`)"
                    />
                    <div class="recipe-info">
                      <h4 @click="$router.push(`/recipes/${comment.recipe?.id}`)">
                        {{ comment.recipe?.name }}
                      </h4>
                      <p>{{ comment.recipe?.dialectName }}</p>
                    </div>
                  </div>
                  <div class="comment-details">
                    <div class="comment-meta">
                      <el-rate
                        v-if="comment.rating"
                        v-model="comment.rating"
                        disabled
                        size="small"
                      />
                      <span class="comment-time">{{ formatDate(comment.createdAt) }}</span>
                    </div>
                    <div class="comment-content">{{ comment.content }}</div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="pagination" v-if="commentsPagination.total > 0">
                <el-pagination
                  v-model:current-page="commentsPagination.page"
                  v-model:page-size="commentsPagination.limit"
                  :total="commentsPagination.total"
                  layout="total, prev, pager, next"
                  @current-change="handleCommentsPageChange"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑资料对话框 -->
      <el-dialog v-model="showEditDialog" title="编辑个人资料" width="500px">
        <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="80px">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="editForm.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="editForm.email" placeholder="请输入邮箱（可选）" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showEditDialog = false">取消</el-button>
            <el-button type="primary" @click="handleUpdateProfile" :loading="updating">
              保存
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 更换头像对话框 -->
      <el-dialog v-model="showAvatarDialog" title="更换头像" width="400px">
        <div class="avatar-upload">
          <el-upload
            class="avatar-uploader"
            action=""
            :http-request="handleAvatarUpload"
            :show-file-list="false"
            accept="image/*"
          >
            <el-avatar v-if="newAvatar" :size="150" :src="newAvatar" />
            <el-avatar v-else-if="userStore.user?.avatar" :size="150" :src="userStore.user.avatar" />
            <div v-else class="avatar-placeholder">
              <el-icon><Plus /></el-icon>
              <div>点击上传头像</div>
            </div>
          </el-upload>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelAvatarUpload">取消</el-button>
            <el-button type="primary" @click="saveAvatar" :loading="uploading" :disabled="!newAvatar">
              保存
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useUserStore } from '../stores/user.js'
import { uploadImage } from '../api/upload.js'
import { getFavorites } from '../api/favorites.js'
import { getUserComments } from '../api/comments.js'
import { getMyApplication } from '../api/applications.js'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

const activeTab = ref('favorites')
const showEditDialog = ref(false)
const showAvatarDialog = ref(false)
const updating = ref(false)
const uploading = ref(false)
const newAvatar = ref('')

// 传承人申请状态
const applicationStatus = ref(null)
const applicationLoading = ref(false)

// 收藏相关状态
const favoriteRecipes = ref([])
const favoritesLoading = ref(false)
const favoritesPagination = reactive({
  page: 1,
  limit: 12,
  total: 0
})

// 评论相关状态
const userComments = ref([])
const commentsLoading = ref(false)
const commentsPagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

const editFormRef = ref(null)
const editForm = reactive({
  username: '',
  email: ''
})

const editRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const getRoleType = (role) => {
  const types = {
    'ADMIN': 'danger',
    'INHERITOR': 'warning',
    'USER': 'info'
  }
  return types[role] || 'info'
}

const getRoleText = (role) => {
  const texts = {
    'ADMIN': '管理员',
    'INHERITOR': '传承人',
    'USER': '普通用户'
  }
  return texts[role] || '普通用户'
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  try {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

const handleUpdateProfile = async () => {
  try {
    await editFormRef.value.validate()
    updating.value = true

    await userStore.updateUserProfile(editForm)

    ElMessage.success('个人资料更新成功')
    showEditDialog.value = false
  } catch (error) {
    ElMessage.error(error.error || '更新失败')
  } finally {
    updating.value = false
  }
}

const handleAvatarUpload = async ({ file, onError, onSuccess }) => {
  try {
    uploading.value = true
    const result = await uploadImage(file)
    newAvatar.value = result.file.url
    onSuccess(result)
  } catch (error) {
    ElMessage.error('头像上传失败')
    onError(error)
  } finally {
    uploading.value = false
  }
}

const saveAvatar = async () => {
  try {
    updating.value = true
    await userStore.updateUserProfile({ avatar: newAvatar.value })
    ElMessage.success('头像更新成功')
    showAvatarDialog.value = false
    newAvatar.value = ''
  } catch (error) {
    ElMessage.error(error.error || '头像更新失败')
  } finally {
    updating.value = false
  }
}

const cancelAvatarUpload = () => {
  showAvatarDialog.value = false
  newAvatar.value = ''
}

const openEditDialog = () => {
  editForm.username = userStore.user?.username || ''
  editForm.email = userStore.user?.email || ''
  showEditDialog.value = true
}

// 获取收藏列表
const fetchFavorites = async () => {
  try {
    favoritesLoading.value = true
    const response = await getFavorites({
      page: favoritesPagination.page,
      limit: favoritesPagination.limit
    })
    favoriteRecipes.value = response.recipes || []
    favoritesPagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    ElMessage.error('获取收藏列表失败')
  } finally {
    favoritesLoading.value = false
  }
}

const handleFavoritesPageChange = (page) => {
  favoritesPagination.page = page
  fetchFavorites()
}

// 获取用户评论列表
const fetchUserComments = async () => {
  if (!userStore.user?.id) return

  try {
    commentsLoading.value = true
    const response = await getUserComments(userStore.user.id, {
      page: commentsPagination.page,
      limit: commentsPagination.limit
    })
    userComments.value = response.comments || []
    commentsPagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('获取评论列表失败:', error)
    ElMessage.error('获取评论列表失败')
  } finally {
    commentsLoading.value = false
  }
}

const handleCommentsPageChange = (page) => {
  commentsPagination.page = page
  fetchUserComments()
}

// 获取传承人申请状态
const fetchApplicationStatus = async () => {
  if (userStore.user?.role === 'INHERITOR') {
    // 如果已经是传承人，不需要查询申请状态
    return
  }

  try {
    applicationLoading.value = true
    const response = await getMyApplication()
    applicationStatus.value = response.application
  } catch (error) {
    // 如果没有申请记录，这是正常的
    if (error.status !== 404) {
      console.error('获取申请状态失败:', error)
    }
  } finally {
    applicationLoading.value = false
  }
}

// 获取申请状态文本
const getApplicationStatusText = (status) => {
  switch (status) {
    case 'PENDING':
      return '审核中'
    case 'APPROVED':
      return '已通过'
    case 'REJECTED':
      return '已拒绝'
    default:
      return '未知状态'
  }
}

// 获取申请状态类型
const getApplicationStatusType = (status) => {
  switch (status) {
    case 'PENDING':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return 'info'
  }
}

// 监听activeTab变化，切换时加载对应数据
watch(activeTab, (newTab) => {
  if (newTab === 'favorites' && favoriteRecipes.value.length === 0) {
    fetchFavorites()
  } else if (newTab === 'comments' && userComments.value.length === 0) {
    fetchUserComments()
  }
})

onMounted(async () => {
  try {
    await userStore.fetchCurrentUser()
    // 获取传承人申请状态
    await fetchApplicationStatus()
    // 如果默认显示收藏页面，则加载收藏数据
    if (activeTab.value === 'favorites') {
      fetchFavorites()
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})
</script>

<style scoped>
.profile-view {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.profile-header {
  text-align: center;
  margin-bottom: 30px;
}

.profile-header h1 {
  font-size: 2.5rem;
  color: #303133;
  margin-bottom: 10px;
}

.profile-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.profile-info {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 30px;
  align-items: center;
}

.avatar-section {
  text-align: center;
}

.avatar-section .el-button {
  margin-top: 10px;
  font-size: 12px;
}

.user-details h2 {
  font-size: 1.8rem;
  color: #303133;
  margin-bottom: 10px;
}

.user-role {
  margin-bottom: 8px;
}

.user-email {
  color: #606266;
  margin-bottom: 8px;
}

.join-date {
  color: #909399;
  font-size: 0.9rem;
}

.application-status {
  margin: 15px 0;
}

.application-status .apply-time,
.application-status .review-time {
  margin-top: 8px;
  font-size: 0.85rem;
  color: #909399;
}

.application-status .review-comment {
  margin-top: 8px;
  font-size: 0.9rem;
  color: #606266;
  font-style: italic;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 0.9rem;
  color: #606266;
}

.profile-actions {
  margin-top: 20px;
}

.feature-nav {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.nav-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.nav-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.nav-item .el-icon {
  font-size: 2rem;
  color: #409eff;
  margin-bottom: 10px;
}

.nav-item span {
  display: block;
  font-weight: bold;
  color: #303133;
}

.content-area {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  min-height: 300px;
}

.tab-content h3 {
  color: #303133;
  margin-bottom: 20px;
}

.avatar-upload {
  text-align: center;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-placeholder {
  width: 150px;
  height: 150px;
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.avatar-placeholder:hover {
  border-color: #409eff;
}

.avatar-placeholder .el-icon {
  font-size: 2rem;
  color: #d9d9d9;
  margin-bottom: 10px;
}

/* 收藏列表样式 */
.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.recipe-card {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.recipe-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.recipe-image {
  height: 160px;
  overflow: hidden;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-info {
  padding: 15px;
}

.recipe-name {
  font-size: 1.1rem;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.recipe-dialect {
  color: #409eff;
  margin-bottom: 5px;
}

.recipe-region {
  color: #909399;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.recipe-stats {
  display: flex;
  gap: 15px;
  color: #909399;
  font-size: 0.9rem;
}

.recipe-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 评论列表样式 */
.comments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-comment-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  gap: 15px;
}

.comment-recipe {
  display: flex;
  gap: 10px;
  min-width: 200px;
}

.recipe-thumb {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
}

.recipe-info h4 {
  font-size: 1rem;
  color: #303133;
  margin-bottom: 4px;
  cursor: pointer;
  transition: color 0.3s;
}

.recipe-info h4:hover {
  color: #409eff;
}

.recipe-info p {
  color: #909399;
  font-size: 0.9rem;
}

.comment-details {
  flex: 1;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.comment-time {
  color: #909399;
  font-size: 0.85rem;
}

.comment-content {
  color: #606266;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-info {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 20px;
  }

  .user-stats {
    flex-direction: row;
    justify-content: center;
  }

  .feature-nav {
    grid-template-columns: 1fr;
  }

  .recipes-grid {
    grid-template-columns: 1fr;
  }
}
</style>
