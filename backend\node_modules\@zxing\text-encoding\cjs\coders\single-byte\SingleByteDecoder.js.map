{"version": 3, "file": "SingleByteDecoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/single-byte/SingleByteDecoder.ts"], "names": [], "mappings": ";;AACA,sDAAwD;AACxD,oDAAmD;AACnD,0DAAwE;AAExE;;;;;GAKG;AACH;IAIE,2BAA6B,KAAoB,EAAE,OAA4B;QAAlD,UAAK,GAAL,KAAK,CAAe;QAC/C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG;IACH,mCAAO,GAAP,UAAQ,MAAc,EAAE,IAAY;QAClC,gDAAgD;QAChD,IAAI,IAAI,KAAK,2BAAa;YACxB,OAAO,mBAAQ,CAAC;QAElB,+DAA+D;QAC/D,WAAW;QACX,IAAI,yBAAW,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI,CAAC;QAEd,+DAA+D;QAC/D,qBAAqB;QACrB,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAE3C,0CAA0C;QAC1C,IAAI,CAAC,UAAU;YACb,OAAO,wBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAElC,oDAAoD;QACpD,OAAO,UAAU,CAAC;IACpB,CAAC;IACH,wBAAC;AAAD,CAAC,AApCD,IAoCC;AApCY,8CAAiB"}