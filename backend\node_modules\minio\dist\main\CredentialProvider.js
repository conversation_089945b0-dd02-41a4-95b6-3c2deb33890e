"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _Credentials = require("./Credentials.js");
class CredentialProvider {
  constructor({
    accessKey,
    secretKey,
    sessionToken
  }) {
    this.credentials = new _Credentials.Credentials({
      accessKey,
      secretKey,
      sessionToken
    });
  }
  async getCredentials() {
    return this.credentials.get();
  }
  setCredentials(credentials) {
    if (credentials instanceof _Credentials.Credentials) {
      this.credentials = credentials;
    } else {
      throw new Error('Unable to set Credentials. it should be an instance of Credentials class');
    }
  }
  setAccessKey(accessKey) {
    this.credentials.setAccessKey(accessKey);
  }
  getAccessKey() {
    return this.credentials.getAccessKey();
  }
  setSecretKey(secretKey) {
    this.credentials.setSecretKey(secretKey);
  }
  getSecretKey() {
    return this.credentials.getSecretKey();
  }
  setSessionToken(sessionToken) {
    this.credentials.setSessionToken(sessionToken);
  }
  getSessionToken() {
    return this.credentials.getSessionToken();
  }
}

// deprecated default export, please use named exports.
// keep for backward compatibility.
// eslint-disable-next-line import/no-default-export
exports.CredentialProvider = CredentialProvider;
var _default = CredentialProvider;
exports.default = _default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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