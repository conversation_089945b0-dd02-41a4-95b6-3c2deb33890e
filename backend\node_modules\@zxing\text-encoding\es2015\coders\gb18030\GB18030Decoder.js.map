{"version": 3, "file": "GB18030Decoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/gb18030/GB18030Decoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,8BAA8B,EAAE,MAAM,wBAAwB,CAAC;AAClG,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACxE,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAEnD;;;;GAIG;AACH,MAAM,OAAO,cAAc;IAQzB,YAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,6DAA6D;QAC7D,kDAAkD;QAClD,qBAAqB,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI;YAC/C,qBAAqB,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI;YAChD,qBAAqB,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAClD,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CAAC,MAAc,EAAE,IAAY;QAClC,yDAAyD;QACzD,uDAAuD;QACvD,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI;YACvD,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC7D,OAAO,QAAQ,CAAC;SACjB;QACD,0DAA0D;QAC1D,2DAA2D;QAC3D,+DAA+D;QAC/D,IAAI,IAAI,KAAK,aAAa;YACxB,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI;gBAC1D,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE;YAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;QACD,IAAI,UAAkB,CAAC;QACvB,uDAAuD;QACvD,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC/B,6BAA6B;YAC7B,UAAU,GAAG,IAAI,CAAC;YAClB,0DAA0D;YAC1D,wDAAwD;YACxD,2DAA2D;YAC3D,kDAAkD;YAClD,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;gBAC7B,UAAU,GAAG,8BAA8B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,GAAG;oBAChH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;aAClD;YACD,yDAAyD;YACzD,6CAA6C;YAC7C,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAC/D,6DAA6D;YAC7D,QAAQ;YACR,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,yDAAyD;YACzD,gBAAgB;YAChB,IAAI,UAAU,KAAK,IAAI,EAAE;gBACvB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjC;YACD,oDAAoD;YACpD,OAAO,UAAU,CAAC;SACnB;QACD,wDAAwD;QACxD,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAChC,0DAA0D;YAC1D,6CAA6C;YAC7C,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;gBAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC1B,OAAO,IAAI,CAAC;aACb;YACD,4DAA4D;YAC5D,8DAA8D;YAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,uDAAuD;QACvD,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC/B,0DAA0D;YAC1D,8CAA8C;YAC9C,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;gBAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,OAAO,IAAI,CAAC;aACb;YACD,6DAA6D;YAC7D,yBAAyB;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;YAChC,IAAI,OAAO,GAAW,IAAI,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,2DAA2D;YAC3D,aAAa;YACb,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACzC,8DAA8D;YAC9D,2DAA2D;YAC3D,mBAAmB;YACnB,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBACxD,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;YAClD,6DAA6D;YAC7D,qDAAqD;YACrD,UAAU,GAAG,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACpC,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAa,CAAC,CAAC;YAC3D,8DAA8D;YAC9D,kBAAkB;YAClB,IAAI,UAAU,KAAK,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;gBAC1C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvB,0CAA0C;YAC1C,IAAI,UAAU,KAAK,IAAI;gBACrB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,oDAAoD;YACpD,OAAO,UAAU,CAAC;SACnB;QACD,+DAA+D;QAC/D,WAAW;QACX,IAAI,WAAW,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,gDAAgD;QAChD,IAAI,IAAI,KAAK,IAAI;YACf,OAAO,MAAM,CAAC;QAChB,0DAA0D;QAC1D,6CAA6C;QAC7C,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO,IAAI,CAAC;SACb;QACD,mBAAmB;QACnB,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;CACF"}