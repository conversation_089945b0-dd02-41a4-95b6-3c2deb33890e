import api from './index.js'

// 获取用户收藏列表
export const getFavorites = (params = {}) => {
  return api.get('/favorites', { params })
}

// 检查菜谱是否已收藏
export const checkFavorite = (recipeId) => {
  return api.get(`/favorites/check/${recipeId}`)
}

// 添加收藏
export const addFavorite = (recipeId) => {
  return api.post(`/favorites/${recipeId}`)
}

// 取消收藏
export const removeFavorite = (recipeId) => {
  return api.delete(`/favorites/${recipeId}`)
}

// 切换收藏状态
export const toggleFavorite = async (recipeId) => {
  try {
    const { isFavorited } = await checkFavorite(recipeId)
    
    if (isFavorited) {
      await removeFavorite(recipeId)
      return { isFavorited: false, message: '取消收藏成功' }
    } else {
      await addFavorite(recipeId)
      return { isFavorited: true, message: '收藏成功' }
    }
  } catch (error) {
    throw error
  }
}
