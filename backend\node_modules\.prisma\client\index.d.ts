
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model InheritorApplication
 * 
 */
export type InheritorApplication = $Result.DefaultSelection<Prisma.$InheritorApplicationPayload>
/**
 * Model Recipe
 * 
 */
export type Recipe = $Result.DefaultSelection<Prisma.$RecipePayload>
/**
 * Model Category
 * 
 */
export type Category = $Result.DefaultSelection<Prisma.$CategoryPayload>
/**
 * Model RecipeCategory
 * 
 */
export type RecipeCategory = $Result.DefaultSelection<Prisma.$RecipeCategoryPayload>
/**
 * Model Comment
 * 
 */
export type Comment = $Result.DefaultSelection<Prisma.$CommentPayload>
/**
 * Model UserFavorite
 * 
 */
export type UserFavorite = $Result.DefaultSelection<Prisma.$UserFavoritePayload>

/**
 * Enums
 */
export namespace $Enums {
  export const UserRole: {
  USER: 'USER',
  ADMIN: 'ADMIN',
  INHERITOR: 'INHERITOR'
};

export type UserRole = (typeof UserRole)[keyof typeof UserRole]


export const ApplicationStatus: {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

export type ApplicationStatus = (typeof ApplicationStatus)[keyof typeof ApplicationStatus]

}

export type UserRole = $Enums.UserRole

export const UserRole: typeof $Enums.UserRole

export type ApplicationStatus = $Enums.ApplicationStatus

export const ApplicationStatus: typeof $Enums.ApplicationStatus

/**
 * ##  Prisma Client ʲˢ
 * 
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   * 
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): void;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb, ExtArgs>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs>;

  /**
   * `prisma.inheritorApplication`: Exposes CRUD operations for the **InheritorApplication** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more InheritorApplications
    * const inheritorApplications = await prisma.inheritorApplication.findMany()
    * ```
    */
  get inheritorApplication(): Prisma.InheritorApplicationDelegate<ExtArgs>;

  /**
   * `prisma.recipe`: Exposes CRUD operations for the **Recipe** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Recipes
    * const recipes = await prisma.recipe.findMany()
    * ```
    */
  get recipe(): Prisma.RecipeDelegate<ExtArgs>;

  /**
   * `prisma.category`: Exposes CRUD operations for the **Category** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Categories
    * const categories = await prisma.category.findMany()
    * ```
    */
  get category(): Prisma.CategoryDelegate<ExtArgs>;

  /**
   * `prisma.recipeCategory`: Exposes CRUD operations for the **RecipeCategory** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more RecipeCategories
    * const recipeCategories = await prisma.recipeCategory.findMany()
    * ```
    */
  get recipeCategory(): Prisma.RecipeCategoryDelegate<ExtArgs>;

  /**
   * `prisma.comment`: Exposes CRUD operations for the **Comment** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Comments
    * const comments = await prisma.comment.findMany()
    * ```
    */
  get comment(): Prisma.CommentDelegate<ExtArgs>;

  /**
   * `prisma.userFavorite`: Exposes CRUD operations for the **UserFavorite** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more UserFavorites
    * const userFavorites = await prisma.userFavorite.findMany()
    * ```
    */
  get userFavorite(): Prisma.UserFavoriteDelegate<ExtArgs>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError
  export import NotFoundError = runtime.NotFoundError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics 
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 5.22.0
   * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion 

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? K : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    InheritorApplication: 'InheritorApplication',
    Recipe: 'Recipe',
    Category: 'Category',
    RecipeCategory: 'RecipeCategory',
    Comment: 'Comment',
    UserFavorite: 'UserFavorite'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb extends $Utils.Fn<{extArgs: $Extensions.InternalArgs, clientOptions: PrismaClientOptions }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], this['params']['clientOptions']>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, ClientOptions = {}> = {
    meta: {
      modelProps: "user" | "inheritorApplication" | "recipe" | "category" | "recipeCategory" | "comment" | "userFavorite"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      InheritorApplication: {
        payload: Prisma.$InheritorApplicationPayload<ExtArgs>
        fields: Prisma.InheritorApplicationFieldRefs
        operations: {
          findUnique: {
            args: Prisma.InheritorApplicationFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.InheritorApplicationFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload>
          }
          findFirst: {
            args: Prisma.InheritorApplicationFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.InheritorApplicationFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload>
          }
          findMany: {
            args: Prisma.InheritorApplicationFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload>[]
          }
          create: {
            args: Prisma.InheritorApplicationCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload>
          }
          createMany: {
            args: Prisma.InheritorApplicationCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.InheritorApplicationCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload>[]
          }
          delete: {
            args: Prisma.InheritorApplicationDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload>
          }
          update: {
            args: Prisma.InheritorApplicationUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload>
          }
          deleteMany: {
            args: Prisma.InheritorApplicationDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.InheritorApplicationUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.InheritorApplicationUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InheritorApplicationPayload>
          }
          aggregate: {
            args: Prisma.InheritorApplicationAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateInheritorApplication>
          }
          groupBy: {
            args: Prisma.InheritorApplicationGroupByArgs<ExtArgs>
            result: $Utils.Optional<InheritorApplicationGroupByOutputType>[]
          }
          count: {
            args: Prisma.InheritorApplicationCountArgs<ExtArgs>
            result: $Utils.Optional<InheritorApplicationCountAggregateOutputType> | number
          }
        }
      }
      Recipe: {
        payload: Prisma.$RecipePayload<ExtArgs>
        fields: Prisma.RecipeFieldRefs
        operations: {
          findUnique: {
            args: Prisma.RecipeFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.RecipeFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload>
          }
          findFirst: {
            args: Prisma.RecipeFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.RecipeFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload>
          }
          findMany: {
            args: Prisma.RecipeFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload>[]
          }
          create: {
            args: Prisma.RecipeCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload>
          }
          createMany: {
            args: Prisma.RecipeCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.RecipeCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload>[]
          }
          delete: {
            args: Prisma.RecipeDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload>
          }
          update: {
            args: Prisma.RecipeUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload>
          }
          deleteMany: {
            args: Prisma.RecipeDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.RecipeUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.RecipeUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipePayload>
          }
          aggregate: {
            args: Prisma.RecipeAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateRecipe>
          }
          groupBy: {
            args: Prisma.RecipeGroupByArgs<ExtArgs>
            result: $Utils.Optional<RecipeGroupByOutputType>[]
          }
          count: {
            args: Prisma.RecipeCountArgs<ExtArgs>
            result: $Utils.Optional<RecipeCountAggregateOutputType> | number
          }
        }
      }
      Category: {
        payload: Prisma.$CategoryPayload<ExtArgs>
        fields: Prisma.CategoryFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CategoryFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CategoryFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload>
          }
          findFirst: {
            args: Prisma.CategoryFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CategoryFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload>
          }
          findMany: {
            args: Prisma.CategoryFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload>[]
          }
          create: {
            args: Prisma.CategoryCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload>
          }
          createMany: {
            args: Prisma.CategoryCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.CategoryCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload>[]
          }
          delete: {
            args: Prisma.CategoryDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload>
          }
          update: {
            args: Prisma.CategoryUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload>
          }
          deleteMany: {
            args: Prisma.CategoryDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CategoryUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.CategoryUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CategoryPayload>
          }
          aggregate: {
            args: Prisma.CategoryAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateCategory>
          }
          groupBy: {
            args: Prisma.CategoryGroupByArgs<ExtArgs>
            result: $Utils.Optional<CategoryGroupByOutputType>[]
          }
          count: {
            args: Prisma.CategoryCountArgs<ExtArgs>
            result: $Utils.Optional<CategoryCountAggregateOutputType> | number
          }
        }
      }
      RecipeCategory: {
        payload: Prisma.$RecipeCategoryPayload<ExtArgs>
        fields: Prisma.RecipeCategoryFieldRefs
        operations: {
          findUnique: {
            args: Prisma.RecipeCategoryFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.RecipeCategoryFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload>
          }
          findFirst: {
            args: Prisma.RecipeCategoryFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.RecipeCategoryFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload>
          }
          findMany: {
            args: Prisma.RecipeCategoryFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload>[]
          }
          create: {
            args: Prisma.RecipeCategoryCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload>
          }
          createMany: {
            args: Prisma.RecipeCategoryCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.RecipeCategoryCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload>[]
          }
          delete: {
            args: Prisma.RecipeCategoryDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload>
          }
          update: {
            args: Prisma.RecipeCategoryUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload>
          }
          deleteMany: {
            args: Prisma.RecipeCategoryDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.RecipeCategoryUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.RecipeCategoryUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RecipeCategoryPayload>
          }
          aggregate: {
            args: Prisma.RecipeCategoryAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateRecipeCategory>
          }
          groupBy: {
            args: Prisma.RecipeCategoryGroupByArgs<ExtArgs>
            result: $Utils.Optional<RecipeCategoryGroupByOutputType>[]
          }
          count: {
            args: Prisma.RecipeCategoryCountArgs<ExtArgs>
            result: $Utils.Optional<RecipeCategoryCountAggregateOutputType> | number
          }
        }
      }
      Comment: {
        payload: Prisma.$CommentPayload<ExtArgs>
        fields: Prisma.CommentFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CommentFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CommentFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload>
          }
          findFirst: {
            args: Prisma.CommentFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CommentFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload>
          }
          findMany: {
            args: Prisma.CommentFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload>[]
          }
          create: {
            args: Prisma.CommentCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload>
          }
          createMany: {
            args: Prisma.CommentCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.CommentCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload>[]
          }
          delete: {
            args: Prisma.CommentDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload>
          }
          update: {
            args: Prisma.CommentUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload>
          }
          deleteMany: {
            args: Prisma.CommentDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CommentUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.CommentUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CommentPayload>
          }
          aggregate: {
            args: Prisma.CommentAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateComment>
          }
          groupBy: {
            args: Prisma.CommentGroupByArgs<ExtArgs>
            result: $Utils.Optional<CommentGroupByOutputType>[]
          }
          count: {
            args: Prisma.CommentCountArgs<ExtArgs>
            result: $Utils.Optional<CommentCountAggregateOutputType> | number
          }
        }
      }
      UserFavorite: {
        payload: Prisma.$UserFavoritePayload<ExtArgs>
        fields: Prisma.UserFavoriteFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFavoriteFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFavoriteFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload>
          }
          findFirst: {
            args: Prisma.UserFavoriteFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFavoriteFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload>
          }
          findMany: {
            args: Prisma.UserFavoriteFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload>[]
          }
          create: {
            args: Prisma.UserFavoriteCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload>
          }
          createMany: {
            args: Prisma.UserFavoriteCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserFavoriteCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload>[]
          }
          delete: {
            args: Prisma.UserFavoriteDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload>
          }
          update: {
            args: Prisma.UserFavoriteUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload>
          }
          deleteMany: {
            args: Prisma.UserFavoriteDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserFavoriteUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.UserFavoriteUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserFavoritePayload>
          }
          aggregate: {
            args: Prisma.UserFavoriteAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUserFavorite>
          }
          groupBy: {
            args: Prisma.UserFavoriteGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserFavoriteGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserFavoriteCountArgs<ExtArgs>
            result: $Utils.Optional<UserFavoriteCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
  }


  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    recipes: number
    comments: number
    favorites: number
    reviewedApplications: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    recipes?: boolean | UserCountOutputTypeCountRecipesArgs
    comments?: boolean | UserCountOutputTypeCountCommentsArgs
    favorites?: boolean | UserCountOutputTypeCountFavoritesArgs
    reviewedApplications?: boolean | UserCountOutputTypeCountReviewedApplicationsArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountRecipesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecipeWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountCommentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CommentWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountFavoritesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserFavoriteWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountReviewedApplicationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: InheritorApplicationWhereInput
  }


  /**
   * Count Type RecipeCountOutputType
   */

  export type RecipeCountOutputType = {
    categories: number
    comments: number
    favorites: number
  }

  export type RecipeCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    categories?: boolean | RecipeCountOutputTypeCountCategoriesArgs
    comments?: boolean | RecipeCountOutputTypeCountCommentsArgs
    favorites?: boolean | RecipeCountOutputTypeCountFavoritesArgs
  }

  // Custom InputTypes
  /**
   * RecipeCountOutputType without action
   */
  export type RecipeCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCountOutputType
     */
    select?: RecipeCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * RecipeCountOutputType without action
   */
  export type RecipeCountOutputTypeCountCategoriesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecipeCategoryWhereInput
  }

  /**
   * RecipeCountOutputType without action
   */
  export type RecipeCountOutputTypeCountCommentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CommentWhereInput
  }

  /**
   * RecipeCountOutputType without action
   */
  export type RecipeCountOutputTypeCountFavoritesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserFavoriteWhereInput
  }


  /**
   * Count Type CategoryCountOutputType
   */

  export type CategoryCountOutputType = {
    recipes: number
  }

  export type CategoryCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    recipes?: boolean | CategoryCountOutputTypeCountRecipesArgs
  }

  // Custom InputTypes
  /**
   * CategoryCountOutputType without action
   */
  export type CategoryCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CategoryCountOutputType
     */
    select?: CategoryCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * CategoryCountOutputType without action
   */
  export type CategoryCountOutputTypeCountRecipesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecipeCategoryWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserMinAggregateOutputType = {
    id: string | null
    username: string | null
    email: string | null
    password: string | null
    role: $Enums.UserRole | null
    avatar: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: string | null
    username: string | null
    email: string | null
    password: string | null
    role: $Enums.UserRole | null
    avatar: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    username: number
    email: number
    password: number
    role: number
    avatar: number
    isActive: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserMinAggregateInputType = {
    id?: true
    username?: true
    email?: true
    password?: true
    role?: true
    avatar?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    username?: true
    email?: true
    password?: true
    role?: true
    avatar?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    username?: true
    email?: true
    password?: true
    role?: true
    avatar?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: string
    username: string
    email: string | null
    password: string
    role: $Enums.UserRole
    avatar: string | null
    isActive: boolean
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    email?: boolean
    password?: boolean
    role?: boolean
    avatar?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    recipes?: boolean | User$recipesArgs<ExtArgs>
    comments?: boolean | User$commentsArgs<ExtArgs>
    favorites?: boolean | User$favoritesArgs<ExtArgs>
    inheritorApplication?: boolean | User$inheritorApplicationArgs<ExtArgs>
    reviewedApplications?: boolean | User$reviewedApplicationsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    email?: boolean
    password?: boolean
    role?: boolean
    avatar?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    username?: boolean
    email?: boolean
    password?: boolean
    role?: boolean
    avatar?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    recipes?: boolean | User$recipesArgs<ExtArgs>
    comments?: boolean | User$commentsArgs<ExtArgs>
    favorites?: boolean | User$favoritesArgs<ExtArgs>
    inheritorApplication?: boolean | User$inheritorApplicationArgs<ExtArgs>
    reviewedApplications?: boolean | User$reviewedApplicationsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      recipes: Prisma.$RecipePayload<ExtArgs>[]
      comments: Prisma.$CommentPayload<ExtArgs>[]
      favorites: Prisma.$UserFavoritePayload<ExtArgs>[]
      inheritorApplication: Prisma.$InheritorApplicationPayload<ExtArgs> | null
      reviewedApplications: Prisma.$InheritorApplicationPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      username: string
      email: string | null
      password: string
      role: $Enums.UserRole
      avatar: string | null
      isActive: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    recipes<T extends User$recipesArgs<ExtArgs> = {}>(args?: Subset<T, User$recipesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findMany"> | Null>
    comments<T extends User$commentsArgs<ExtArgs> = {}>(args?: Subset<T, User$commentsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "findMany"> | Null>
    favorites<T extends User$favoritesArgs<ExtArgs> = {}>(args?: Subset<T, User$favoritesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "findMany"> | Null>
    inheritorApplication<T extends User$inheritorApplicationArgs<ExtArgs> = {}>(args?: Subset<T, User$inheritorApplicationArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "findUniqueOrThrow"> | null, null, ExtArgs>
    reviewedApplications<T extends User$reviewedApplicationsArgs<ExtArgs> = {}>(args?: Subset<T, User$reviewedApplicationsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */ 
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'String'>
    readonly username: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly password: FieldRef<"User", 'String'>
    readonly role: FieldRef<"User", 'UserRole'>
    readonly avatar: FieldRef<"User", 'String'>
    readonly isActive: FieldRef<"User", 'Boolean'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
  }

  /**
   * User.recipes
   */
  export type User$recipesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    where?: RecipeWhereInput
    orderBy?: RecipeOrderByWithRelationInput | RecipeOrderByWithRelationInput[]
    cursor?: RecipeWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RecipeScalarFieldEnum | RecipeScalarFieldEnum[]
  }

  /**
   * User.comments
   */
  export type User$commentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    where?: CommentWhereInput
    orderBy?: CommentOrderByWithRelationInput | CommentOrderByWithRelationInput[]
    cursor?: CommentWhereUniqueInput
    take?: number
    skip?: number
    distinct?: CommentScalarFieldEnum | CommentScalarFieldEnum[]
  }

  /**
   * User.favorites
   */
  export type User$favoritesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    where?: UserFavoriteWhereInput
    orderBy?: UserFavoriteOrderByWithRelationInput | UserFavoriteOrderByWithRelationInput[]
    cursor?: UserFavoriteWhereUniqueInput
    take?: number
    skip?: number
    distinct?: UserFavoriteScalarFieldEnum | UserFavoriteScalarFieldEnum[]
  }

  /**
   * User.inheritorApplication
   */
  export type User$inheritorApplicationArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    where?: InheritorApplicationWhereInput
  }

  /**
   * User.reviewedApplications
   */
  export type User$reviewedApplicationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    where?: InheritorApplicationWhereInput
    orderBy?: InheritorApplicationOrderByWithRelationInput | InheritorApplicationOrderByWithRelationInput[]
    cursor?: InheritorApplicationWhereUniqueInput
    take?: number
    skip?: number
    distinct?: InheritorApplicationScalarFieldEnum | InheritorApplicationScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model InheritorApplication
   */

  export type AggregateInheritorApplication = {
    _count: InheritorApplicationCountAggregateOutputType | null
    _min: InheritorApplicationMinAggregateOutputType | null
    _max: InheritorApplicationMaxAggregateOutputType | null
  }

  export type InheritorApplicationMinAggregateOutputType = {
    id: string | null
    userId: string | null
    realName: string | null
    phone: string | null
    region: string | null
    specialties: string | null
    experience: string | null
    certifications: string | null
    introduction: string | null
    status: $Enums.ApplicationStatus | null
    appliedAt: Date | null
    reviewedAt: Date | null
    reviewedBy: string | null
    reviewComment: string | null
  }

  export type InheritorApplicationMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    realName: string | null
    phone: string | null
    region: string | null
    specialties: string | null
    experience: string | null
    certifications: string | null
    introduction: string | null
    status: $Enums.ApplicationStatus | null
    appliedAt: Date | null
    reviewedAt: Date | null
    reviewedBy: string | null
    reviewComment: string | null
  }

  export type InheritorApplicationCountAggregateOutputType = {
    id: number
    userId: number
    realName: number
    phone: number
    region: number
    specialties: number
    experience: number
    certifications: number
    introduction: number
    status: number
    appliedAt: number
    reviewedAt: number
    reviewedBy: number
    reviewComment: number
    _all: number
  }


  export type InheritorApplicationMinAggregateInputType = {
    id?: true
    userId?: true
    realName?: true
    phone?: true
    region?: true
    specialties?: true
    experience?: true
    certifications?: true
    introduction?: true
    status?: true
    appliedAt?: true
    reviewedAt?: true
    reviewedBy?: true
    reviewComment?: true
  }

  export type InheritorApplicationMaxAggregateInputType = {
    id?: true
    userId?: true
    realName?: true
    phone?: true
    region?: true
    specialties?: true
    experience?: true
    certifications?: true
    introduction?: true
    status?: true
    appliedAt?: true
    reviewedAt?: true
    reviewedBy?: true
    reviewComment?: true
  }

  export type InheritorApplicationCountAggregateInputType = {
    id?: true
    userId?: true
    realName?: true
    phone?: true
    region?: true
    specialties?: true
    experience?: true
    certifications?: true
    introduction?: true
    status?: true
    appliedAt?: true
    reviewedAt?: true
    reviewedBy?: true
    reviewComment?: true
    _all?: true
  }

  export type InheritorApplicationAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which InheritorApplication to aggregate.
     */
    where?: InheritorApplicationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of InheritorApplications to fetch.
     */
    orderBy?: InheritorApplicationOrderByWithRelationInput | InheritorApplicationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: InheritorApplicationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` InheritorApplications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` InheritorApplications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned InheritorApplications
    **/
    _count?: true | InheritorApplicationCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: InheritorApplicationMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: InheritorApplicationMaxAggregateInputType
  }

  export type GetInheritorApplicationAggregateType<T extends InheritorApplicationAggregateArgs> = {
        [P in keyof T & keyof AggregateInheritorApplication]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateInheritorApplication[P]>
      : GetScalarType<T[P], AggregateInheritorApplication[P]>
  }




  export type InheritorApplicationGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: InheritorApplicationWhereInput
    orderBy?: InheritorApplicationOrderByWithAggregationInput | InheritorApplicationOrderByWithAggregationInput[]
    by: InheritorApplicationScalarFieldEnum[] | InheritorApplicationScalarFieldEnum
    having?: InheritorApplicationScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: InheritorApplicationCountAggregateInputType | true
    _min?: InheritorApplicationMinAggregateInputType
    _max?: InheritorApplicationMaxAggregateInputType
  }

  export type InheritorApplicationGroupByOutputType = {
    id: string
    userId: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications: string | null
    introduction: string
    status: $Enums.ApplicationStatus
    appliedAt: Date
    reviewedAt: Date | null
    reviewedBy: string | null
    reviewComment: string | null
    _count: InheritorApplicationCountAggregateOutputType | null
    _min: InheritorApplicationMinAggregateOutputType | null
    _max: InheritorApplicationMaxAggregateOutputType | null
  }

  type GetInheritorApplicationGroupByPayload<T extends InheritorApplicationGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<InheritorApplicationGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof InheritorApplicationGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], InheritorApplicationGroupByOutputType[P]>
            : GetScalarType<T[P], InheritorApplicationGroupByOutputType[P]>
        }
      >
    >


  export type InheritorApplicationSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    realName?: boolean
    phone?: boolean
    region?: boolean
    specialties?: boolean
    experience?: boolean
    certifications?: boolean
    introduction?: boolean
    status?: boolean
    appliedAt?: boolean
    reviewedAt?: boolean
    reviewedBy?: boolean
    reviewComment?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    reviewer?: boolean | InheritorApplication$reviewerArgs<ExtArgs>
  }, ExtArgs["result"]["inheritorApplication"]>

  export type InheritorApplicationSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    realName?: boolean
    phone?: boolean
    region?: boolean
    specialties?: boolean
    experience?: boolean
    certifications?: boolean
    introduction?: boolean
    status?: boolean
    appliedAt?: boolean
    reviewedAt?: boolean
    reviewedBy?: boolean
    reviewComment?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    reviewer?: boolean | InheritorApplication$reviewerArgs<ExtArgs>
  }, ExtArgs["result"]["inheritorApplication"]>

  export type InheritorApplicationSelectScalar = {
    id?: boolean
    userId?: boolean
    realName?: boolean
    phone?: boolean
    region?: boolean
    specialties?: boolean
    experience?: boolean
    certifications?: boolean
    introduction?: boolean
    status?: boolean
    appliedAt?: boolean
    reviewedAt?: boolean
    reviewedBy?: boolean
    reviewComment?: boolean
  }

  export type InheritorApplicationInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    reviewer?: boolean | InheritorApplication$reviewerArgs<ExtArgs>
  }
  export type InheritorApplicationIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    reviewer?: boolean | InheritorApplication$reviewerArgs<ExtArgs>
  }

  export type $InheritorApplicationPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "InheritorApplication"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      reviewer: Prisma.$UserPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      realName: string
      phone: string
      region: string
      specialties: string
      experience: string
      certifications: string | null
      introduction: string
      status: $Enums.ApplicationStatus
      appliedAt: Date
      reviewedAt: Date | null
      reviewedBy: string | null
      reviewComment: string | null
    }, ExtArgs["result"]["inheritorApplication"]>
    composites: {}
  }

  type InheritorApplicationGetPayload<S extends boolean | null | undefined | InheritorApplicationDefaultArgs> = $Result.GetResult<Prisma.$InheritorApplicationPayload, S>

  type InheritorApplicationCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<InheritorApplicationFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: InheritorApplicationCountAggregateInputType | true
    }

  export interface InheritorApplicationDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['InheritorApplication'], meta: { name: 'InheritorApplication' } }
    /**
     * Find zero or one InheritorApplication that matches the filter.
     * @param {InheritorApplicationFindUniqueArgs} args - Arguments to find a InheritorApplication
     * @example
     * // Get one InheritorApplication
     * const inheritorApplication = await prisma.inheritorApplication.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends InheritorApplicationFindUniqueArgs>(args: SelectSubset<T, InheritorApplicationFindUniqueArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one InheritorApplication that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {InheritorApplicationFindUniqueOrThrowArgs} args - Arguments to find a InheritorApplication
     * @example
     * // Get one InheritorApplication
     * const inheritorApplication = await prisma.inheritorApplication.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends InheritorApplicationFindUniqueOrThrowArgs>(args: SelectSubset<T, InheritorApplicationFindUniqueOrThrowArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first InheritorApplication that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InheritorApplicationFindFirstArgs} args - Arguments to find a InheritorApplication
     * @example
     * // Get one InheritorApplication
     * const inheritorApplication = await prisma.inheritorApplication.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends InheritorApplicationFindFirstArgs>(args?: SelectSubset<T, InheritorApplicationFindFirstArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first InheritorApplication that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InheritorApplicationFindFirstOrThrowArgs} args - Arguments to find a InheritorApplication
     * @example
     * // Get one InheritorApplication
     * const inheritorApplication = await prisma.inheritorApplication.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends InheritorApplicationFindFirstOrThrowArgs>(args?: SelectSubset<T, InheritorApplicationFindFirstOrThrowArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more InheritorApplications that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InheritorApplicationFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all InheritorApplications
     * const inheritorApplications = await prisma.inheritorApplication.findMany()
     * 
     * // Get first 10 InheritorApplications
     * const inheritorApplications = await prisma.inheritorApplication.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const inheritorApplicationWithIdOnly = await prisma.inheritorApplication.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends InheritorApplicationFindManyArgs>(args?: SelectSubset<T, InheritorApplicationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a InheritorApplication.
     * @param {InheritorApplicationCreateArgs} args - Arguments to create a InheritorApplication.
     * @example
     * // Create one InheritorApplication
     * const InheritorApplication = await prisma.inheritorApplication.create({
     *   data: {
     *     // ... data to create a InheritorApplication
     *   }
     * })
     * 
     */
    create<T extends InheritorApplicationCreateArgs>(args: SelectSubset<T, InheritorApplicationCreateArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many InheritorApplications.
     * @param {InheritorApplicationCreateManyArgs} args - Arguments to create many InheritorApplications.
     * @example
     * // Create many InheritorApplications
     * const inheritorApplication = await prisma.inheritorApplication.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends InheritorApplicationCreateManyArgs>(args?: SelectSubset<T, InheritorApplicationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many InheritorApplications and returns the data saved in the database.
     * @param {InheritorApplicationCreateManyAndReturnArgs} args - Arguments to create many InheritorApplications.
     * @example
     * // Create many InheritorApplications
     * const inheritorApplication = await prisma.inheritorApplication.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many InheritorApplications and only return the `id`
     * const inheritorApplicationWithIdOnly = await prisma.inheritorApplication.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends InheritorApplicationCreateManyAndReturnArgs>(args?: SelectSubset<T, InheritorApplicationCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a InheritorApplication.
     * @param {InheritorApplicationDeleteArgs} args - Arguments to delete one InheritorApplication.
     * @example
     * // Delete one InheritorApplication
     * const InheritorApplication = await prisma.inheritorApplication.delete({
     *   where: {
     *     // ... filter to delete one InheritorApplication
     *   }
     * })
     * 
     */
    delete<T extends InheritorApplicationDeleteArgs>(args: SelectSubset<T, InheritorApplicationDeleteArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one InheritorApplication.
     * @param {InheritorApplicationUpdateArgs} args - Arguments to update one InheritorApplication.
     * @example
     * // Update one InheritorApplication
     * const inheritorApplication = await prisma.inheritorApplication.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends InheritorApplicationUpdateArgs>(args: SelectSubset<T, InheritorApplicationUpdateArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more InheritorApplications.
     * @param {InheritorApplicationDeleteManyArgs} args - Arguments to filter InheritorApplications to delete.
     * @example
     * // Delete a few InheritorApplications
     * const { count } = await prisma.inheritorApplication.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends InheritorApplicationDeleteManyArgs>(args?: SelectSubset<T, InheritorApplicationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more InheritorApplications.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InheritorApplicationUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many InheritorApplications
     * const inheritorApplication = await prisma.inheritorApplication.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends InheritorApplicationUpdateManyArgs>(args: SelectSubset<T, InheritorApplicationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one InheritorApplication.
     * @param {InheritorApplicationUpsertArgs} args - Arguments to update or create a InheritorApplication.
     * @example
     * // Update or create a InheritorApplication
     * const inheritorApplication = await prisma.inheritorApplication.upsert({
     *   create: {
     *     // ... data to create a InheritorApplication
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the InheritorApplication we want to update
     *   }
     * })
     */
    upsert<T extends InheritorApplicationUpsertArgs>(args: SelectSubset<T, InheritorApplicationUpsertArgs<ExtArgs>>): Prisma__InheritorApplicationClient<$Result.GetResult<Prisma.$InheritorApplicationPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of InheritorApplications.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InheritorApplicationCountArgs} args - Arguments to filter InheritorApplications to count.
     * @example
     * // Count the number of InheritorApplications
     * const count = await prisma.inheritorApplication.count({
     *   where: {
     *     // ... the filter for the InheritorApplications we want to count
     *   }
     * })
    **/
    count<T extends InheritorApplicationCountArgs>(
      args?: Subset<T, InheritorApplicationCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], InheritorApplicationCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a InheritorApplication.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InheritorApplicationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends InheritorApplicationAggregateArgs>(args: Subset<T, InheritorApplicationAggregateArgs>): Prisma.PrismaPromise<GetInheritorApplicationAggregateType<T>>

    /**
     * Group by InheritorApplication.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InheritorApplicationGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends InheritorApplicationGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: InheritorApplicationGroupByArgs['orderBy'] }
        : { orderBy?: InheritorApplicationGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, InheritorApplicationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetInheritorApplicationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the InheritorApplication model
   */
  readonly fields: InheritorApplicationFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for InheritorApplication.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__InheritorApplicationClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    reviewer<T extends InheritorApplication$reviewerArgs<ExtArgs> = {}>(args?: Subset<T, InheritorApplication$reviewerArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | null, null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the InheritorApplication model
   */ 
  interface InheritorApplicationFieldRefs {
    readonly id: FieldRef<"InheritorApplication", 'String'>
    readonly userId: FieldRef<"InheritorApplication", 'String'>
    readonly realName: FieldRef<"InheritorApplication", 'String'>
    readonly phone: FieldRef<"InheritorApplication", 'String'>
    readonly region: FieldRef<"InheritorApplication", 'String'>
    readonly specialties: FieldRef<"InheritorApplication", 'String'>
    readonly experience: FieldRef<"InheritorApplication", 'String'>
    readonly certifications: FieldRef<"InheritorApplication", 'String'>
    readonly introduction: FieldRef<"InheritorApplication", 'String'>
    readonly status: FieldRef<"InheritorApplication", 'ApplicationStatus'>
    readonly appliedAt: FieldRef<"InheritorApplication", 'DateTime'>
    readonly reviewedAt: FieldRef<"InheritorApplication", 'DateTime'>
    readonly reviewedBy: FieldRef<"InheritorApplication", 'String'>
    readonly reviewComment: FieldRef<"InheritorApplication", 'String'>
  }
    

  // Custom InputTypes
  /**
   * InheritorApplication findUnique
   */
  export type InheritorApplicationFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * Filter, which InheritorApplication to fetch.
     */
    where: InheritorApplicationWhereUniqueInput
  }

  /**
   * InheritorApplication findUniqueOrThrow
   */
  export type InheritorApplicationFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * Filter, which InheritorApplication to fetch.
     */
    where: InheritorApplicationWhereUniqueInput
  }

  /**
   * InheritorApplication findFirst
   */
  export type InheritorApplicationFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * Filter, which InheritorApplication to fetch.
     */
    where?: InheritorApplicationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of InheritorApplications to fetch.
     */
    orderBy?: InheritorApplicationOrderByWithRelationInput | InheritorApplicationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for InheritorApplications.
     */
    cursor?: InheritorApplicationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` InheritorApplications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` InheritorApplications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of InheritorApplications.
     */
    distinct?: InheritorApplicationScalarFieldEnum | InheritorApplicationScalarFieldEnum[]
  }

  /**
   * InheritorApplication findFirstOrThrow
   */
  export type InheritorApplicationFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * Filter, which InheritorApplication to fetch.
     */
    where?: InheritorApplicationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of InheritorApplications to fetch.
     */
    orderBy?: InheritorApplicationOrderByWithRelationInput | InheritorApplicationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for InheritorApplications.
     */
    cursor?: InheritorApplicationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` InheritorApplications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` InheritorApplications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of InheritorApplications.
     */
    distinct?: InheritorApplicationScalarFieldEnum | InheritorApplicationScalarFieldEnum[]
  }

  /**
   * InheritorApplication findMany
   */
  export type InheritorApplicationFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * Filter, which InheritorApplications to fetch.
     */
    where?: InheritorApplicationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of InheritorApplications to fetch.
     */
    orderBy?: InheritorApplicationOrderByWithRelationInput | InheritorApplicationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing InheritorApplications.
     */
    cursor?: InheritorApplicationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` InheritorApplications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` InheritorApplications.
     */
    skip?: number
    distinct?: InheritorApplicationScalarFieldEnum | InheritorApplicationScalarFieldEnum[]
  }

  /**
   * InheritorApplication create
   */
  export type InheritorApplicationCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * The data needed to create a InheritorApplication.
     */
    data: XOR<InheritorApplicationCreateInput, InheritorApplicationUncheckedCreateInput>
  }

  /**
   * InheritorApplication createMany
   */
  export type InheritorApplicationCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many InheritorApplications.
     */
    data: InheritorApplicationCreateManyInput | InheritorApplicationCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * InheritorApplication createManyAndReturn
   */
  export type InheritorApplicationCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many InheritorApplications.
     */
    data: InheritorApplicationCreateManyInput | InheritorApplicationCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * InheritorApplication update
   */
  export type InheritorApplicationUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * The data needed to update a InheritorApplication.
     */
    data: XOR<InheritorApplicationUpdateInput, InheritorApplicationUncheckedUpdateInput>
    /**
     * Choose, which InheritorApplication to update.
     */
    where: InheritorApplicationWhereUniqueInput
  }

  /**
   * InheritorApplication updateMany
   */
  export type InheritorApplicationUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update InheritorApplications.
     */
    data: XOR<InheritorApplicationUpdateManyMutationInput, InheritorApplicationUncheckedUpdateManyInput>
    /**
     * Filter which InheritorApplications to update
     */
    where?: InheritorApplicationWhereInput
  }

  /**
   * InheritorApplication upsert
   */
  export type InheritorApplicationUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * The filter to search for the InheritorApplication to update in case it exists.
     */
    where: InheritorApplicationWhereUniqueInput
    /**
     * In case the InheritorApplication found by the `where` argument doesn't exist, create a new InheritorApplication with this data.
     */
    create: XOR<InheritorApplicationCreateInput, InheritorApplicationUncheckedCreateInput>
    /**
     * In case the InheritorApplication was found with the provided `where` argument, update it with this data.
     */
    update: XOR<InheritorApplicationUpdateInput, InheritorApplicationUncheckedUpdateInput>
  }

  /**
   * InheritorApplication delete
   */
  export type InheritorApplicationDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
    /**
     * Filter which InheritorApplication to delete.
     */
    where: InheritorApplicationWhereUniqueInput
  }

  /**
   * InheritorApplication deleteMany
   */
  export type InheritorApplicationDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which InheritorApplications to delete
     */
    where?: InheritorApplicationWhereInput
  }

  /**
   * InheritorApplication.reviewer
   */
  export type InheritorApplication$reviewerArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    where?: UserWhereInput
  }

  /**
   * InheritorApplication without action
   */
  export type InheritorApplicationDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the InheritorApplication
     */
    select?: InheritorApplicationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: InheritorApplicationInclude<ExtArgs> | null
  }


  /**
   * Model Recipe
   */

  export type AggregateRecipe = {
    _count: RecipeCountAggregateOutputType | null
    _avg: RecipeAvgAggregateOutputType | null
    _sum: RecipeSumAggregateOutputType | null
    _min: RecipeMinAggregateOutputType | null
    _max: RecipeMaxAggregateOutputType | null
  }

  export type RecipeAvgAggregateOutputType = {
    difficulty: number | null
    cookTime: number | null
    servings: number | null
    viewCount: number | null
  }

  export type RecipeSumAggregateOutputType = {
    difficulty: number | null
    cookTime: number | null
    servings: number | null
    viewCount: number | null
  }

  export type RecipeMinAggregateOutputType = {
    id: string | null
    name: string | null
    dialectName: string | null
    dialectAudio: string | null
    region: string | null
    ingredients: string | null
    steps: string | null
    image: string | null
    description: string | null
    difficulty: number | null
    cookTime: number | null
    servings: number | null
    isPublished: boolean | null
    viewCount: number | null
    culturalHistory: string | null
    regionalFeature: string | null
    culturalMeaning: string | null
    inheritanceStory: string | null
    createdAt: Date | null
    updatedAt: Date | null
    authorId: string | null
  }

  export type RecipeMaxAggregateOutputType = {
    id: string | null
    name: string | null
    dialectName: string | null
    dialectAudio: string | null
    region: string | null
    ingredients: string | null
    steps: string | null
    image: string | null
    description: string | null
    difficulty: number | null
    cookTime: number | null
    servings: number | null
    isPublished: boolean | null
    viewCount: number | null
    culturalHistory: string | null
    regionalFeature: string | null
    culturalMeaning: string | null
    inheritanceStory: string | null
    createdAt: Date | null
    updatedAt: Date | null
    authorId: string | null
  }

  export type RecipeCountAggregateOutputType = {
    id: number
    name: number
    dialectName: number
    dialectAudio: number
    region: number
    ingredients: number
    steps: number
    image: number
    description: number
    difficulty: number
    cookTime: number
    servings: number
    isPublished: number
    viewCount: number
    culturalHistory: number
    regionalFeature: number
    culturalMeaning: number
    inheritanceStory: number
    createdAt: number
    updatedAt: number
    authorId: number
    _all: number
  }


  export type RecipeAvgAggregateInputType = {
    difficulty?: true
    cookTime?: true
    servings?: true
    viewCount?: true
  }

  export type RecipeSumAggregateInputType = {
    difficulty?: true
    cookTime?: true
    servings?: true
    viewCount?: true
  }

  export type RecipeMinAggregateInputType = {
    id?: true
    name?: true
    dialectName?: true
    dialectAudio?: true
    region?: true
    ingredients?: true
    steps?: true
    image?: true
    description?: true
    difficulty?: true
    cookTime?: true
    servings?: true
    isPublished?: true
    viewCount?: true
    culturalHistory?: true
    regionalFeature?: true
    culturalMeaning?: true
    inheritanceStory?: true
    createdAt?: true
    updatedAt?: true
    authorId?: true
  }

  export type RecipeMaxAggregateInputType = {
    id?: true
    name?: true
    dialectName?: true
    dialectAudio?: true
    region?: true
    ingredients?: true
    steps?: true
    image?: true
    description?: true
    difficulty?: true
    cookTime?: true
    servings?: true
    isPublished?: true
    viewCount?: true
    culturalHistory?: true
    regionalFeature?: true
    culturalMeaning?: true
    inheritanceStory?: true
    createdAt?: true
    updatedAt?: true
    authorId?: true
  }

  export type RecipeCountAggregateInputType = {
    id?: true
    name?: true
    dialectName?: true
    dialectAudio?: true
    region?: true
    ingredients?: true
    steps?: true
    image?: true
    description?: true
    difficulty?: true
    cookTime?: true
    servings?: true
    isPublished?: true
    viewCount?: true
    culturalHistory?: true
    regionalFeature?: true
    culturalMeaning?: true
    inheritanceStory?: true
    createdAt?: true
    updatedAt?: true
    authorId?: true
    _all?: true
  }

  export type RecipeAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Recipe to aggregate.
     */
    where?: RecipeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Recipes to fetch.
     */
    orderBy?: RecipeOrderByWithRelationInput | RecipeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: RecipeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Recipes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Recipes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Recipes
    **/
    _count?: true | RecipeCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: RecipeAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: RecipeSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: RecipeMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: RecipeMaxAggregateInputType
  }

  export type GetRecipeAggregateType<T extends RecipeAggregateArgs> = {
        [P in keyof T & keyof AggregateRecipe]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateRecipe[P]>
      : GetScalarType<T[P], AggregateRecipe[P]>
  }




  export type RecipeGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecipeWhereInput
    orderBy?: RecipeOrderByWithAggregationInput | RecipeOrderByWithAggregationInput[]
    by: RecipeScalarFieldEnum[] | RecipeScalarFieldEnum
    having?: RecipeScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: RecipeCountAggregateInputType | true
    _avg?: RecipeAvgAggregateInputType
    _sum?: RecipeSumAggregateInputType
    _min?: RecipeMinAggregateInputType
    _max?: RecipeMaxAggregateInputType
  }

  export type RecipeGroupByOutputType = {
    id: string
    name: string
    dialectName: string
    dialectAudio: string | null
    region: string
    ingredients: string
    steps: string
    image: string | null
    description: string | null
    difficulty: number
    cookTime: number | null
    servings: number | null
    isPublished: boolean
    viewCount: number
    culturalHistory: string | null
    regionalFeature: string | null
    culturalMeaning: string | null
    inheritanceStory: string | null
    createdAt: Date
    updatedAt: Date
    authorId: string
    _count: RecipeCountAggregateOutputType | null
    _avg: RecipeAvgAggregateOutputType | null
    _sum: RecipeSumAggregateOutputType | null
    _min: RecipeMinAggregateOutputType | null
    _max: RecipeMaxAggregateOutputType | null
  }

  type GetRecipeGroupByPayload<T extends RecipeGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<RecipeGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof RecipeGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], RecipeGroupByOutputType[P]>
            : GetScalarType<T[P], RecipeGroupByOutputType[P]>
        }
      >
    >


  export type RecipeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    dialectName?: boolean
    dialectAudio?: boolean
    region?: boolean
    ingredients?: boolean
    steps?: boolean
    image?: boolean
    description?: boolean
    difficulty?: boolean
    cookTime?: boolean
    servings?: boolean
    isPublished?: boolean
    viewCount?: boolean
    culturalHistory?: boolean
    regionalFeature?: boolean
    culturalMeaning?: boolean
    inheritanceStory?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    authorId?: boolean
    author?: boolean | UserDefaultArgs<ExtArgs>
    categories?: boolean | Recipe$categoriesArgs<ExtArgs>
    comments?: boolean | Recipe$commentsArgs<ExtArgs>
    favorites?: boolean | Recipe$favoritesArgs<ExtArgs>
    _count?: boolean | RecipeCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["recipe"]>

  export type RecipeSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    dialectName?: boolean
    dialectAudio?: boolean
    region?: boolean
    ingredients?: boolean
    steps?: boolean
    image?: boolean
    description?: boolean
    difficulty?: boolean
    cookTime?: boolean
    servings?: boolean
    isPublished?: boolean
    viewCount?: boolean
    culturalHistory?: boolean
    regionalFeature?: boolean
    culturalMeaning?: boolean
    inheritanceStory?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    authorId?: boolean
    author?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["recipe"]>

  export type RecipeSelectScalar = {
    id?: boolean
    name?: boolean
    dialectName?: boolean
    dialectAudio?: boolean
    region?: boolean
    ingredients?: boolean
    steps?: boolean
    image?: boolean
    description?: boolean
    difficulty?: boolean
    cookTime?: boolean
    servings?: boolean
    isPublished?: boolean
    viewCount?: boolean
    culturalHistory?: boolean
    regionalFeature?: boolean
    culturalMeaning?: boolean
    inheritanceStory?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    authorId?: boolean
  }

  export type RecipeInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    author?: boolean | UserDefaultArgs<ExtArgs>
    categories?: boolean | Recipe$categoriesArgs<ExtArgs>
    comments?: boolean | Recipe$commentsArgs<ExtArgs>
    favorites?: boolean | Recipe$favoritesArgs<ExtArgs>
    _count?: boolean | RecipeCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type RecipeIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    author?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $RecipePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Recipe"
    objects: {
      author: Prisma.$UserPayload<ExtArgs>
      categories: Prisma.$RecipeCategoryPayload<ExtArgs>[]
      comments: Prisma.$CommentPayload<ExtArgs>[]
      favorites: Prisma.$UserFavoritePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      name: string
      dialectName: string
      dialectAudio: string | null
      region: string
      ingredients: string
      steps: string
      image: string | null
      description: string | null
      difficulty: number
      cookTime: number | null
      servings: number | null
      isPublished: boolean
      viewCount: number
      culturalHistory: string | null
      regionalFeature: string | null
      culturalMeaning: string | null
      inheritanceStory: string | null
      createdAt: Date
      updatedAt: Date
      authorId: string
    }, ExtArgs["result"]["recipe"]>
    composites: {}
  }

  type RecipeGetPayload<S extends boolean | null | undefined | RecipeDefaultArgs> = $Result.GetResult<Prisma.$RecipePayload, S>

  type RecipeCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<RecipeFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: RecipeCountAggregateInputType | true
    }

  export interface RecipeDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Recipe'], meta: { name: 'Recipe' } }
    /**
     * Find zero or one Recipe that matches the filter.
     * @param {RecipeFindUniqueArgs} args - Arguments to find a Recipe
     * @example
     * // Get one Recipe
     * const recipe = await prisma.recipe.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends RecipeFindUniqueArgs>(args: SelectSubset<T, RecipeFindUniqueArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one Recipe that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {RecipeFindUniqueOrThrowArgs} args - Arguments to find a Recipe
     * @example
     * // Get one Recipe
     * const recipe = await prisma.recipe.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends RecipeFindUniqueOrThrowArgs>(args: SelectSubset<T, RecipeFindUniqueOrThrowArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first Recipe that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeFindFirstArgs} args - Arguments to find a Recipe
     * @example
     * // Get one Recipe
     * const recipe = await prisma.recipe.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends RecipeFindFirstArgs>(args?: SelectSubset<T, RecipeFindFirstArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first Recipe that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeFindFirstOrThrowArgs} args - Arguments to find a Recipe
     * @example
     * // Get one Recipe
     * const recipe = await prisma.recipe.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends RecipeFindFirstOrThrowArgs>(args?: SelectSubset<T, RecipeFindFirstOrThrowArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Recipes that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Recipes
     * const recipes = await prisma.recipe.findMany()
     * 
     * // Get first 10 Recipes
     * const recipes = await prisma.recipe.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const recipeWithIdOnly = await prisma.recipe.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends RecipeFindManyArgs>(args?: SelectSubset<T, RecipeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findMany">>

    /**
     * Create a Recipe.
     * @param {RecipeCreateArgs} args - Arguments to create a Recipe.
     * @example
     * // Create one Recipe
     * const Recipe = await prisma.recipe.create({
     *   data: {
     *     // ... data to create a Recipe
     *   }
     * })
     * 
     */
    create<T extends RecipeCreateArgs>(args: SelectSubset<T, RecipeCreateArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Recipes.
     * @param {RecipeCreateManyArgs} args - Arguments to create many Recipes.
     * @example
     * // Create many Recipes
     * const recipe = await prisma.recipe.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends RecipeCreateManyArgs>(args?: SelectSubset<T, RecipeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Recipes and returns the data saved in the database.
     * @param {RecipeCreateManyAndReturnArgs} args - Arguments to create many Recipes.
     * @example
     * // Create many Recipes
     * const recipe = await prisma.recipe.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Recipes and only return the `id`
     * const recipeWithIdOnly = await prisma.recipe.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends RecipeCreateManyAndReturnArgs>(args?: SelectSubset<T, RecipeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a Recipe.
     * @param {RecipeDeleteArgs} args - Arguments to delete one Recipe.
     * @example
     * // Delete one Recipe
     * const Recipe = await prisma.recipe.delete({
     *   where: {
     *     // ... filter to delete one Recipe
     *   }
     * })
     * 
     */
    delete<T extends RecipeDeleteArgs>(args: SelectSubset<T, RecipeDeleteArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one Recipe.
     * @param {RecipeUpdateArgs} args - Arguments to update one Recipe.
     * @example
     * // Update one Recipe
     * const recipe = await prisma.recipe.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends RecipeUpdateArgs>(args: SelectSubset<T, RecipeUpdateArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Recipes.
     * @param {RecipeDeleteManyArgs} args - Arguments to filter Recipes to delete.
     * @example
     * // Delete a few Recipes
     * const { count } = await prisma.recipe.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends RecipeDeleteManyArgs>(args?: SelectSubset<T, RecipeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Recipes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Recipes
     * const recipe = await prisma.recipe.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends RecipeUpdateManyArgs>(args: SelectSubset<T, RecipeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Recipe.
     * @param {RecipeUpsertArgs} args - Arguments to update or create a Recipe.
     * @example
     * // Update or create a Recipe
     * const recipe = await prisma.recipe.upsert({
     *   create: {
     *     // ... data to create a Recipe
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Recipe we want to update
     *   }
     * })
     */
    upsert<T extends RecipeUpsertArgs>(args: SelectSubset<T, RecipeUpsertArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Recipes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeCountArgs} args - Arguments to filter Recipes to count.
     * @example
     * // Count the number of Recipes
     * const count = await prisma.recipe.count({
     *   where: {
     *     // ... the filter for the Recipes we want to count
     *   }
     * })
    **/
    count<T extends RecipeCountArgs>(
      args?: Subset<T, RecipeCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], RecipeCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Recipe.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends RecipeAggregateArgs>(args: Subset<T, RecipeAggregateArgs>): Prisma.PrismaPromise<GetRecipeAggregateType<T>>

    /**
     * Group by Recipe.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends RecipeGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: RecipeGroupByArgs['orderBy'] }
        : { orderBy?: RecipeGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, RecipeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRecipeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Recipe model
   */
  readonly fields: RecipeFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Recipe.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__RecipeClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    author<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    categories<T extends Recipe$categoriesArgs<ExtArgs> = {}>(args?: Subset<T, Recipe$categoriesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "findMany"> | Null>
    comments<T extends Recipe$commentsArgs<ExtArgs> = {}>(args?: Subset<T, Recipe$commentsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "findMany"> | Null>
    favorites<T extends Recipe$favoritesArgs<ExtArgs> = {}>(args?: Subset<T, Recipe$favoritesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Recipe model
   */ 
  interface RecipeFieldRefs {
    readonly id: FieldRef<"Recipe", 'String'>
    readonly name: FieldRef<"Recipe", 'String'>
    readonly dialectName: FieldRef<"Recipe", 'String'>
    readonly dialectAudio: FieldRef<"Recipe", 'String'>
    readonly region: FieldRef<"Recipe", 'String'>
    readonly ingredients: FieldRef<"Recipe", 'String'>
    readonly steps: FieldRef<"Recipe", 'String'>
    readonly image: FieldRef<"Recipe", 'String'>
    readonly description: FieldRef<"Recipe", 'String'>
    readonly difficulty: FieldRef<"Recipe", 'Int'>
    readonly cookTime: FieldRef<"Recipe", 'Int'>
    readonly servings: FieldRef<"Recipe", 'Int'>
    readonly isPublished: FieldRef<"Recipe", 'Boolean'>
    readonly viewCount: FieldRef<"Recipe", 'Int'>
    readonly culturalHistory: FieldRef<"Recipe", 'String'>
    readonly regionalFeature: FieldRef<"Recipe", 'String'>
    readonly culturalMeaning: FieldRef<"Recipe", 'String'>
    readonly inheritanceStory: FieldRef<"Recipe", 'String'>
    readonly createdAt: FieldRef<"Recipe", 'DateTime'>
    readonly updatedAt: FieldRef<"Recipe", 'DateTime'>
    readonly authorId: FieldRef<"Recipe", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Recipe findUnique
   */
  export type RecipeFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * Filter, which Recipe to fetch.
     */
    where: RecipeWhereUniqueInput
  }

  /**
   * Recipe findUniqueOrThrow
   */
  export type RecipeFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * Filter, which Recipe to fetch.
     */
    where: RecipeWhereUniqueInput
  }

  /**
   * Recipe findFirst
   */
  export type RecipeFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * Filter, which Recipe to fetch.
     */
    where?: RecipeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Recipes to fetch.
     */
    orderBy?: RecipeOrderByWithRelationInput | RecipeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Recipes.
     */
    cursor?: RecipeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Recipes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Recipes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Recipes.
     */
    distinct?: RecipeScalarFieldEnum | RecipeScalarFieldEnum[]
  }

  /**
   * Recipe findFirstOrThrow
   */
  export type RecipeFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * Filter, which Recipe to fetch.
     */
    where?: RecipeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Recipes to fetch.
     */
    orderBy?: RecipeOrderByWithRelationInput | RecipeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Recipes.
     */
    cursor?: RecipeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Recipes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Recipes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Recipes.
     */
    distinct?: RecipeScalarFieldEnum | RecipeScalarFieldEnum[]
  }

  /**
   * Recipe findMany
   */
  export type RecipeFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * Filter, which Recipes to fetch.
     */
    where?: RecipeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Recipes to fetch.
     */
    orderBy?: RecipeOrderByWithRelationInput | RecipeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Recipes.
     */
    cursor?: RecipeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Recipes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Recipes.
     */
    skip?: number
    distinct?: RecipeScalarFieldEnum | RecipeScalarFieldEnum[]
  }

  /**
   * Recipe create
   */
  export type RecipeCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * The data needed to create a Recipe.
     */
    data: XOR<RecipeCreateInput, RecipeUncheckedCreateInput>
  }

  /**
   * Recipe createMany
   */
  export type RecipeCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Recipes.
     */
    data: RecipeCreateManyInput | RecipeCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Recipe createManyAndReturn
   */
  export type RecipeCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Recipes.
     */
    data: RecipeCreateManyInput | RecipeCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Recipe update
   */
  export type RecipeUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * The data needed to update a Recipe.
     */
    data: XOR<RecipeUpdateInput, RecipeUncheckedUpdateInput>
    /**
     * Choose, which Recipe to update.
     */
    where: RecipeWhereUniqueInput
  }

  /**
   * Recipe updateMany
   */
  export type RecipeUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Recipes.
     */
    data: XOR<RecipeUpdateManyMutationInput, RecipeUncheckedUpdateManyInput>
    /**
     * Filter which Recipes to update
     */
    where?: RecipeWhereInput
  }

  /**
   * Recipe upsert
   */
  export type RecipeUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * The filter to search for the Recipe to update in case it exists.
     */
    where: RecipeWhereUniqueInput
    /**
     * In case the Recipe found by the `where` argument doesn't exist, create a new Recipe with this data.
     */
    create: XOR<RecipeCreateInput, RecipeUncheckedCreateInput>
    /**
     * In case the Recipe was found with the provided `where` argument, update it with this data.
     */
    update: XOR<RecipeUpdateInput, RecipeUncheckedUpdateInput>
  }

  /**
   * Recipe delete
   */
  export type RecipeDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
    /**
     * Filter which Recipe to delete.
     */
    where: RecipeWhereUniqueInput
  }

  /**
   * Recipe deleteMany
   */
  export type RecipeDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Recipes to delete
     */
    where?: RecipeWhereInput
  }

  /**
   * Recipe.categories
   */
  export type Recipe$categoriesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    where?: RecipeCategoryWhereInput
    orderBy?: RecipeCategoryOrderByWithRelationInput | RecipeCategoryOrderByWithRelationInput[]
    cursor?: RecipeCategoryWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RecipeCategoryScalarFieldEnum | RecipeCategoryScalarFieldEnum[]
  }

  /**
   * Recipe.comments
   */
  export type Recipe$commentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    where?: CommentWhereInput
    orderBy?: CommentOrderByWithRelationInput | CommentOrderByWithRelationInput[]
    cursor?: CommentWhereUniqueInput
    take?: number
    skip?: number
    distinct?: CommentScalarFieldEnum | CommentScalarFieldEnum[]
  }

  /**
   * Recipe.favorites
   */
  export type Recipe$favoritesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    where?: UserFavoriteWhereInput
    orderBy?: UserFavoriteOrderByWithRelationInput | UserFavoriteOrderByWithRelationInput[]
    cursor?: UserFavoriteWhereUniqueInput
    take?: number
    skip?: number
    distinct?: UserFavoriteScalarFieldEnum | UserFavoriteScalarFieldEnum[]
  }

  /**
   * Recipe without action
   */
  export type RecipeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Recipe
     */
    select?: RecipeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeInclude<ExtArgs> | null
  }


  /**
   * Model Category
   */

  export type AggregateCategory = {
    _count: CategoryCountAggregateOutputType | null
    _min: CategoryMinAggregateOutputType | null
    _max: CategoryMaxAggregateOutputType | null
  }

  export type CategoryMinAggregateOutputType = {
    id: string | null
    name: string | null
    description: string | null
    icon: string | null
    createdAt: Date | null
  }

  export type CategoryMaxAggregateOutputType = {
    id: string | null
    name: string | null
    description: string | null
    icon: string | null
    createdAt: Date | null
  }

  export type CategoryCountAggregateOutputType = {
    id: number
    name: number
    description: number
    icon: number
    createdAt: number
    _all: number
  }


  export type CategoryMinAggregateInputType = {
    id?: true
    name?: true
    description?: true
    icon?: true
    createdAt?: true
  }

  export type CategoryMaxAggregateInputType = {
    id?: true
    name?: true
    description?: true
    icon?: true
    createdAt?: true
  }

  export type CategoryCountAggregateInputType = {
    id?: true
    name?: true
    description?: true
    icon?: true
    createdAt?: true
    _all?: true
  }

  export type CategoryAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Category to aggregate.
     */
    where?: CategoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Categories to fetch.
     */
    orderBy?: CategoryOrderByWithRelationInput | CategoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CategoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Categories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Categories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Categories
    **/
    _count?: true | CategoryCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CategoryMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CategoryMaxAggregateInputType
  }

  export type GetCategoryAggregateType<T extends CategoryAggregateArgs> = {
        [P in keyof T & keyof AggregateCategory]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateCategory[P]>
      : GetScalarType<T[P], AggregateCategory[P]>
  }




  export type CategoryGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CategoryWhereInput
    orderBy?: CategoryOrderByWithAggregationInput | CategoryOrderByWithAggregationInput[]
    by: CategoryScalarFieldEnum[] | CategoryScalarFieldEnum
    having?: CategoryScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CategoryCountAggregateInputType | true
    _min?: CategoryMinAggregateInputType
    _max?: CategoryMaxAggregateInputType
  }

  export type CategoryGroupByOutputType = {
    id: string
    name: string
    description: string | null
    icon: string | null
    createdAt: Date
    _count: CategoryCountAggregateOutputType | null
    _min: CategoryMinAggregateOutputType | null
    _max: CategoryMaxAggregateOutputType | null
  }

  type GetCategoryGroupByPayload<T extends CategoryGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CategoryGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CategoryGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CategoryGroupByOutputType[P]>
            : GetScalarType<T[P], CategoryGroupByOutputType[P]>
        }
      >
    >


  export type CategorySelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    icon?: boolean
    createdAt?: boolean
    recipes?: boolean | Category$recipesArgs<ExtArgs>
    _count?: boolean | CategoryCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["category"]>

  export type CategorySelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    icon?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["category"]>

  export type CategorySelectScalar = {
    id?: boolean
    name?: boolean
    description?: boolean
    icon?: boolean
    createdAt?: boolean
  }

  export type CategoryInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    recipes?: boolean | Category$recipesArgs<ExtArgs>
    _count?: boolean | CategoryCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type CategoryIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $CategoryPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Category"
    objects: {
      recipes: Prisma.$RecipeCategoryPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      name: string
      description: string | null
      icon: string | null
      createdAt: Date
    }, ExtArgs["result"]["category"]>
    composites: {}
  }

  type CategoryGetPayload<S extends boolean | null | undefined | CategoryDefaultArgs> = $Result.GetResult<Prisma.$CategoryPayload, S>

  type CategoryCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<CategoryFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: CategoryCountAggregateInputType | true
    }

  export interface CategoryDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Category'], meta: { name: 'Category' } }
    /**
     * Find zero or one Category that matches the filter.
     * @param {CategoryFindUniqueArgs} args - Arguments to find a Category
     * @example
     * // Get one Category
     * const category = await prisma.category.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CategoryFindUniqueArgs>(args: SelectSubset<T, CategoryFindUniqueArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one Category that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {CategoryFindUniqueOrThrowArgs} args - Arguments to find a Category
     * @example
     * // Get one Category
     * const category = await prisma.category.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CategoryFindUniqueOrThrowArgs>(args: SelectSubset<T, CategoryFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first Category that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CategoryFindFirstArgs} args - Arguments to find a Category
     * @example
     * // Get one Category
     * const category = await prisma.category.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CategoryFindFirstArgs>(args?: SelectSubset<T, CategoryFindFirstArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first Category that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CategoryFindFirstOrThrowArgs} args - Arguments to find a Category
     * @example
     * // Get one Category
     * const category = await prisma.category.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CategoryFindFirstOrThrowArgs>(args?: SelectSubset<T, CategoryFindFirstOrThrowArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Categories that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CategoryFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Categories
     * const categories = await prisma.category.findMany()
     * 
     * // Get first 10 Categories
     * const categories = await prisma.category.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const categoryWithIdOnly = await prisma.category.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CategoryFindManyArgs>(args?: SelectSubset<T, CategoryFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a Category.
     * @param {CategoryCreateArgs} args - Arguments to create a Category.
     * @example
     * // Create one Category
     * const Category = await prisma.category.create({
     *   data: {
     *     // ... data to create a Category
     *   }
     * })
     * 
     */
    create<T extends CategoryCreateArgs>(args: SelectSubset<T, CategoryCreateArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Categories.
     * @param {CategoryCreateManyArgs} args - Arguments to create many Categories.
     * @example
     * // Create many Categories
     * const category = await prisma.category.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CategoryCreateManyArgs>(args?: SelectSubset<T, CategoryCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Categories and returns the data saved in the database.
     * @param {CategoryCreateManyAndReturnArgs} args - Arguments to create many Categories.
     * @example
     * // Create many Categories
     * const category = await prisma.category.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Categories and only return the `id`
     * const categoryWithIdOnly = await prisma.category.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends CategoryCreateManyAndReturnArgs>(args?: SelectSubset<T, CategoryCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a Category.
     * @param {CategoryDeleteArgs} args - Arguments to delete one Category.
     * @example
     * // Delete one Category
     * const Category = await prisma.category.delete({
     *   where: {
     *     // ... filter to delete one Category
     *   }
     * })
     * 
     */
    delete<T extends CategoryDeleteArgs>(args: SelectSubset<T, CategoryDeleteArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one Category.
     * @param {CategoryUpdateArgs} args - Arguments to update one Category.
     * @example
     * // Update one Category
     * const category = await prisma.category.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CategoryUpdateArgs>(args: SelectSubset<T, CategoryUpdateArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Categories.
     * @param {CategoryDeleteManyArgs} args - Arguments to filter Categories to delete.
     * @example
     * // Delete a few Categories
     * const { count } = await prisma.category.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CategoryDeleteManyArgs>(args?: SelectSubset<T, CategoryDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Categories.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CategoryUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Categories
     * const category = await prisma.category.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CategoryUpdateManyArgs>(args: SelectSubset<T, CategoryUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Category.
     * @param {CategoryUpsertArgs} args - Arguments to update or create a Category.
     * @example
     * // Update or create a Category
     * const category = await prisma.category.upsert({
     *   create: {
     *     // ... data to create a Category
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Category we want to update
     *   }
     * })
     */
    upsert<T extends CategoryUpsertArgs>(args: SelectSubset<T, CategoryUpsertArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Categories.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CategoryCountArgs} args - Arguments to filter Categories to count.
     * @example
     * // Count the number of Categories
     * const count = await prisma.category.count({
     *   where: {
     *     // ... the filter for the Categories we want to count
     *   }
     * })
    **/
    count<T extends CategoryCountArgs>(
      args?: Subset<T, CategoryCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CategoryCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Category.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CategoryAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CategoryAggregateArgs>(args: Subset<T, CategoryAggregateArgs>): Prisma.PrismaPromise<GetCategoryAggregateType<T>>

    /**
     * Group by Category.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CategoryGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CategoryGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CategoryGroupByArgs['orderBy'] }
        : { orderBy?: CategoryGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CategoryGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCategoryGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Category model
   */
  readonly fields: CategoryFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Category.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CategoryClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    recipes<T extends Category$recipesArgs<ExtArgs> = {}>(args?: Subset<T, Category$recipesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Category model
   */ 
  interface CategoryFieldRefs {
    readonly id: FieldRef<"Category", 'String'>
    readonly name: FieldRef<"Category", 'String'>
    readonly description: FieldRef<"Category", 'String'>
    readonly icon: FieldRef<"Category", 'String'>
    readonly createdAt: FieldRef<"Category", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Category findUnique
   */
  export type CategoryFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * Filter, which Category to fetch.
     */
    where: CategoryWhereUniqueInput
  }

  /**
   * Category findUniqueOrThrow
   */
  export type CategoryFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * Filter, which Category to fetch.
     */
    where: CategoryWhereUniqueInput
  }

  /**
   * Category findFirst
   */
  export type CategoryFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * Filter, which Category to fetch.
     */
    where?: CategoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Categories to fetch.
     */
    orderBy?: CategoryOrderByWithRelationInput | CategoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Categories.
     */
    cursor?: CategoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Categories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Categories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Categories.
     */
    distinct?: CategoryScalarFieldEnum | CategoryScalarFieldEnum[]
  }

  /**
   * Category findFirstOrThrow
   */
  export type CategoryFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * Filter, which Category to fetch.
     */
    where?: CategoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Categories to fetch.
     */
    orderBy?: CategoryOrderByWithRelationInput | CategoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Categories.
     */
    cursor?: CategoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Categories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Categories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Categories.
     */
    distinct?: CategoryScalarFieldEnum | CategoryScalarFieldEnum[]
  }

  /**
   * Category findMany
   */
  export type CategoryFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * Filter, which Categories to fetch.
     */
    where?: CategoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Categories to fetch.
     */
    orderBy?: CategoryOrderByWithRelationInput | CategoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Categories.
     */
    cursor?: CategoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Categories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Categories.
     */
    skip?: number
    distinct?: CategoryScalarFieldEnum | CategoryScalarFieldEnum[]
  }

  /**
   * Category create
   */
  export type CategoryCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * The data needed to create a Category.
     */
    data: XOR<CategoryCreateInput, CategoryUncheckedCreateInput>
  }

  /**
   * Category createMany
   */
  export type CategoryCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Categories.
     */
    data: CategoryCreateManyInput | CategoryCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Category createManyAndReturn
   */
  export type CategoryCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Categories.
     */
    data: CategoryCreateManyInput | CategoryCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Category update
   */
  export type CategoryUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * The data needed to update a Category.
     */
    data: XOR<CategoryUpdateInput, CategoryUncheckedUpdateInput>
    /**
     * Choose, which Category to update.
     */
    where: CategoryWhereUniqueInput
  }

  /**
   * Category updateMany
   */
  export type CategoryUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Categories.
     */
    data: XOR<CategoryUpdateManyMutationInput, CategoryUncheckedUpdateManyInput>
    /**
     * Filter which Categories to update
     */
    where?: CategoryWhereInput
  }

  /**
   * Category upsert
   */
  export type CategoryUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * The filter to search for the Category to update in case it exists.
     */
    where: CategoryWhereUniqueInput
    /**
     * In case the Category found by the `where` argument doesn't exist, create a new Category with this data.
     */
    create: XOR<CategoryCreateInput, CategoryUncheckedCreateInput>
    /**
     * In case the Category was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CategoryUpdateInput, CategoryUncheckedUpdateInput>
  }

  /**
   * Category delete
   */
  export type CategoryDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
    /**
     * Filter which Category to delete.
     */
    where: CategoryWhereUniqueInput
  }

  /**
   * Category deleteMany
   */
  export type CategoryDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Categories to delete
     */
    where?: CategoryWhereInput
  }

  /**
   * Category.recipes
   */
  export type Category$recipesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    where?: RecipeCategoryWhereInput
    orderBy?: RecipeCategoryOrderByWithRelationInput | RecipeCategoryOrderByWithRelationInput[]
    cursor?: RecipeCategoryWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RecipeCategoryScalarFieldEnum | RecipeCategoryScalarFieldEnum[]
  }

  /**
   * Category without action
   */
  export type CategoryDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Category
     */
    select?: CategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CategoryInclude<ExtArgs> | null
  }


  /**
   * Model RecipeCategory
   */

  export type AggregateRecipeCategory = {
    _count: RecipeCategoryCountAggregateOutputType | null
    _min: RecipeCategoryMinAggregateOutputType | null
    _max: RecipeCategoryMaxAggregateOutputType | null
  }

  export type RecipeCategoryMinAggregateOutputType = {
    recipeId: string | null
    categoryId: string | null
  }

  export type RecipeCategoryMaxAggregateOutputType = {
    recipeId: string | null
    categoryId: string | null
  }

  export type RecipeCategoryCountAggregateOutputType = {
    recipeId: number
    categoryId: number
    _all: number
  }


  export type RecipeCategoryMinAggregateInputType = {
    recipeId?: true
    categoryId?: true
  }

  export type RecipeCategoryMaxAggregateInputType = {
    recipeId?: true
    categoryId?: true
  }

  export type RecipeCategoryCountAggregateInputType = {
    recipeId?: true
    categoryId?: true
    _all?: true
  }

  export type RecipeCategoryAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which RecipeCategory to aggregate.
     */
    where?: RecipeCategoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RecipeCategories to fetch.
     */
    orderBy?: RecipeCategoryOrderByWithRelationInput | RecipeCategoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: RecipeCategoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RecipeCategories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RecipeCategories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned RecipeCategories
    **/
    _count?: true | RecipeCategoryCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: RecipeCategoryMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: RecipeCategoryMaxAggregateInputType
  }

  export type GetRecipeCategoryAggregateType<T extends RecipeCategoryAggregateArgs> = {
        [P in keyof T & keyof AggregateRecipeCategory]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateRecipeCategory[P]>
      : GetScalarType<T[P], AggregateRecipeCategory[P]>
  }




  export type RecipeCategoryGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RecipeCategoryWhereInput
    orderBy?: RecipeCategoryOrderByWithAggregationInput | RecipeCategoryOrderByWithAggregationInput[]
    by: RecipeCategoryScalarFieldEnum[] | RecipeCategoryScalarFieldEnum
    having?: RecipeCategoryScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: RecipeCategoryCountAggregateInputType | true
    _min?: RecipeCategoryMinAggregateInputType
    _max?: RecipeCategoryMaxAggregateInputType
  }

  export type RecipeCategoryGroupByOutputType = {
    recipeId: string
    categoryId: string
    _count: RecipeCategoryCountAggregateOutputType | null
    _min: RecipeCategoryMinAggregateOutputType | null
    _max: RecipeCategoryMaxAggregateOutputType | null
  }

  type GetRecipeCategoryGroupByPayload<T extends RecipeCategoryGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<RecipeCategoryGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof RecipeCategoryGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], RecipeCategoryGroupByOutputType[P]>
            : GetScalarType<T[P], RecipeCategoryGroupByOutputType[P]>
        }
      >
    >


  export type RecipeCategorySelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    recipeId?: boolean
    categoryId?: boolean
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
    category?: boolean | CategoryDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["recipeCategory"]>

  export type RecipeCategorySelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    recipeId?: boolean
    categoryId?: boolean
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
    category?: boolean | CategoryDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["recipeCategory"]>

  export type RecipeCategorySelectScalar = {
    recipeId?: boolean
    categoryId?: boolean
  }

  export type RecipeCategoryInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
    category?: boolean | CategoryDefaultArgs<ExtArgs>
  }
  export type RecipeCategoryIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
    category?: boolean | CategoryDefaultArgs<ExtArgs>
  }

  export type $RecipeCategoryPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "RecipeCategory"
    objects: {
      recipe: Prisma.$RecipePayload<ExtArgs>
      category: Prisma.$CategoryPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      recipeId: string
      categoryId: string
    }, ExtArgs["result"]["recipeCategory"]>
    composites: {}
  }

  type RecipeCategoryGetPayload<S extends boolean | null | undefined | RecipeCategoryDefaultArgs> = $Result.GetResult<Prisma.$RecipeCategoryPayload, S>

  type RecipeCategoryCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<RecipeCategoryFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: RecipeCategoryCountAggregateInputType | true
    }

  export interface RecipeCategoryDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['RecipeCategory'], meta: { name: 'RecipeCategory' } }
    /**
     * Find zero or one RecipeCategory that matches the filter.
     * @param {RecipeCategoryFindUniqueArgs} args - Arguments to find a RecipeCategory
     * @example
     * // Get one RecipeCategory
     * const recipeCategory = await prisma.recipeCategory.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends RecipeCategoryFindUniqueArgs>(args: SelectSubset<T, RecipeCategoryFindUniqueArgs<ExtArgs>>): Prisma__RecipeCategoryClient<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one RecipeCategory that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {RecipeCategoryFindUniqueOrThrowArgs} args - Arguments to find a RecipeCategory
     * @example
     * // Get one RecipeCategory
     * const recipeCategory = await prisma.recipeCategory.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends RecipeCategoryFindUniqueOrThrowArgs>(args: SelectSubset<T, RecipeCategoryFindUniqueOrThrowArgs<ExtArgs>>): Prisma__RecipeCategoryClient<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first RecipeCategory that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeCategoryFindFirstArgs} args - Arguments to find a RecipeCategory
     * @example
     * // Get one RecipeCategory
     * const recipeCategory = await prisma.recipeCategory.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends RecipeCategoryFindFirstArgs>(args?: SelectSubset<T, RecipeCategoryFindFirstArgs<ExtArgs>>): Prisma__RecipeCategoryClient<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first RecipeCategory that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeCategoryFindFirstOrThrowArgs} args - Arguments to find a RecipeCategory
     * @example
     * // Get one RecipeCategory
     * const recipeCategory = await prisma.recipeCategory.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends RecipeCategoryFindFirstOrThrowArgs>(args?: SelectSubset<T, RecipeCategoryFindFirstOrThrowArgs<ExtArgs>>): Prisma__RecipeCategoryClient<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more RecipeCategories that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeCategoryFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all RecipeCategories
     * const recipeCategories = await prisma.recipeCategory.findMany()
     * 
     * // Get first 10 RecipeCategories
     * const recipeCategories = await prisma.recipeCategory.findMany({ take: 10 })
     * 
     * // Only select the `recipeId`
     * const recipeCategoryWithRecipeIdOnly = await prisma.recipeCategory.findMany({ select: { recipeId: true } })
     * 
     */
    findMany<T extends RecipeCategoryFindManyArgs>(args?: SelectSubset<T, RecipeCategoryFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a RecipeCategory.
     * @param {RecipeCategoryCreateArgs} args - Arguments to create a RecipeCategory.
     * @example
     * // Create one RecipeCategory
     * const RecipeCategory = await prisma.recipeCategory.create({
     *   data: {
     *     // ... data to create a RecipeCategory
     *   }
     * })
     * 
     */
    create<T extends RecipeCategoryCreateArgs>(args: SelectSubset<T, RecipeCategoryCreateArgs<ExtArgs>>): Prisma__RecipeCategoryClient<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many RecipeCategories.
     * @param {RecipeCategoryCreateManyArgs} args - Arguments to create many RecipeCategories.
     * @example
     * // Create many RecipeCategories
     * const recipeCategory = await prisma.recipeCategory.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends RecipeCategoryCreateManyArgs>(args?: SelectSubset<T, RecipeCategoryCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many RecipeCategories and returns the data saved in the database.
     * @param {RecipeCategoryCreateManyAndReturnArgs} args - Arguments to create many RecipeCategories.
     * @example
     * // Create many RecipeCategories
     * const recipeCategory = await prisma.recipeCategory.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many RecipeCategories and only return the `recipeId`
     * const recipeCategoryWithRecipeIdOnly = await prisma.recipeCategory.createManyAndReturn({ 
     *   select: { recipeId: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends RecipeCategoryCreateManyAndReturnArgs>(args?: SelectSubset<T, RecipeCategoryCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a RecipeCategory.
     * @param {RecipeCategoryDeleteArgs} args - Arguments to delete one RecipeCategory.
     * @example
     * // Delete one RecipeCategory
     * const RecipeCategory = await prisma.recipeCategory.delete({
     *   where: {
     *     // ... filter to delete one RecipeCategory
     *   }
     * })
     * 
     */
    delete<T extends RecipeCategoryDeleteArgs>(args: SelectSubset<T, RecipeCategoryDeleteArgs<ExtArgs>>): Prisma__RecipeCategoryClient<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one RecipeCategory.
     * @param {RecipeCategoryUpdateArgs} args - Arguments to update one RecipeCategory.
     * @example
     * // Update one RecipeCategory
     * const recipeCategory = await prisma.recipeCategory.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends RecipeCategoryUpdateArgs>(args: SelectSubset<T, RecipeCategoryUpdateArgs<ExtArgs>>): Prisma__RecipeCategoryClient<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more RecipeCategories.
     * @param {RecipeCategoryDeleteManyArgs} args - Arguments to filter RecipeCategories to delete.
     * @example
     * // Delete a few RecipeCategories
     * const { count } = await prisma.recipeCategory.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends RecipeCategoryDeleteManyArgs>(args?: SelectSubset<T, RecipeCategoryDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more RecipeCategories.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeCategoryUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many RecipeCategories
     * const recipeCategory = await prisma.recipeCategory.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends RecipeCategoryUpdateManyArgs>(args: SelectSubset<T, RecipeCategoryUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one RecipeCategory.
     * @param {RecipeCategoryUpsertArgs} args - Arguments to update or create a RecipeCategory.
     * @example
     * // Update or create a RecipeCategory
     * const recipeCategory = await prisma.recipeCategory.upsert({
     *   create: {
     *     // ... data to create a RecipeCategory
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the RecipeCategory we want to update
     *   }
     * })
     */
    upsert<T extends RecipeCategoryUpsertArgs>(args: SelectSubset<T, RecipeCategoryUpsertArgs<ExtArgs>>): Prisma__RecipeCategoryClient<$Result.GetResult<Prisma.$RecipeCategoryPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of RecipeCategories.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeCategoryCountArgs} args - Arguments to filter RecipeCategories to count.
     * @example
     * // Count the number of RecipeCategories
     * const count = await prisma.recipeCategory.count({
     *   where: {
     *     // ... the filter for the RecipeCategories we want to count
     *   }
     * })
    **/
    count<T extends RecipeCategoryCountArgs>(
      args?: Subset<T, RecipeCategoryCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], RecipeCategoryCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a RecipeCategory.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeCategoryAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends RecipeCategoryAggregateArgs>(args: Subset<T, RecipeCategoryAggregateArgs>): Prisma.PrismaPromise<GetRecipeCategoryAggregateType<T>>

    /**
     * Group by RecipeCategory.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RecipeCategoryGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends RecipeCategoryGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: RecipeCategoryGroupByArgs['orderBy'] }
        : { orderBy?: RecipeCategoryGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, RecipeCategoryGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRecipeCategoryGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the RecipeCategory model
   */
  readonly fields: RecipeCategoryFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for RecipeCategory.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__RecipeCategoryClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    recipe<T extends RecipeDefaultArgs<ExtArgs> = {}>(args?: Subset<T, RecipeDefaultArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    category<T extends CategoryDefaultArgs<ExtArgs> = {}>(args?: Subset<T, CategoryDefaultArgs<ExtArgs>>): Prisma__CategoryClient<$Result.GetResult<Prisma.$CategoryPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the RecipeCategory model
   */ 
  interface RecipeCategoryFieldRefs {
    readonly recipeId: FieldRef<"RecipeCategory", 'String'>
    readonly categoryId: FieldRef<"RecipeCategory", 'String'>
  }
    

  // Custom InputTypes
  /**
   * RecipeCategory findUnique
   */
  export type RecipeCategoryFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * Filter, which RecipeCategory to fetch.
     */
    where: RecipeCategoryWhereUniqueInput
  }

  /**
   * RecipeCategory findUniqueOrThrow
   */
  export type RecipeCategoryFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * Filter, which RecipeCategory to fetch.
     */
    where: RecipeCategoryWhereUniqueInput
  }

  /**
   * RecipeCategory findFirst
   */
  export type RecipeCategoryFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * Filter, which RecipeCategory to fetch.
     */
    where?: RecipeCategoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RecipeCategories to fetch.
     */
    orderBy?: RecipeCategoryOrderByWithRelationInput | RecipeCategoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for RecipeCategories.
     */
    cursor?: RecipeCategoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RecipeCategories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RecipeCategories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of RecipeCategories.
     */
    distinct?: RecipeCategoryScalarFieldEnum | RecipeCategoryScalarFieldEnum[]
  }

  /**
   * RecipeCategory findFirstOrThrow
   */
  export type RecipeCategoryFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * Filter, which RecipeCategory to fetch.
     */
    where?: RecipeCategoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RecipeCategories to fetch.
     */
    orderBy?: RecipeCategoryOrderByWithRelationInput | RecipeCategoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for RecipeCategories.
     */
    cursor?: RecipeCategoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RecipeCategories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RecipeCategories.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of RecipeCategories.
     */
    distinct?: RecipeCategoryScalarFieldEnum | RecipeCategoryScalarFieldEnum[]
  }

  /**
   * RecipeCategory findMany
   */
  export type RecipeCategoryFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * Filter, which RecipeCategories to fetch.
     */
    where?: RecipeCategoryWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RecipeCategories to fetch.
     */
    orderBy?: RecipeCategoryOrderByWithRelationInput | RecipeCategoryOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing RecipeCategories.
     */
    cursor?: RecipeCategoryWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RecipeCategories from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RecipeCategories.
     */
    skip?: number
    distinct?: RecipeCategoryScalarFieldEnum | RecipeCategoryScalarFieldEnum[]
  }

  /**
   * RecipeCategory create
   */
  export type RecipeCategoryCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * The data needed to create a RecipeCategory.
     */
    data: XOR<RecipeCategoryCreateInput, RecipeCategoryUncheckedCreateInput>
  }

  /**
   * RecipeCategory createMany
   */
  export type RecipeCategoryCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many RecipeCategories.
     */
    data: RecipeCategoryCreateManyInput | RecipeCategoryCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * RecipeCategory createManyAndReturn
   */
  export type RecipeCategoryCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many RecipeCategories.
     */
    data: RecipeCategoryCreateManyInput | RecipeCategoryCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * RecipeCategory update
   */
  export type RecipeCategoryUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * The data needed to update a RecipeCategory.
     */
    data: XOR<RecipeCategoryUpdateInput, RecipeCategoryUncheckedUpdateInput>
    /**
     * Choose, which RecipeCategory to update.
     */
    where: RecipeCategoryWhereUniqueInput
  }

  /**
   * RecipeCategory updateMany
   */
  export type RecipeCategoryUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update RecipeCategories.
     */
    data: XOR<RecipeCategoryUpdateManyMutationInput, RecipeCategoryUncheckedUpdateManyInput>
    /**
     * Filter which RecipeCategories to update
     */
    where?: RecipeCategoryWhereInput
  }

  /**
   * RecipeCategory upsert
   */
  export type RecipeCategoryUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * The filter to search for the RecipeCategory to update in case it exists.
     */
    where: RecipeCategoryWhereUniqueInput
    /**
     * In case the RecipeCategory found by the `where` argument doesn't exist, create a new RecipeCategory with this data.
     */
    create: XOR<RecipeCategoryCreateInput, RecipeCategoryUncheckedCreateInput>
    /**
     * In case the RecipeCategory was found with the provided `where` argument, update it with this data.
     */
    update: XOR<RecipeCategoryUpdateInput, RecipeCategoryUncheckedUpdateInput>
  }

  /**
   * RecipeCategory delete
   */
  export type RecipeCategoryDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
    /**
     * Filter which RecipeCategory to delete.
     */
    where: RecipeCategoryWhereUniqueInput
  }

  /**
   * RecipeCategory deleteMany
   */
  export type RecipeCategoryDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which RecipeCategories to delete
     */
    where?: RecipeCategoryWhereInput
  }

  /**
   * RecipeCategory without action
   */
  export type RecipeCategoryDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RecipeCategory
     */
    select?: RecipeCategorySelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: RecipeCategoryInclude<ExtArgs> | null
  }


  /**
   * Model Comment
   */

  export type AggregateComment = {
    _count: CommentCountAggregateOutputType | null
    _avg: CommentAvgAggregateOutputType | null
    _sum: CommentSumAggregateOutputType | null
    _min: CommentMinAggregateOutputType | null
    _max: CommentMaxAggregateOutputType | null
  }

  export type CommentAvgAggregateOutputType = {
    rating: number | null
  }

  export type CommentSumAggregateOutputType = {
    rating: number | null
  }

  export type CommentMinAggregateOutputType = {
    id: string | null
    content: string | null
    rating: number | null
    createdAt: Date | null
    userId: string | null
    recipeId: string | null
  }

  export type CommentMaxAggregateOutputType = {
    id: string | null
    content: string | null
    rating: number | null
    createdAt: Date | null
    userId: string | null
    recipeId: string | null
  }

  export type CommentCountAggregateOutputType = {
    id: number
    content: number
    rating: number
    createdAt: number
    userId: number
    recipeId: number
    _all: number
  }


  export type CommentAvgAggregateInputType = {
    rating?: true
  }

  export type CommentSumAggregateInputType = {
    rating?: true
  }

  export type CommentMinAggregateInputType = {
    id?: true
    content?: true
    rating?: true
    createdAt?: true
    userId?: true
    recipeId?: true
  }

  export type CommentMaxAggregateInputType = {
    id?: true
    content?: true
    rating?: true
    createdAt?: true
    userId?: true
    recipeId?: true
  }

  export type CommentCountAggregateInputType = {
    id?: true
    content?: true
    rating?: true
    createdAt?: true
    userId?: true
    recipeId?: true
    _all?: true
  }

  export type CommentAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Comment to aggregate.
     */
    where?: CommentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Comments to fetch.
     */
    orderBy?: CommentOrderByWithRelationInput | CommentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CommentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Comments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Comments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Comments
    **/
    _count?: true | CommentCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: CommentAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: CommentSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CommentMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CommentMaxAggregateInputType
  }

  export type GetCommentAggregateType<T extends CommentAggregateArgs> = {
        [P in keyof T & keyof AggregateComment]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateComment[P]>
      : GetScalarType<T[P], AggregateComment[P]>
  }




  export type CommentGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CommentWhereInput
    orderBy?: CommentOrderByWithAggregationInput | CommentOrderByWithAggregationInput[]
    by: CommentScalarFieldEnum[] | CommentScalarFieldEnum
    having?: CommentScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CommentCountAggregateInputType | true
    _avg?: CommentAvgAggregateInputType
    _sum?: CommentSumAggregateInputType
    _min?: CommentMinAggregateInputType
    _max?: CommentMaxAggregateInputType
  }

  export type CommentGroupByOutputType = {
    id: string
    content: string
    rating: number | null
    createdAt: Date
    userId: string
    recipeId: string
    _count: CommentCountAggregateOutputType | null
    _avg: CommentAvgAggregateOutputType | null
    _sum: CommentSumAggregateOutputType | null
    _min: CommentMinAggregateOutputType | null
    _max: CommentMaxAggregateOutputType | null
  }

  type GetCommentGroupByPayload<T extends CommentGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CommentGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CommentGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CommentGroupByOutputType[P]>
            : GetScalarType<T[P], CommentGroupByOutputType[P]>
        }
      >
    >


  export type CommentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    content?: boolean
    rating?: boolean
    createdAt?: boolean
    userId?: boolean
    recipeId?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["comment"]>

  export type CommentSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    content?: boolean
    rating?: boolean
    createdAt?: boolean
    userId?: boolean
    recipeId?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["comment"]>

  export type CommentSelectScalar = {
    id?: boolean
    content?: boolean
    rating?: boolean
    createdAt?: boolean
    userId?: boolean
    recipeId?: boolean
  }

  export type CommentInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
  }
  export type CommentIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
  }

  export type $CommentPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Comment"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      recipe: Prisma.$RecipePayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      content: string
      rating: number | null
      createdAt: Date
      userId: string
      recipeId: string
    }, ExtArgs["result"]["comment"]>
    composites: {}
  }

  type CommentGetPayload<S extends boolean | null | undefined | CommentDefaultArgs> = $Result.GetResult<Prisma.$CommentPayload, S>

  type CommentCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<CommentFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: CommentCountAggregateInputType | true
    }

  export interface CommentDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Comment'], meta: { name: 'Comment' } }
    /**
     * Find zero or one Comment that matches the filter.
     * @param {CommentFindUniqueArgs} args - Arguments to find a Comment
     * @example
     * // Get one Comment
     * const comment = await prisma.comment.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CommentFindUniqueArgs>(args: SelectSubset<T, CommentFindUniqueArgs<ExtArgs>>): Prisma__CommentClient<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one Comment that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {CommentFindUniqueOrThrowArgs} args - Arguments to find a Comment
     * @example
     * // Get one Comment
     * const comment = await prisma.comment.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CommentFindUniqueOrThrowArgs>(args: SelectSubset<T, CommentFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CommentClient<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first Comment that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CommentFindFirstArgs} args - Arguments to find a Comment
     * @example
     * // Get one Comment
     * const comment = await prisma.comment.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CommentFindFirstArgs>(args?: SelectSubset<T, CommentFindFirstArgs<ExtArgs>>): Prisma__CommentClient<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first Comment that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CommentFindFirstOrThrowArgs} args - Arguments to find a Comment
     * @example
     * // Get one Comment
     * const comment = await prisma.comment.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CommentFindFirstOrThrowArgs>(args?: SelectSubset<T, CommentFindFirstOrThrowArgs<ExtArgs>>): Prisma__CommentClient<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Comments that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CommentFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Comments
     * const comments = await prisma.comment.findMany()
     * 
     * // Get first 10 Comments
     * const comments = await prisma.comment.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const commentWithIdOnly = await prisma.comment.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CommentFindManyArgs>(args?: SelectSubset<T, CommentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a Comment.
     * @param {CommentCreateArgs} args - Arguments to create a Comment.
     * @example
     * // Create one Comment
     * const Comment = await prisma.comment.create({
     *   data: {
     *     // ... data to create a Comment
     *   }
     * })
     * 
     */
    create<T extends CommentCreateArgs>(args: SelectSubset<T, CommentCreateArgs<ExtArgs>>): Prisma__CommentClient<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Comments.
     * @param {CommentCreateManyArgs} args - Arguments to create many Comments.
     * @example
     * // Create many Comments
     * const comment = await prisma.comment.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CommentCreateManyArgs>(args?: SelectSubset<T, CommentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Comments and returns the data saved in the database.
     * @param {CommentCreateManyAndReturnArgs} args - Arguments to create many Comments.
     * @example
     * // Create many Comments
     * const comment = await prisma.comment.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Comments and only return the `id`
     * const commentWithIdOnly = await prisma.comment.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends CommentCreateManyAndReturnArgs>(args?: SelectSubset<T, CommentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a Comment.
     * @param {CommentDeleteArgs} args - Arguments to delete one Comment.
     * @example
     * // Delete one Comment
     * const Comment = await prisma.comment.delete({
     *   where: {
     *     // ... filter to delete one Comment
     *   }
     * })
     * 
     */
    delete<T extends CommentDeleteArgs>(args: SelectSubset<T, CommentDeleteArgs<ExtArgs>>): Prisma__CommentClient<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one Comment.
     * @param {CommentUpdateArgs} args - Arguments to update one Comment.
     * @example
     * // Update one Comment
     * const comment = await prisma.comment.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CommentUpdateArgs>(args: SelectSubset<T, CommentUpdateArgs<ExtArgs>>): Prisma__CommentClient<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Comments.
     * @param {CommentDeleteManyArgs} args - Arguments to filter Comments to delete.
     * @example
     * // Delete a few Comments
     * const { count } = await prisma.comment.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CommentDeleteManyArgs>(args?: SelectSubset<T, CommentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Comments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CommentUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Comments
     * const comment = await prisma.comment.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CommentUpdateManyArgs>(args: SelectSubset<T, CommentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Comment.
     * @param {CommentUpsertArgs} args - Arguments to update or create a Comment.
     * @example
     * // Update or create a Comment
     * const comment = await prisma.comment.upsert({
     *   create: {
     *     // ... data to create a Comment
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Comment we want to update
     *   }
     * })
     */
    upsert<T extends CommentUpsertArgs>(args: SelectSubset<T, CommentUpsertArgs<ExtArgs>>): Prisma__CommentClient<$Result.GetResult<Prisma.$CommentPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Comments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CommentCountArgs} args - Arguments to filter Comments to count.
     * @example
     * // Count the number of Comments
     * const count = await prisma.comment.count({
     *   where: {
     *     // ... the filter for the Comments we want to count
     *   }
     * })
    **/
    count<T extends CommentCountArgs>(
      args?: Subset<T, CommentCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CommentCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Comment.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CommentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CommentAggregateArgs>(args: Subset<T, CommentAggregateArgs>): Prisma.PrismaPromise<GetCommentAggregateType<T>>

    /**
     * Group by Comment.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CommentGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CommentGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CommentGroupByArgs['orderBy'] }
        : { orderBy?: CommentGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CommentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCommentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Comment model
   */
  readonly fields: CommentFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Comment.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CommentClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    recipe<T extends RecipeDefaultArgs<ExtArgs> = {}>(args?: Subset<T, RecipeDefaultArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Comment model
   */ 
  interface CommentFieldRefs {
    readonly id: FieldRef<"Comment", 'String'>
    readonly content: FieldRef<"Comment", 'String'>
    readonly rating: FieldRef<"Comment", 'Int'>
    readonly createdAt: FieldRef<"Comment", 'DateTime'>
    readonly userId: FieldRef<"Comment", 'String'>
    readonly recipeId: FieldRef<"Comment", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Comment findUnique
   */
  export type CommentFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * Filter, which Comment to fetch.
     */
    where: CommentWhereUniqueInput
  }

  /**
   * Comment findUniqueOrThrow
   */
  export type CommentFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * Filter, which Comment to fetch.
     */
    where: CommentWhereUniqueInput
  }

  /**
   * Comment findFirst
   */
  export type CommentFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * Filter, which Comment to fetch.
     */
    where?: CommentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Comments to fetch.
     */
    orderBy?: CommentOrderByWithRelationInput | CommentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Comments.
     */
    cursor?: CommentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Comments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Comments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Comments.
     */
    distinct?: CommentScalarFieldEnum | CommentScalarFieldEnum[]
  }

  /**
   * Comment findFirstOrThrow
   */
  export type CommentFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * Filter, which Comment to fetch.
     */
    where?: CommentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Comments to fetch.
     */
    orderBy?: CommentOrderByWithRelationInput | CommentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Comments.
     */
    cursor?: CommentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Comments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Comments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Comments.
     */
    distinct?: CommentScalarFieldEnum | CommentScalarFieldEnum[]
  }

  /**
   * Comment findMany
   */
  export type CommentFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * Filter, which Comments to fetch.
     */
    where?: CommentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Comments to fetch.
     */
    orderBy?: CommentOrderByWithRelationInput | CommentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Comments.
     */
    cursor?: CommentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Comments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Comments.
     */
    skip?: number
    distinct?: CommentScalarFieldEnum | CommentScalarFieldEnum[]
  }

  /**
   * Comment create
   */
  export type CommentCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * The data needed to create a Comment.
     */
    data: XOR<CommentCreateInput, CommentUncheckedCreateInput>
  }

  /**
   * Comment createMany
   */
  export type CommentCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Comments.
     */
    data: CommentCreateManyInput | CommentCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Comment createManyAndReturn
   */
  export type CommentCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Comments.
     */
    data: CommentCreateManyInput | CommentCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Comment update
   */
  export type CommentUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * The data needed to update a Comment.
     */
    data: XOR<CommentUpdateInput, CommentUncheckedUpdateInput>
    /**
     * Choose, which Comment to update.
     */
    where: CommentWhereUniqueInput
  }

  /**
   * Comment updateMany
   */
  export type CommentUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Comments.
     */
    data: XOR<CommentUpdateManyMutationInput, CommentUncheckedUpdateManyInput>
    /**
     * Filter which Comments to update
     */
    where?: CommentWhereInput
  }

  /**
   * Comment upsert
   */
  export type CommentUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * The filter to search for the Comment to update in case it exists.
     */
    where: CommentWhereUniqueInput
    /**
     * In case the Comment found by the `where` argument doesn't exist, create a new Comment with this data.
     */
    create: XOR<CommentCreateInput, CommentUncheckedCreateInput>
    /**
     * In case the Comment was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CommentUpdateInput, CommentUncheckedUpdateInput>
  }

  /**
   * Comment delete
   */
  export type CommentDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
    /**
     * Filter which Comment to delete.
     */
    where: CommentWhereUniqueInput
  }

  /**
   * Comment deleteMany
   */
  export type CommentDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Comments to delete
     */
    where?: CommentWhereInput
  }

  /**
   * Comment without action
   */
  export type CommentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Comment
     */
    select?: CommentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CommentInclude<ExtArgs> | null
  }


  /**
   * Model UserFavorite
   */

  export type AggregateUserFavorite = {
    _count: UserFavoriteCountAggregateOutputType | null
    _min: UserFavoriteMinAggregateOutputType | null
    _max: UserFavoriteMaxAggregateOutputType | null
  }

  export type UserFavoriteMinAggregateOutputType = {
    userId: string | null
    recipeId: string | null
    createdAt: Date | null
  }

  export type UserFavoriteMaxAggregateOutputType = {
    userId: string | null
    recipeId: string | null
    createdAt: Date | null
  }

  export type UserFavoriteCountAggregateOutputType = {
    userId: number
    recipeId: number
    createdAt: number
    _all: number
  }


  export type UserFavoriteMinAggregateInputType = {
    userId?: true
    recipeId?: true
    createdAt?: true
  }

  export type UserFavoriteMaxAggregateInputType = {
    userId?: true
    recipeId?: true
    createdAt?: true
  }

  export type UserFavoriteCountAggregateInputType = {
    userId?: true
    recipeId?: true
    createdAt?: true
    _all?: true
  }

  export type UserFavoriteAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserFavorite to aggregate.
     */
    where?: UserFavoriteWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserFavorites to fetch.
     */
    orderBy?: UserFavoriteOrderByWithRelationInput | UserFavoriteOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserFavoriteWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserFavorites from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserFavorites.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned UserFavorites
    **/
    _count?: true | UserFavoriteCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserFavoriteMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserFavoriteMaxAggregateInputType
  }

  export type GetUserFavoriteAggregateType<T extends UserFavoriteAggregateArgs> = {
        [P in keyof T & keyof AggregateUserFavorite]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUserFavorite[P]>
      : GetScalarType<T[P], AggregateUserFavorite[P]>
  }




  export type UserFavoriteGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserFavoriteWhereInput
    orderBy?: UserFavoriteOrderByWithAggregationInput | UserFavoriteOrderByWithAggregationInput[]
    by: UserFavoriteScalarFieldEnum[] | UserFavoriteScalarFieldEnum
    having?: UserFavoriteScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserFavoriteCountAggregateInputType | true
    _min?: UserFavoriteMinAggregateInputType
    _max?: UserFavoriteMaxAggregateInputType
  }

  export type UserFavoriteGroupByOutputType = {
    userId: string
    recipeId: string
    createdAt: Date
    _count: UserFavoriteCountAggregateOutputType | null
    _min: UserFavoriteMinAggregateOutputType | null
    _max: UserFavoriteMaxAggregateOutputType | null
  }

  type GetUserFavoriteGroupByPayload<T extends UserFavoriteGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserFavoriteGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserFavoriteGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserFavoriteGroupByOutputType[P]>
            : GetScalarType<T[P], UserFavoriteGroupByOutputType[P]>
        }
      >
    >


  export type UserFavoriteSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    userId?: boolean
    recipeId?: boolean
    createdAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["userFavorite"]>

  export type UserFavoriteSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    userId?: boolean
    recipeId?: boolean
    createdAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["userFavorite"]>

  export type UserFavoriteSelectScalar = {
    userId?: boolean
    recipeId?: boolean
    createdAt?: boolean
  }

  export type UserFavoriteInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
  }
  export type UserFavoriteIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    recipe?: boolean | RecipeDefaultArgs<ExtArgs>
  }

  export type $UserFavoritePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "UserFavorite"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      recipe: Prisma.$RecipePayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      userId: string
      recipeId: string
      createdAt: Date
    }, ExtArgs["result"]["userFavorite"]>
    composites: {}
  }

  type UserFavoriteGetPayload<S extends boolean | null | undefined | UserFavoriteDefaultArgs> = $Result.GetResult<Prisma.$UserFavoritePayload, S>

  type UserFavoriteCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<UserFavoriteFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: UserFavoriteCountAggregateInputType | true
    }

  export interface UserFavoriteDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['UserFavorite'], meta: { name: 'UserFavorite' } }
    /**
     * Find zero or one UserFavorite that matches the filter.
     * @param {UserFavoriteFindUniqueArgs} args - Arguments to find a UserFavorite
     * @example
     * // Get one UserFavorite
     * const userFavorite = await prisma.userFavorite.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFavoriteFindUniqueArgs>(args: SelectSubset<T, UserFavoriteFindUniqueArgs<ExtArgs>>): Prisma__UserFavoriteClient<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one UserFavorite that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {UserFavoriteFindUniqueOrThrowArgs} args - Arguments to find a UserFavorite
     * @example
     * // Get one UserFavorite
     * const userFavorite = await prisma.userFavorite.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFavoriteFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFavoriteFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserFavoriteClient<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first UserFavorite that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFavoriteFindFirstArgs} args - Arguments to find a UserFavorite
     * @example
     * // Get one UserFavorite
     * const userFavorite = await prisma.userFavorite.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFavoriteFindFirstArgs>(args?: SelectSubset<T, UserFavoriteFindFirstArgs<ExtArgs>>): Prisma__UserFavoriteClient<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first UserFavorite that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFavoriteFindFirstOrThrowArgs} args - Arguments to find a UserFavorite
     * @example
     * // Get one UserFavorite
     * const userFavorite = await prisma.userFavorite.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFavoriteFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFavoriteFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserFavoriteClient<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more UserFavorites that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFavoriteFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all UserFavorites
     * const userFavorites = await prisma.userFavorite.findMany()
     * 
     * // Get first 10 UserFavorites
     * const userFavorites = await prisma.userFavorite.findMany({ take: 10 })
     * 
     * // Only select the `userId`
     * const userFavoriteWithUserIdOnly = await prisma.userFavorite.findMany({ select: { userId: true } })
     * 
     */
    findMany<T extends UserFavoriteFindManyArgs>(args?: SelectSubset<T, UserFavoriteFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "findMany">>

    /**
     * Create a UserFavorite.
     * @param {UserFavoriteCreateArgs} args - Arguments to create a UserFavorite.
     * @example
     * // Create one UserFavorite
     * const UserFavorite = await prisma.userFavorite.create({
     *   data: {
     *     // ... data to create a UserFavorite
     *   }
     * })
     * 
     */
    create<T extends UserFavoriteCreateArgs>(args: SelectSubset<T, UserFavoriteCreateArgs<ExtArgs>>): Prisma__UserFavoriteClient<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many UserFavorites.
     * @param {UserFavoriteCreateManyArgs} args - Arguments to create many UserFavorites.
     * @example
     * // Create many UserFavorites
     * const userFavorite = await prisma.userFavorite.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserFavoriteCreateManyArgs>(args?: SelectSubset<T, UserFavoriteCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many UserFavorites and returns the data saved in the database.
     * @param {UserFavoriteCreateManyAndReturnArgs} args - Arguments to create many UserFavorites.
     * @example
     * // Create many UserFavorites
     * const userFavorite = await prisma.userFavorite.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many UserFavorites and only return the `userId`
     * const userFavoriteWithUserIdOnly = await prisma.userFavorite.createManyAndReturn({ 
     *   select: { userId: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserFavoriteCreateManyAndReturnArgs>(args?: SelectSubset<T, UserFavoriteCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a UserFavorite.
     * @param {UserFavoriteDeleteArgs} args - Arguments to delete one UserFavorite.
     * @example
     * // Delete one UserFavorite
     * const UserFavorite = await prisma.userFavorite.delete({
     *   where: {
     *     // ... filter to delete one UserFavorite
     *   }
     * })
     * 
     */
    delete<T extends UserFavoriteDeleteArgs>(args: SelectSubset<T, UserFavoriteDeleteArgs<ExtArgs>>): Prisma__UserFavoriteClient<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one UserFavorite.
     * @param {UserFavoriteUpdateArgs} args - Arguments to update one UserFavorite.
     * @example
     * // Update one UserFavorite
     * const userFavorite = await prisma.userFavorite.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserFavoriteUpdateArgs>(args: SelectSubset<T, UserFavoriteUpdateArgs<ExtArgs>>): Prisma__UserFavoriteClient<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more UserFavorites.
     * @param {UserFavoriteDeleteManyArgs} args - Arguments to filter UserFavorites to delete.
     * @example
     * // Delete a few UserFavorites
     * const { count } = await prisma.userFavorite.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserFavoriteDeleteManyArgs>(args?: SelectSubset<T, UserFavoriteDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more UserFavorites.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFavoriteUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many UserFavorites
     * const userFavorite = await prisma.userFavorite.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserFavoriteUpdateManyArgs>(args: SelectSubset<T, UserFavoriteUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one UserFavorite.
     * @param {UserFavoriteUpsertArgs} args - Arguments to update or create a UserFavorite.
     * @example
     * // Update or create a UserFavorite
     * const userFavorite = await prisma.userFavorite.upsert({
     *   create: {
     *     // ... data to create a UserFavorite
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the UserFavorite we want to update
     *   }
     * })
     */
    upsert<T extends UserFavoriteUpsertArgs>(args: SelectSubset<T, UserFavoriteUpsertArgs<ExtArgs>>): Prisma__UserFavoriteClient<$Result.GetResult<Prisma.$UserFavoritePayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of UserFavorites.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFavoriteCountArgs} args - Arguments to filter UserFavorites to count.
     * @example
     * // Count the number of UserFavorites
     * const count = await prisma.userFavorite.count({
     *   where: {
     *     // ... the filter for the UserFavorites we want to count
     *   }
     * })
    **/
    count<T extends UserFavoriteCountArgs>(
      args?: Subset<T, UserFavoriteCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserFavoriteCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a UserFavorite.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFavoriteAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserFavoriteAggregateArgs>(args: Subset<T, UserFavoriteAggregateArgs>): Prisma.PrismaPromise<GetUserFavoriteAggregateType<T>>

    /**
     * Group by UserFavorite.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFavoriteGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserFavoriteGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserFavoriteGroupByArgs['orderBy'] }
        : { orderBy?: UserFavoriteGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserFavoriteGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserFavoriteGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the UserFavorite model
   */
  readonly fields: UserFavoriteFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for UserFavorite.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserFavoriteClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    recipe<T extends RecipeDefaultArgs<ExtArgs> = {}>(args?: Subset<T, RecipeDefaultArgs<ExtArgs>>): Prisma__RecipeClient<$Result.GetResult<Prisma.$RecipePayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the UserFavorite model
   */ 
  interface UserFavoriteFieldRefs {
    readonly userId: FieldRef<"UserFavorite", 'String'>
    readonly recipeId: FieldRef<"UserFavorite", 'String'>
    readonly createdAt: FieldRef<"UserFavorite", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * UserFavorite findUnique
   */
  export type UserFavoriteFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * Filter, which UserFavorite to fetch.
     */
    where: UserFavoriteWhereUniqueInput
  }

  /**
   * UserFavorite findUniqueOrThrow
   */
  export type UserFavoriteFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * Filter, which UserFavorite to fetch.
     */
    where: UserFavoriteWhereUniqueInput
  }

  /**
   * UserFavorite findFirst
   */
  export type UserFavoriteFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * Filter, which UserFavorite to fetch.
     */
    where?: UserFavoriteWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserFavorites to fetch.
     */
    orderBy?: UserFavoriteOrderByWithRelationInput | UserFavoriteOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserFavorites.
     */
    cursor?: UserFavoriteWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserFavorites from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserFavorites.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserFavorites.
     */
    distinct?: UserFavoriteScalarFieldEnum | UserFavoriteScalarFieldEnum[]
  }

  /**
   * UserFavorite findFirstOrThrow
   */
  export type UserFavoriteFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * Filter, which UserFavorite to fetch.
     */
    where?: UserFavoriteWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserFavorites to fetch.
     */
    orderBy?: UserFavoriteOrderByWithRelationInput | UserFavoriteOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserFavorites.
     */
    cursor?: UserFavoriteWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserFavorites from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserFavorites.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserFavorites.
     */
    distinct?: UserFavoriteScalarFieldEnum | UserFavoriteScalarFieldEnum[]
  }

  /**
   * UserFavorite findMany
   */
  export type UserFavoriteFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * Filter, which UserFavorites to fetch.
     */
    where?: UserFavoriteWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserFavorites to fetch.
     */
    orderBy?: UserFavoriteOrderByWithRelationInput | UserFavoriteOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing UserFavorites.
     */
    cursor?: UserFavoriteWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserFavorites from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserFavorites.
     */
    skip?: number
    distinct?: UserFavoriteScalarFieldEnum | UserFavoriteScalarFieldEnum[]
  }

  /**
   * UserFavorite create
   */
  export type UserFavoriteCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * The data needed to create a UserFavorite.
     */
    data: XOR<UserFavoriteCreateInput, UserFavoriteUncheckedCreateInput>
  }

  /**
   * UserFavorite createMany
   */
  export type UserFavoriteCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many UserFavorites.
     */
    data: UserFavoriteCreateManyInput | UserFavoriteCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * UserFavorite createManyAndReturn
   */
  export type UserFavoriteCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many UserFavorites.
     */
    data: UserFavoriteCreateManyInput | UserFavoriteCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * UserFavorite update
   */
  export type UserFavoriteUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * The data needed to update a UserFavorite.
     */
    data: XOR<UserFavoriteUpdateInput, UserFavoriteUncheckedUpdateInput>
    /**
     * Choose, which UserFavorite to update.
     */
    where: UserFavoriteWhereUniqueInput
  }

  /**
   * UserFavorite updateMany
   */
  export type UserFavoriteUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update UserFavorites.
     */
    data: XOR<UserFavoriteUpdateManyMutationInput, UserFavoriteUncheckedUpdateManyInput>
    /**
     * Filter which UserFavorites to update
     */
    where?: UserFavoriteWhereInput
  }

  /**
   * UserFavorite upsert
   */
  export type UserFavoriteUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * The filter to search for the UserFavorite to update in case it exists.
     */
    where: UserFavoriteWhereUniqueInput
    /**
     * In case the UserFavorite found by the `where` argument doesn't exist, create a new UserFavorite with this data.
     */
    create: XOR<UserFavoriteCreateInput, UserFavoriteUncheckedCreateInput>
    /**
     * In case the UserFavorite was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserFavoriteUpdateInput, UserFavoriteUncheckedUpdateInput>
  }

  /**
   * UserFavorite delete
   */
  export type UserFavoriteDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
    /**
     * Filter which UserFavorite to delete.
     */
    where: UserFavoriteWhereUniqueInput
  }

  /**
   * UserFavorite deleteMany
   */
  export type UserFavoriteDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserFavorites to delete
     */
    where?: UserFavoriteWhereInput
  }

  /**
   * UserFavorite without action
   */
  export type UserFavoriteDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserFavorite
     */
    select?: UserFavoriteSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserFavoriteInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    username: 'username',
    email: 'email',
    password: 'password',
    role: 'role',
    avatar: 'avatar',
    isActive: 'isActive',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const InheritorApplicationScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    realName: 'realName',
    phone: 'phone',
    region: 'region',
    specialties: 'specialties',
    experience: 'experience',
    certifications: 'certifications',
    introduction: 'introduction',
    status: 'status',
    appliedAt: 'appliedAt',
    reviewedAt: 'reviewedAt',
    reviewedBy: 'reviewedBy',
    reviewComment: 'reviewComment'
  };

  export type InheritorApplicationScalarFieldEnum = (typeof InheritorApplicationScalarFieldEnum)[keyof typeof InheritorApplicationScalarFieldEnum]


  export const RecipeScalarFieldEnum: {
    id: 'id',
    name: 'name',
    dialectName: 'dialectName',
    dialectAudio: 'dialectAudio',
    region: 'region',
    ingredients: 'ingredients',
    steps: 'steps',
    image: 'image',
    description: 'description',
    difficulty: 'difficulty',
    cookTime: 'cookTime',
    servings: 'servings',
    isPublished: 'isPublished',
    viewCount: 'viewCount',
    culturalHistory: 'culturalHistory',
    regionalFeature: 'regionalFeature',
    culturalMeaning: 'culturalMeaning',
    inheritanceStory: 'inheritanceStory',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    authorId: 'authorId'
  };

  export type RecipeScalarFieldEnum = (typeof RecipeScalarFieldEnum)[keyof typeof RecipeScalarFieldEnum]


  export const CategoryScalarFieldEnum: {
    id: 'id',
    name: 'name',
    description: 'description',
    icon: 'icon',
    createdAt: 'createdAt'
  };

  export type CategoryScalarFieldEnum = (typeof CategoryScalarFieldEnum)[keyof typeof CategoryScalarFieldEnum]


  export const RecipeCategoryScalarFieldEnum: {
    recipeId: 'recipeId',
    categoryId: 'categoryId'
  };

  export type RecipeCategoryScalarFieldEnum = (typeof RecipeCategoryScalarFieldEnum)[keyof typeof RecipeCategoryScalarFieldEnum]


  export const CommentScalarFieldEnum: {
    id: 'id',
    content: 'content',
    rating: 'rating',
    createdAt: 'createdAt',
    userId: 'userId',
    recipeId: 'recipeId'
  };

  export type CommentScalarFieldEnum = (typeof CommentScalarFieldEnum)[keyof typeof CommentScalarFieldEnum]


  export const UserFavoriteScalarFieldEnum: {
    userId: 'userId',
    recipeId: 'recipeId',
    createdAt: 'createdAt'
  };

  export type UserFavoriteScalarFieldEnum = (typeof UserFavoriteScalarFieldEnum)[keyof typeof UserFavoriteScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references 
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'UserRole'
   */
  export type EnumUserRoleFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserRole'>
    


  /**
   * Reference to a field of type 'UserRole[]'
   */
  export type ListEnumUserRoleFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserRole[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'ApplicationStatus'
   */
  export type EnumApplicationStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ApplicationStatus'>
    


  /**
   * Reference to a field of type 'ApplicationStatus[]'
   */
  export type ListEnumApplicationStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ApplicationStatus[]'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: StringFilter<"User"> | string
    username?: StringFilter<"User"> | string
    email?: StringNullableFilter<"User"> | string | null
    password?: StringFilter<"User"> | string
    role?: EnumUserRoleFilter<"User"> | $Enums.UserRole
    avatar?: StringNullableFilter<"User"> | string | null
    isActive?: BoolFilter<"User"> | boolean
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    recipes?: RecipeListRelationFilter
    comments?: CommentListRelationFilter
    favorites?: UserFavoriteListRelationFilter
    inheritorApplication?: XOR<InheritorApplicationNullableRelationFilter, InheritorApplicationWhereInput> | null
    reviewedApplications?: InheritorApplicationListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrderInput | SortOrder
    password?: SortOrder
    role?: SortOrder
    avatar?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    recipes?: RecipeOrderByRelationAggregateInput
    comments?: CommentOrderByRelationAggregateInput
    favorites?: UserFavoriteOrderByRelationAggregateInput
    inheritorApplication?: InheritorApplicationOrderByWithRelationInput
    reviewedApplications?: InheritorApplicationOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    username?: string
    email?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    password?: StringFilter<"User"> | string
    role?: EnumUserRoleFilter<"User"> | $Enums.UserRole
    avatar?: StringNullableFilter<"User"> | string | null
    isActive?: BoolFilter<"User"> | boolean
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    recipes?: RecipeListRelationFilter
    comments?: CommentListRelationFilter
    favorites?: UserFavoriteListRelationFilter
    inheritorApplication?: XOR<InheritorApplicationNullableRelationFilter, InheritorApplicationWhereInput> | null
    reviewedApplications?: InheritorApplicationListRelationFilter
  }, "id" | "username" | "email">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrderInput | SortOrder
    password?: SortOrder
    role?: SortOrder
    avatar?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"User"> | string
    username?: StringWithAggregatesFilter<"User"> | string
    email?: StringNullableWithAggregatesFilter<"User"> | string | null
    password?: StringWithAggregatesFilter<"User"> | string
    role?: EnumUserRoleWithAggregatesFilter<"User"> | $Enums.UserRole
    avatar?: StringNullableWithAggregatesFilter<"User"> | string | null
    isActive?: BoolWithAggregatesFilter<"User"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type InheritorApplicationWhereInput = {
    AND?: InheritorApplicationWhereInput | InheritorApplicationWhereInput[]
    OR?: InheritorApplicationWhereInput[]
    NOT?: InheritorApplicationWhereInput | InheritorApplicationWhereInput[]
    id?: StringFilter<"InheritorApplication"> | string
    userId?: StringFilter<"InheritorApplication"> | string
    realName?: StringFilter<"InheritorApplication"> | string
    phone?: StringFilter<"InheritorApplication"> | string
    region?: StringFilter<"InheritorApplication"> | string
    specialties?: StringFilter<"InheritorApplication"> | string
    experience?: StringFilter<"InheritorApplication"> | string
    certifications?: StringNullableFilter<"InheritorApplication"> | string | null
    introduction?: StringFilter<"InheritorApplication"> | string
    status?: EnumApplicationStatusFilter<"InheritorApplication"> | $Enums.ApplicationStatus
    appliedAt?: DateTimeFilter<"InheritorApplication"> | Date | string
    reviewedAt?: DateTimeNullableFilter<"InheritorApplication"> | Date | string | null
    reviewedBy?: StringNullableFilter<"InheritorApplication"> | string | null
    reviewComment?: StringNullableFilter<"InheritorApplication"> | string | null
    user?: XOR<UserRelationFilter, UserWhereInput>
    reviewer?: XOR<UserNullableRelationFilter, UserWhereInput> | null
  }

  export type InheritorApplicationOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    realName?: SortOrder
    phone?: SortOrder
    region?: SortOrder
    specialties?: SortOrder
    experience?: SortOrder
    certifications?: SortOrderInput | SortOrder
    introduction?: SortOrder
    status?: SortOrder
    appliedAt?: SortOrder
    reviewedAt?: SortOrderInput | SortOrder
    reviewedBy?: SortOrderInput | SortOrder
    reviewComment?: SortOrderInput | SortOrder
    user?: UserOrderByWithRelationInput
    reviewer?: UserOrderByWithRelationInput
  }

  export type InheritorApplicationWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    userId?: string
    AND?: InheritorApplicationWhereInput | InheritorApplicationWhereInput[]
    OR?: InheritorApplicationWhereInput[]
    NOT?: InheritorApplicationWhereInput | InheritorApplicationWhereInput[]
    realName?: StringFilter<"InheritorApplication"> | string
    phone?: StringFilter<"InheritorApplication"> | string
    region?: StringFilter<"InheritorApplication"> | string
    specialties?: StringFilter<"InheritorApplication"> | string
    experience?: StringFilter<"InheritorApplication"> | string
    certifications?: StringNullableFilter<"InheritorApplication"> | string | null
    introduction?: StringFilter<"InheritorApplication"> | string
    status?: EnumApplicationStatusFilter<"InheritorApplication"> | $Enums.ApplicationStatus
    appliedAt?: DateTimeFilter<"InheritorApplication"> | Date | string
    reviewedAt?: DateTimeNullableFilter<"InheritorApplication"> | Date | string | null
    reviewedBy?: StringNullableFilter<"InheritorApplication"> | string | null
    reviewComment?: StringNullableFilter<"InheritorApplication"> | string | null
    user?: XOR<UserRelationFilter, UserWhereInput>
    reviewer?: XOR<UserNullableRelationFilter, UserWhereInput> | null
  }, "id" | "userId">

  export type InheritorApplicationOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    realName?: SortOrder
    phone?: SortOrder
    region?: SortOrder
    specialties?: SortOrder
    experience?: SortOrder
    certifications?: SortOrderInput | SortOrder
    introduction?: SortOrder
    status?: SortOrder
    appliedAt?: SortOrder
    reviewedAt?: SortOrderInput | SortOrder
    reviewedBy?: SortOrderInput | SortOrder
    reviewComment?: SortOrderInput | SortOrder
    _count?: InheritorApplicationCountOrderByAggregateInput
    _max?: InheritorApplicationMaxOrderByAggregateInput
    _min?: InheritorApplicationMinOrderByAggregateInput
  }

  export type InheritorApplicationScalarWhereWithAggregatesInput = {
    AND?: InheritorApplicationScalarWhereWithAggregatesInput | InheritorApplicationScalarWhereWithAggregatesInput[]
    OR?: InheritorApplicationScalarWhereWithAggregatesInput[]
    NOT?: InheritorApplicationScalarWhereWithAggregatesInput | InheritorApplicationScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"InheritorApplication"> | string
    userId?: StringWithAggregatesFilter<"InheritorApplication"> | string
    realName?: StringWithAggregatesFilter<"InheritorApplication"> | string
    phone?: StringWithAggregatesFilter<"InheritorApplication"> | string
    region?: StringWithAggregatesFilter<"InheritorApplication"> | string
    specialties?: StringWithAggregatesFilter<"InheritorApplication"> | string
    experience?: StringWithAggregatesFilter<"InheritorApplication"> | string
    certifications?: StringNullableWithAggregatesFilter<"InheritorApplication"> | string | null
    introduction?: StringWithAggregatesFilter<"InheritorApplication"> | string
    status?: EnumApplicationStatusWithAggregatesFilter<"InheritorApplication"> | $Enums.ApplicationStatus
    appliedAt?: DateTimeWithAggregatesFilter<"InheritorApplication"> | Date | string
    reviewedAt?: DateTimeNullableWithAggregatesFilter<"InheritorApplication"> | Date | string | null
    reviewedBy?: StringNullableWithAggregatesFilter<"InheritorApplication"> | string | null
    reviewComment?: StringNullableWithAggregatesFilter<"InheritorApplication"> | string | null
  }

  export type RecipeWhereInput = {
    AND?: RecipeWhereInput | RecipeWhereInput[]
    OR?: RecipeWhereInput[]
    NOT?: RecipeWhereInput | RecipeWhereInput[]
    id?: StringFilter<"Recipe"> | string
    name?: StringFilter<"Recipe"> | string
    dialectName?: StringFilter<"Recipe"> | string
    dialectAudio?: StringNullableFilter<"Recipe"> | string | null
    region?: StringFilter<"Recipe"> | string
    ingredients?: StringFilter<"Recipe"> | string
    steps?: StringFilter<"Recipe"> | string
    image?: StringNullableFilter<"Recipe"> | string | null
    description?: StringNullableFilter<"Recipe"> | string | null
    difficulty?: IntFilter<"Recipe"> | number
    cookTime?: IntNullableFilter<"Recipe"> | number | null
    servings?: IntNullableFilter<"Recipe"> | number | null
    isPublished?: BoolFilter<"Recipe"> | boolean
    viewCount?: IntFilter<"Recipe"> | number
    culturalHistory?: StringNullableFilter<"Recipe"> | string | null
    regionalFeature?: StringNullableFilter<"Recipe"> | string | null
    culturalMeaning?: StringNullableFilter<"Recipe"> | string | null
    inheritanceStory?: StringNullableFilter<"Recipe"> | string | null
    createdAt?: DateTimeFilter<"Recipe"> | Date | string
    updatedAt?: DateTimeFilter<"Recipe"> | Date | string
    authorId?: StringFilter<"Recipe"> | string
    author?: XOR<UserRelationFilter, UserWhereInput>
    categories?: RecipeCategoryListRelationFilter
    comments?: CommentListRelationFilter
    favorites?: UserFavoriteListRelationFilter
  }

  export type RecipeOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    dialectName?: SortOrder
    dialectAudio?: SortOrderInput | SortOrder
    region?: SortOrder
    ingredients?: SortOrder
    steps?: SortOrder
    image?: SortOrderInput | SortOrder
    description?: SortOrderInput | SortOrder
    difficulty?: SortOrder
    cookTime?: SortOrderInput | SortOrder
    servings?: SortOrderInput | SortOrder
    isPublished?: SortOrder
    viewCount?: SortOrder
    culturalHistory?: SortOrderInput | SortOrder
    regionalFeature?: SortOrderInput | SortOrder
    culturalMeaning?: SortOrderInput | SortOrder
    inheritanceStory?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    authorId?: SortOrder
    author?: UserOrderByWithRelationInput
    categories?: RecipeCategoryOrderByRelationAggregateInput
    comments?: CommentOrderByRelationAggregateInput
    favorites?: UserFavoriteOrderByRelationAggregateInput
  }

  export type RecipeWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: RecipeWhereInput | RecipeWhereInput[]
    OR?: RecipeWhereInput[]
    NOT?: RecipeWhereInput | RecipeWhereInput[]
    name?: StringFilter<"Recipe"> | string
    dialectName?: StringFilter<"Recipe"> | string
    dialectAudio?: StringNullableFilter<"Recipe"> | string | null
    region?: StringFilter<"Recipe"> | string
    ingredients?: StringFilter<"Recipe"> | string
    steps?: StringFilter<"Recipe"> | string
    image?: StringNullableFilter<"Recipe"> | string | null
    description?: StringNullableFilter<"Recipe"> | string | null
    difficulty?: IntFilter<"Recipe"> | number
    cookTime?: IntNullableFilter<"Recipe"> | number | null
    servings?: IntNullableFilter<"Recipe"> | number | null
    isPublished?: BoolFilter<"Recipe"> | boolean
    viewCount?: IntFilter<"Recipe"> | number
    culturalHistory?: StringNullableFilter<"Recipe"> | string | null
    regionalFeature?: StringNullableFilter<"Recipe"> | string | null
    culturalMeaning?: StringNullableFilter<"Recipe"> | string | null
    inheritanceStory?: StringNullableFilter<"Recipe"> | string | null
    createdAt?: DateTimeFilter<"Recipe"> | Date | string
    updatedAt?: DateTimeFilter<"Recipe"> | Date | string
    authorId?: StringFilter<"Recipe"> | string
    author?: XOR<UserRelationFilter, UserWhereInput>
    categories?: RecipeCategoryListRelationFilter
    comments?: CommentListRelationFilter
    favorites?: UserFavoriteListRelationFilter
  }, "id">

  export type RecipeOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    dialectName?: SortOrder
    dialectAudio?: SortOrderInput | SortOrder
    region?: SortOrder
    ingredients?: SortOrder
    steps?: SortOrder
    image?: SortOrderInput | SortOrder
    description?: SortOrderInput | SortOrder
    difficulty?: SortOrder
    cookTime?: SortOrderInput | SortOrder
    servings?: SortOrderInput | SortOrder
    isPublished?: SortOrder
    viewCount?: SortOrder
    culturalHistory?: SortOrderInput | SortOrder
    regionalFeature?: SortOrderInput | SortOrder
    culturalMeaning?: SortOrderInput | SortOrder
    inheritanceStory?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    authorId?: SortOrder
    _count?: RecipeCountOrderByAggregateInput
    _avg?: RecipeAvgOrderByAggregateInput
    _max?: RecipeMaxOrderByAggregateInput
    _min?: RecipeMinOrderByAggregateInput
    _sum?: RecipeSumOrderByAggregateInput
  }

  export type RecipeScalarWhereWithAggregatesInput = {
    AND?: RecipeScalarWhereWithAggregatesInput | RecipeScalarWhereWithAggregatesInput[]
    OR?: RecipeScalarWhereWithAggregatesInput[]
    NOT?: RecipeScalarWhereWithAggregatesInput | RecipeScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Recipe"> | string
    name?: StringWithAggregatesFilter<"Recipe"> | string
    dialectName?: StringWithAggregatesFilter<"Recipe"> | string
    dialectAudio?: StringNullableWithAggregatesFilter<"Recipe"> | string | null
    region?: StringWithAggregatesFilter<"Recipe"> | string
    ingredients?: StringWithAggregatesFilter<"Recipe"> | string
    steps?: StringWithAggregatesFilter<"Recipe"> | string
    image?: StringNullableWithAggregatesFilter<"Recipe"> | string | null
    description?: StringNullableWithAggregatesFilter<"Recipe"> | string | null
    difficulty?: IntWithAggregatesFilter<"Recipe"> | number
    cookTime?: IntNullableWithAggregatesFilter<"Recipe"> | number | null
    servings?: IntNullableWithAggregatesFilter<"Recipe"> | number | null
    isPublished?: BoolWithAggregatesFilter<"Recipe"> | boolean
    viewCount?: IntWithAggregatesFilter<"Recipe"> | number
    culturalHistory?: StringNullableWithAggregatesFilter<"Recipe"> | string | null
    regionalFeature?: StringNullableWithAggregatesFilter<"Recipe"> | string | null
    culturalMeaning?: StringNullableWithAggregatesFilter<"Recipe"> | string | null
    inheritanceStory?: StringNullableWithAggregatesFilter<"Recipe"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"Recipe"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Recipe"> | Date | string
    authorId?: StringWithAggregatesFilter<"Recipe"> | string
  }

  export type CategoryWhereInput = {
    AND?: CategoryWhereInput | CategoryWhereInput[]
    OR?: CategoryWhereInput[]
    NOT?: CategoryWhereInput | CategoryWhereInput[]
    id?: StringFilter<"Category"> | string
    name?: StringFilter<"Category"> | string
    description?: StringNullableFilter<"Category"> | string | null
    icon?: StringNullableFilter<"Category"> | string | null
    createdAt?: DateTimeFilter<"Category"> | Date | string
    recipes?: RecipeCategoryListRelationFilter
  }

  export type CategoryOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrderInput | SortOrder
    icon?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    recipes?: RecipeCategoryOrderByRelationAggregateInput
  }

  export type CategoryWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    name?: string
    AND?: CategoryWhereInput | CategoryWhereInput[]
    OR?: CategoryWhereInput[]
    NOT?: CategoryWhereInput | CategoryWhereInput[]
    description?: StringNullableFilter<"Category"> | string | null
    icon?: StringNullableFilter<"Category"> | string | null
    createdAt?: DateTimeFilter<"Category"> | Date | string
    recipes?: RecipeCategoryListRelationFilter
  }, "id" | "name">

  export type CategoryOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrderInput | SortOrder
    icon?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    _count?: CategoryCountOrderByAggregateInput
    _max?: CategoryMaxOrderByAggregateInput
    _min?: CategoryMinOrderByAggregateInput
  }

  export type CategoryScalarWhereWithAggregatesInput = {
    AND?: CategoryScalarWhereWithAggregatesInput | CategoryScalarWhereWithAggregatesInput[]
    OR?: CategoryScalarWhereWithAggregatesInput[]
    NOT?: CategoryScalarWhereWithAggregatesInput | CategoryScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Category"> | string
    name?: StringWithAggregatesFilter<"Category"> | string
    description?: StringNullableWithAggregatesFilter<"Category"> | string | null
    icon?: StringNullableWithAggregatesFilter<"Category"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"Category"> | Date | string
  }

  export type RecipeCategoryWhereInput = {
    AND?: RecipeCategoryWhereInput | RecipeCategoryWhereInput[]
    OR?: RecipeCategoryWhereInput[]
    NOT?: RecipeCategoryWhereInput | RecipeCategoryWhereInput[]
    recipeId?: StringFilter<"RecipeCategory"> | string
    categoryId?: StringFilter<"RecipeCategory"> | string
    recipe?: XOR<RecipeRelationFilter, RecipeWhereInput>
    category?: XOR<CategoryRelationFilter, CategoryWhereInput>
  }

  export type RecipeCategoryOrderByWithRelationInput = {
    recipeId?: SortOrder
    categoryId?: SortOrder
    recipe?: RecipeOrderByWithRelationInput
    category?: CategoryOrderByWithRelationInput
  }

  export type RecipeCategoryWhereUniqueInput = Prisma.AtLeast<{
    recipeId_categoryId?: RecipeCategoryRecipeIdCategoryIdCompoundUniqueInput
    AND?: RecipeCategoryWhereInput | RecipeCategoryWhereInput[]
    OR?: RecipeCategoryWhereInput[]
    NOT?: RecipeCategoryWhereInput | RecipeCategoryWhereInput[]
    recipeId?: StringFilter<"RecipeCategory"> | string
    categoryId?: StringFilter<"RecipeCategory"> | string
    recipe?: XOR<RecipeRelationFilter, RecipeWhereInput>
    category?: XOR<CategoryRelationFilter, CategoryWhereInput>
  }, "recipeId_categoryId">

  export type RecipeCategoryOrderByWithAggregationInput = {
    recipeId?: SortOrder
    categoryId?: SortOrder
    _count?: RecipeCategoryCountOrderByAggregateInput
    _max?: RecipeCategoryMaxOrderByAggregateInput
    _min?: RecipeCategoryMinOrderByAggregateInput
  }

  export type RecipeCategoryScalarWhereWithAggregatesInput = {
    AND?: RecipeCategoryScalarWhereWithAggregatesInput | RecipeCategoryScalarWhereWithAggregatesInput[]
    OR?: RecipeCategoryScalarWhereWithAggregatesInput[]
    NOT?: RecipeCategoryScalarWhereWithAggregatesInput | RecipeCategoryScalarWhereWithAggregatesInput[]
    recipeId?: StringWithAggregatesFilter<"RecipeCategory"> | string
    categoryId?: StringWithAggregatesFilter<"RecipeCategory"> | string
  }

  export type CommentWhereInput = {
    AND?: CommentWhereInput | CommentWhereInput[]
    OR?: CommentWhereInput[]
    NOT?: CommentWhereInput | CommentWhereInput[]
    id?: StringFilter<"Comment"> | string
    content?: StringFilter<"Comment"> | string
    rating?: IntNullableFilter<"Comment"> | number | null
    createdAt?: DateTimeFilter<"Comment"> | Date | string
    userId?: StringFilter<"Comment"> | string
    recipeId?: StringFilter<"Comment"> | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    recipe?: XOR<RecipeRelationFilter, RecipeWhereInput>
  }

  export type CommentOrderByWithRelationInput = {
    id?: SortOrder
    content?: SortOrder
    rating?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    userId?: SortOrder
    recipeId?: SortOrder
    user?: UserOrderByWithRelationInput
    recipe?: RecipeOrderByWithRelationInput
  }

  export type CommentWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: CommentWhereInput | CommentWhereInput[]
    OR?: CommentWhereInput[]
    NOT?: CommentWhereInput | CommentWhereInput[]
    content?: StringFilter<"Comment"> | string
    rating?: IntNullableFilter<"Comment"> | number | null
    createdAt?: DateTimeFilter<"Comment"> | Date | string
    userId?: StringFilter<"Comment"> | string
    recipeId?: StringFilter<"Comment"> | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    recipe?: XOR<RecipeRelationFilter, RecipeWhereInput>
  }, "id">

  export type CommentOrderByWithAggregationInput = {
    id?: SortOrder
    content?: SortOrder
    rating?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    userId?: SortOrder
    recipeId?: SortOrder
    _count?: CommentCountOrderByAggregateInput
    _avg?: CommentAvgOrderByAggregateInput
    _max?: CommentMaxOrderByAggregateInput
    _min?: CommentMinOrderByAggregateInput
    _sum?: CommentSumOrderByAggregateInput
  }

  export type CommentScalarWhereWithAggregatesInput = {
    AND?: CommentScalarWhereWithAggregatesInput | CommentScalarWhereWithAggregatesInput[]
    OR?: CommentScalarWhereWithAggregatesInput[]
    NOT?: CommentScalarWhereWithAggregatesInput | CommentScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Comment"> | string
    content?: StringWithAggregatesFilter<"Comment"> | string
    rating?: IntNullableWithAggregatesFilter<"Comment"> | number | null
    createdAt?: DateTimeWithAggregatesFilter<"Comment"> | Date | string
    userId?: StringWithAggregatesFilter<"Comment"> | string
    recipeId?: StringWithAggregatesFilter<"Comment"> | string
  }

  export type UserFavoriteWhereInput = {
    AND?: UserFavoriteWhereInput | UserFavoriteWhereInput[]
    OR?: UserFavoriteWhereInput[]
    NOT?: UserFavoriteWhereInput | UserFavoriteWhereInput[]
    userId?: StringFilter<"UserFavorite"> | string
    recipeId?: StringFilter<"UserFavorite"> | string
    createdAt?: DateTimeFilter<"UserFavorite"> | Date | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    recipe?: XOR<RecipeRelationFilter, RecipeWhereInput>
  }

  export type UserFavoriteOrderByWithRelationInput = {
    userId?: SortOrder
    recipeId?: SortOrder
    createdAt?: SortOrder
    user?: UserOrderByWithRelationInput
    recipe?: RecipeOrderByWithRelationInput
  }

  export type UserFavoriteWhereUniqueInput = Prisma.AtLeast<{
    userId_recipeId?: UserFavoriteUserIdRecipeIdCompoundUniqueInput
    AND?: UserFavoriteWhereInput | UserFavoriteWhereInput[]
    OR?: UserFavoriteWhereInput[]
    NOT?: UserFavoriteWhereInput | UserFavoriteWhereInput[]
    userId?: StringFilter<"UserFavorite"> | string
    recipeId?: StringFilter<"UserFavorite"> | string
    createdAt?: DateTimeFilter<"UserFavorite"> | Date | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    recipe?: XOR<RecipeRelationFilter, RecipeWhereInput>
  }, "userId_recipeId">

  export type UserFavoriteOrderByWithAggregationInput = {
    userId?: SortOrder
    recipeId?: SortOrder
    createdAt?: SortOrder
    _count?: UserFavoriteCountOrderByAggregateInput
    _max?: UserFavoriteMaxOrderByAggregateInput
    _min?: UserFavoriteMinOrderByAggregateInput
  }

  export type UserFavoriteScalarWhereWithAggregatesInput = {
    AND?: UserFavoriteScalarWhereWithAggregatesInput | UserFavoriteScalarWhereWithAggregatesInput[]
    OR?: UserFavoriteScalarWhereWithAggregatesInput[]
    NOT?: UserFavoriteScalarWhereWithAggregatesInput | UserFavoriteScalarWhereWithAggregatesInput[]
    userId?: StringWithAggregatesFilter<"UserFavorite"> | string
    recipeId?: StringWithAggregatesFilter<"UserFavorite"> | string
    createdAt?: DateTimeWithAggregatesFilter<"UserFavorite"> | Date | string
  }

  export type UserCreateInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeCreateNestedManyWithoutAuthorInput
    comments?: CommentCreateNestedManyWithoutUserInput
    favorites?: UserFavoriteCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationCreateNestedOneWithoutUserInput
    reviewedApplications?: InheritorApplicationCreateNestedManyWithoutReviewerInput
  }

  export type UserUncheckedCreateInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeUncheckedCreateNestedManyWithoutAuthorInput
    comments?: CommentUncheckedCreateNestedManyWithoutUserInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationUncheckedCreateNestedOneWithoutUserInput
    reviewedApplications?: InheritorApplicationUncheckedCreateNestedManyWithoutReviewerInput
  }

  export type UserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUpdateManyWithoutAuthorNestedInput
    comments?: CommentUpdateManyWithoutUserNestedInput
    favorites?: UserFavoriteUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUpdateOneWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUpdateManyWithoutReviewerNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUncheckedUpdateManyWithoutAuthorNestedInput
    comments?: CommentUncheckedUpdateManyWithoutUserNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUncheckedUpdateOneWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUncheckedUpdateManyWithoutReviewerNestedInput
  }

  export type UserCreateManyInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type InheritorApplicationCreateInput = {
    id?: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications?: string | null
    introduction: string
    status?: $Enums.ApplicationStatus
    appliedAt?: Date | string
    reviewedAt?: Date | string | null
    reviewComment?: string | null
    user: UserCreateNestedOneWithoutInheritorApplicationInput
    reviewer?: UserCreateNestedOneWithoutReviewedApplicationsInput
  }

  export type InheritorApplicationUncheckedCreateInput = {
    id?: string
    userId: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications?: string | null
    introduction: string
    status?: $Enums.ApplicationStatus
    appliedAt?: Date | string
    reviewedAt?: Date | string | null
    reviewedBy?: string | null
    reviewComment?: string | null
  }

  export type InheritorApplicationUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
    user?: UserUpdateOneRequiredWithoutInheritorApplicationNestedInput
    reviewer?: UserUpdateOneWithoutReviewedApplicationsNestedInput
  }

  export type InheritorApplicationUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewedBy?: NullableStringFieldUpdateOperationsInput | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type InheritorApplicationCreateManyInput = {
    id?: string
    userId: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications?: string | null
    introduction: string
    status?: $Enums.ApplicationStatus
    appliedAt?: Date | string
    reviewedAt?: Date | string | null
    reviewedBy?: string | null
    reviewComment?: string | null
  }

  export type InheritorApplicationUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type InheritorApplicationUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewedBy?: NullableStringFieldUpdateOperationsInput | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type RecipeCreateInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    author: UserCreateNestedOneWithoutRecipesInput
    categories?: RecipeCategoryCreateNestedManyWithoutRecipeInput
    comments?: CommentCreateNestedManyWithoutRecipeInput
    favorites?: UserFavoriteCreateNestedManyWithoutRecipeInput
  }

  export type RecipeUncheckedCreateInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    authorId: string
    categories?: RecipeCategoryUncheckedCreateNestedManyWithoutRecipeInput
    comments?: CommentUncheckedCreateNestedManyWithoutRecipeInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutRecipeInput
  }

  export type RecipeUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    author?: UserUpdateOneRequiredWithoutRecipesNestedInput
    categories?: RecipeCategoryUpdateManyWithoutRecipeNestedInput
    comments?: CommentUpdateManyWithoutRecipeNestedInput
    favorites?: UserFavoriteUpdateManyWithoutRecipeNestedInput
  }

  export type RecipeUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    authorId?: StringFieldUpdateOperationsInput | string
    categories?: RecipeCategoryUncheckedUpdateManyWithoutRecipeNestedInput
    comments?: CommentUncheckedUpdateManyWithoutRecipeNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutRecipeNestedInput
  }

  export type RecipeCreateManyInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    authorId: string
  }

  export type RecipeUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecipeUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    authorId?: StringFieldUpdateOperationsInput | string
  }

  export type CategoryCreateInput = {
    id?: string
    name: string
    description?: string | null
    icon?: string | null
    createdAt?: Date | string
    recipes?: RecipeCategoryCreateNestedManyWithoutCategoryInput
  }

  export type CategoryUncheckedCreateInput = {
    id?: string
    name: string
    description?: string | null
    icon?: string | null
    createdAt?: Date | string
    recipes?: RecipeCategoryUncheckedCreateNestedManyWithoutCategoryInput
  }

  export type CategoryUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    icon?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeCategoryUpdateManyWithoutCategoryNestedInput
  }

  export type CategoryUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    icon?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeCategoryUncheckedUpdateManyWithoutCategoryNestedInput
  }

  export type CategoryCreateManyInput = {
    id?: string
    name: string
    description?: string | null
    icon?: string | null
    createdAt?: Date | string
  }

  export type CategoryUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    icon?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CategoryUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    icon?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecipeCategoryCreateInput = {
    recipe: RecipeCreateNestedOneWithoutCategoriesInput
    category: CategoryCreateNestedOneWithoutRecipesInput
  }

  export type RecipeCategoryUncheckedCreateInput = {
    recipeId: string
    categoryId: string
  }

  export type RecipeCategoryUpdateInput = {
    recipe?: RecipeUpdateOneRequiredWithoutCategoriesNestedInput
    category?: CategoryUpdateOneRequiredWithoutRecipesNestedInput
  }

  export type RecipeCategoryUncheckedUpdateInput = {
    recipeId?: StringFieldUpdateOperationsInput | string
    categoryId?: StringFieldUpdateOperationsInput | string
  }

  export type RecipeCategoryCreateManyInput = {
    recipeId: string
    categoryId: string
  }

  export type RecipeCategoryUpdateManyMutationInput = {

  }

  export type RecipeCategoryUncheckedUpdateManyInput = {
    recipeId?: StringFieldUpdateOperationsInput | string
    categoryId?: StringFieldUpdateOperationsInput | string
  }

  export type CommentCreateInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutCommentsInput
    recipe: RecipeCreateNestedOneWithoutCommentsInput
  }

  export type CommentUncheckedCreateInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    userId: string
    recipeId: string
  }

  export type CommentUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutCommentsNestedInput
    recipe?: RecipeUpdateOneRequiredWithoutCommentsNestedInput
  }

  export type CommentUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
    recipeId?: StringFieldUpdateOperationsInput | string
  }

  export type CommentCreateManyInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    userId: string
    recipeId: string
  }

  export type CommentUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CommentUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
    recipeId?: StringFieldUpdateOperationsInput | string
  }

  export type UserFavoriteCreateInput = {
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutFavoritesInput
    recipe: RecipeCreateNestedOneWithoutFavoritesInput
  }

  export type UserFavoriteUncheckedCreateInput = {
    userId: string
    recipeId: string
    createdAt?: Date | string
  }

  export type UserFavoriteUpdateInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutFavoritesNestedInput
    recipe?: RecipeUpdateOneRequiredWithoutFavoritesNestedInput
  }

  export type UserFavoriteUncheckedUpdateInput = {
    userId?: StringFieldUpdateOperationsInput | string
    recipeId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFavoriteCreateManyInput = {
    userId: string
    recipeId: string
    createdAt?: Date | string
  }

  export type UserFavoriteUpdateManyMutationInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFavoriteUncheckedUpdateManyInput = {
    userId?: StringFieldUpdateOperationsInput | string
    recipeId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type EnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type RecipeListRelationFilter = {
    every?: RecipeWhereInput
    some?: RecipeWhereInput
    none?: RecipeWhereInput
  }

  export type CommentListRelationFilter = {
    every?: CommentWhereInput
    some?: CommentWhereInput
    none?: CommentWhereInput
  }

  export type UserFavoriteListRelationFilter = {
    every?: UserFavoriteWhereInput
    some?: UserFavoriteWhereInput
    none?: UserFavoriteWhereInput
  }

  export type InheritorApplicationNullableRelationFilter = {
    is?: InheritorApplicationWhereInput | null
    isNot?: InheritorApplicationWhereInput | null
  }

  export type InheritorApplicationListRelationFilter = {
    every?: InheritorApplicationWhereInput
    some?: InheritorApplicationWhereInput
    none?: InheritorApplicationWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type RecipeOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type CommentOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserFavoriteOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type InheritorApplicationOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrder
    password?: SortOrder
    role?: SortOrder
    avatar?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrder
    password?: SortOrder
    role?: SortOrder
    avatar?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    email?: SortOrder
    password?: SortOrder
    role?: SortOrder
    avatar?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type EnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type EnumApplicationStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.ApplicationStatus | EnumApplicationStatusFieldRefInput<$PrismaModel>
    in?: $Enums.ApplicationStatus[] | ListEnumApplicationStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.ApplicationStatus[] | ListEnumApplicationStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumApplicationStatusFilter<$PrismaModel> | $Enums.ApplicationStatus
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type UserRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type UserNullableRelationFilter = {
    is?: UserWhereInput | null
    isNot?: UserWhereInput | null
  }

  export type InheritorApplicationCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    realName?: SortOrder
    phone?: SortOrder
    region?: SortOrder
    specialties?: SortOrder
    experience?: SortOrder
    certifications?: SortOrder
    introduction?: SortOrder
    status?: SortOrder
    appliedAt?: SortOrder
    reviewedAt?: SortOrder
    reviewedBy?: SortOrder
    reviewComment?: SortOrder
  }

  export type InheritorApplicationMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    realName?: SortOrder
    phone?: SortOrder
    region?: SortOrder
    specialties?: SortOrder
    experience?: SortOrder
    certifications?: SortOrder
    introduction?: SortOrder
    status?: SortOrder
    appliedAt?: SortOrder
    reviewedAt?: SortOrder
    reviewedBy?: SortOrder
    reviewComment?: SortOrder
  }

  export type InheritorApplicationMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    realName?: SortOrder
    phone?: SortOrder
    region?: SortOrder
    specialties?: SortOrder
    experience?: SortOrder
    certifications?: SortOrder
    introduction?: SortOrder
    status?: SortOrder
    appliedAt?: SortOrder
    reviewedAt?: SortOrder
    reviewedBy?: SortOrder
    reviewComment?: SortOrder
  }

  export type EnumApplicationStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.ApplicationStatus | EnumApplicationStatusFieldRefInput<$PrismaModel>
    in?: $Enums.ApplicationStatus[] | ListEnumApplicationStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.ApplicationStatus[] | ListEnumApplicationStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumApplicationStatusWithAggregatesFilter<$PrismaModel> | $Enums.ApplicationStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumApplicationStatusFilter<$PrismaModel>
    _max?: NestedEnumApplicationStatusFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type RecipeCategoryListRelationFilter = {
    every?: RecipeCategoryWhereInput
    some?: RecipeCategoryWhereInput
    none?: RecipeCategoryWhereInput
  }

  export type RecipeCategoryOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type RecipeCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    dialectName?: SortOrder
    dialectAudio?: SortOrder
    region?: SortOrder
    ingredients?: SortOrder
    steps?: SortOrder
    image?: SortOrder
    description?: SortOrder
    difficulty?: SortOrder
    cookTime?: SortOrder
    servings?: SortOrder
    isPublished?: SortOrder
    viewCount?: SortOrder
    culturalHistory?: SortOrder
    regionalFeature?: SortOrder
    culturalMeaning?: SortOrder
    inheritanceStory?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    authorId?: SortOrder
  }

  export type RecipeAvgOrderByAggregateInput = {
    difficulty?: SortOrder
    cookTime?: SortOrder
    servings?: SortOrder
    viewCount?: SortOrder
  }

  export type RecipeMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    dialectName?: SortOrder
    dialectAudio?: SortOrder
    region?: SortOrder
    ingredients?: SortOrder
    steps?: SortOrder
    image?: SortOrder
    description?: SortOrder
    difficulty?: SortOrder
    cookTime?: SortOrder
    servings?: SortOrder
    isPublished?: SortOrder
    viewCount?: SortOrder
    culturalHistory?: SortOrder
    regionalFeature?: SortOrder
    culturalMeaning?: SortOrder
    inheritanceStory?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    authorId?: SortOrder
  }

  export type RecipeMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    dialectName?: SortOrder
    dialectAudio?: SortOrder
    region?: SortOrder
    ingredients?: SortOrder
    steps?: SortOrder
    image?: SortOrder
    description?: SortOrder
    difficulty?: SortOrder
    cookTime?: SortOrder
    servings?: SortOrder
    isPublished?: SortOrder
    viewCount?: SortOrder
    culturalHistory?: SortOrder
    regionalFeature?: SortOrder
    culturalMeaning?: SortOrder
    inheritanceStory?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    authorId?: SortOrder
  }

  export type RecipeSumOrderByAggregateInput = {
    difficulty?: SortOrder
    cookTime?: SortOrder
    servings?: SortOrder
    viewCount?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type CategoryCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    icon?: SortOrder
    createdAt?: SortOrder
  }

  export type CategoryMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    icon?: SortOrder
    createdAt?: SortOrder
  }

  export type CategoryMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    icon?: SortOrder
    createdAt?: SortOrder
  }

  export type RecipeRelationFilter = {
    is?: RecipeWhereInput
    isNot?: RecipeWhereInput
  }

  export type CategoryRelationFilter = {
    is?: CategoryWhereInput
    isNot?: CategoryWhereInput
  }

  export type RecipeCategoryRecipeIdCategoryIdCompoundUniqueInput = {
    recipeId: string
    categoryId: string
  }

  export type RecipeCategoryCountOrderByAggregateInput = {
    recipeId?: SortOrder
    categoryId?: SortOrder
  }

  export type RecipeCategoryMaxOrderByAggregateInput = {
    recipeId?: SortOrder
    categoryId?: SortOrder
  }

  export type RecipeCategoryMinOrderByAggregateInput = {
    recipeId?: SortOrder
    categoryId?: SortOrder
  }

  export type CommentCountOrderByAggregateInput = {
    id?: SortOrder
    content?: SortOrder
    rating?: SortOrder
    createdAt?: SortOrder
    userId?: SortOrder
    recipeId?: SortOrder
  }

  export type CommentAvgOrderByAggregateInput = {
    rating?: SortOrder
  }

  export type CommentMaxOrderByAggregateInput = {
    id?: SortOrder
    content?: SortOrder
    rating?: SortOrder
    createdAt?: SortOrder
    userId?: SortOrder
    recipeId?: SortOrder
  }

  export type CommentMinOrderByAggregateInput = {
    id?: SortOrder
    content?: SortOrder
    rating?: SortOrder
    createdAt?: SortOrder
    userId?: SortOrder
    recipeId?: SortOrder
  }

  export type CommentSumOrderByAggregateInput = {
    rating?: SortOrder
  }

  export type UserFavoriteUserIdRecipeIdCompoundUniqueInput = {
    userId: string
    recipeId: string
  }

  export type UserFavoriteCountOrderByAggregateInput = {
    userId?: SortOrder
    recipeId?: SortOrder
    createdAt?: SortOrder
  }

  export type UserFavoriteMaxOrderByAggregateInput = {
    userId?: SortOrder
    recipeId?: SortOrder
    createdAt?: SortOrder
  }

  export type UserFavoriteMinOrderByAggregateInput = {
    userId?: SortOrder
    recipeId?: SortOrder
    createdAt?: SortOrder
  }

  export type RecipeCreateNestedManyWithoutAuthorInput = {
    create?: XOR<RecipeCreateWithoutAuthorInput, RecipeUncheckedCreateWithoutAuthorInput> | RecipeCreateWithoutAuthorInput[] | RecipeUncheckedCreateWithoutAuthorInput[]
    connectOrCreate?: RecipeCreateOrConnectWithoutAuthorInput | RecipeCreateOrConnectWithoutAuthorInput[]
    createMany?: RecipeCreateManyAuthorInputEnvelope
    connect?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
  }

  export type CommentCreateNestedManyWithoutUserInput = {
    create?: XOR<CommentCreateWithoutUserInput, CommentUncheckedCreateWithoutUserInput> | CommentCreateWithoutUserInput[] | CommentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CommentCreateOrConnectWithoutUserInput | CommentCreateOrConnectWithoutUserInput[]
    createMany?: CommentCreateManyUserInputEnvelope
    connect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
  }

  export type UserFavoriteCreateNestedManyWithoutUserInput = {
    create?: XOR<UserFavoriteCreateWithoutUserInput, UserFavoriteUncheckedCreateWithoutUserInput> | UserFavoriteCreateWithoutUserInput[] | UserFavoriteUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserFavoriteCreateOrConnectWithoutUserInput | UserFavoriteCreateOrConnectWithoutUserInput[]
    createMany?: UserFavoriteCreateManyUserInputEnvelope
    connect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
  }

  export type InheritorApplicationCreateNestedOneWithoutUserInput = {
    create?: XOR<InheritorApplicationCreateWithoutUserInput, InheritorApplicationUncheckedCreateWithoutUserInput>
    connectOrCreate?: InheritorApplicationCreateOrConnectWithoutUserInput
    connect?: InheritorApplicationWhereUniqueInput
  }

  export type InheritorApplicationCreateNestedManyWithoutReviewerInput = {
    create?: XOR<InheritorApplicationCreateWithoutReviewerInput, InheritorApplicationUncheckedCreateWithoutReviewerInput> | InheritorApplicationCreateWithoutReviewerInput[] | InheritorApplicationUncheckedCreateWithoutReviewerInput[]
    connectOrCreate?: InheritorApplicationCreateOrConnectWithoutReviewerInput | InheritorApplicationCreateOrConnectWithoutReviewerInput[]
    createMany?: InheritorApplicationCreateManyReviewerInputEnvelope
    connect?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
  }

  export type RecipeUncheckedCreateNestedManyWithoutAuthorInput = {
    create?: XOR<RecipeCreateWithoutAuthorInput, RecipeUncheckedCreateWithoutAuthorInput> | RecipeCreateWithoutAuthorInput[] | RecipeUncheckedCreateWithoutAuthorInput[]
    connectOrCreate?: RecipeCreateOrConnectWithoutAuthorInput | RecipeCreateOrConnectWithoutAuthorInput[]
    createMany?: RecipeCreateManyAuthorInputEnvelope
    connect?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
  }

  export type CommentUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<CommentCreateWithoutUserInput, CommentUncheckedCreateWithoutUserInput> | CommentCreateWithoutUserInput[] | CommentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CommentCreateOrConnectWithoutUserInput | CommentCreateOrConnectWithoutUserInput[]
    createMany?: CommentCreateManyUserInputEnvelope
    connect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
  }

  export type UserFavoriteUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<UserFavoriteCreateWithoutUserInput, UserFavoriteUncheckedCreateWithoutUserInput> | UserFavoriteCreateWithoutUserInput[] | UserFavoriteUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserFavoriteCreateOrConnectWithoutUserInput | UserFavoriteCreateOrConnectWithoutUserInput[]
    createMany?: UserFavoriteCreateManyUserInputEnvelope
    connect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
  }

  export type InheritorApplicationUncheckedCreateNestedOneWithoutUserInput = {
    create?: XOR<InheritorApplicationCreateWithoutUserInput, InheritorApplicationUncheckedCreateWithoutUserInput>
    connectOrCreate?: InheritorApplicationCreateOrConnectWithoutUserInput
    connect?: InheritorApplicationWhereUniqueInput
  }

  export type InheritorApplicationUncheckedCreateNestedManyWithoutReviewerInput = {
    create?: XOR<InheritorApplicationCreateWithoutReviewerInput, InheritorApplicationUncheckedCreateWithoutReviewerInput> | InheritorApplicationCreateWithoutReviewerInput[] | InheritorApplicationUncheckedCreateWithoutReviewerInput[]
    connectOrCreate?: InheritorApplicationCreateOrConnectWithoutReviewerInput | InheritorApplicationCreateOrConnectWithoutReviewerInput[]
    createMany?: InheritorApplicationCreateManyReviewerInputEnvelope
    connect?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type EnumUserRoleFieldUpdateOperationsInput = {
    set?: $Enums.UserRole
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type RecipeUpdateManyWithoutAuthorNestedInput = {
    create?: XOR<RecipeCreateWithoutAuthorInput, RecipeUncheckedCreateWithoutAuthorInput> | RecipeCreateWithoutAuthorInput[] | RecipeUncheckedCreateWithoutAuthorInput[]
    connectOrCreate?: RecipeCreateOrConnectWithoutAuthorInput | RecipeCreateOrConnectWithoutAuthorInput[]
    upsert?: RecipeUpsertWithWhereUniqueWithoutAuthorInput | RecipeUpsertWithWhereUniqueWithoutAuthorInput[]
    createMany?: RecipeCreateManyAuthorInputEnvelope
    set?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
    disconnect?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
    delete?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
    connect?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
    update?: RecipeUpdateWithWhereUniqueWithoutAuthorInput | RecipeUpdateWithWhereUniqueWithoutAuthorInput[]
    updateMany?: RecipeUpdateManyWithWhereWithoutAuthorInput | RecipeUpdateManyWithWhereWithoutAuthorInput[]
    deleteMany?: RecipeScalarWhereInput | RecipeScalarWhereInput[]
  }

  export type CommentUpdateManyWithoutUserNestedInput = {
    create?: XOR<CommentCreateWithoutUserInput, CommentUncheckedCreateWithoutUserInput> | CommentCreateWithoutUserInput[] | CommentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CommentCreateOrConnectWithoutUserInput | CommentCreateOrConnectWithoutUserInput[]
    upsert?: CommentUpsertWithWhereUniqueWithoutUserInput | CommentUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: CommentCreateManyUserInputEnvelope
    set?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    disconnect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    delete?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    connect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    update?: CommentUpdateWithWhereUniqueWithoutUserInput | CommentUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: CommentUpdateManyWithWhereWithoutUserInput | CommentUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: CommentScalarWhereInput | CommentScalarWhereInput[]
  }

  export type UserFavoriteUpdateManyWithoutUserNestedInput = {
    create?: XOR<UserFavoriteCreateWithoutUserInput, UserFavoriteUncheckedCreateWithoutUserInput> | UserFavoriteCreateWithoutUserInput[] | UserFavoriteUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserFavoriteCreateOrConnectWithoutUserInput | UserFavoriteCreateOrConnectWithoutUserInput[]
    upsert?: UserFavoriteUpsertWithWhereUniqueWithoutUserInput | UserFavoriteUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: UserFavoriteCreateManyUserInputEnvelope
    set?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    disconnect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    delete?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    connect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    update?: UserFavoriteUpdateWithWhereUniqueWithoutUserInput | UserFavoriteUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: UserFavoriteUpdateManyWithWhereWithoutUserInput | UserFavoriteUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: UserFavoriteScalarWhereInput | UserFavoriteScalarWhereInput[]
  }

  export type InheritorApplicationUpdateOneWithoutUserNestedInput = {
    create?: XOR<InheritorApplicationCreateWithoutUserInput, InheritorApplicationUncheckedCreateWithoutUserInput>
    connectOrCreate?: InheritorApplicationCreateOrConnectWithoutUserInput
    upsert?: InheritorApplicationUpsertWithoutUserInput
    disconnect?: InheritorApplicationWhereInput | boolean
    delete?: InheritorApplicationWhereInput | boolean
    connect?: InheritorApplicationWhereUniqueInput
    update?: XOR<XOR<InheritorApplicationUpdateToOneWithWhereWithoutUserInput, InheritorApplicationUpdateWithoutUserInput>, InheritorApplicationUncheckedUpdateWithoutUserInput>
  }

  export type InheritorApplicationUpdateManyWithoutReviewerNestedInput = {
    create?: XOR<InheritorApplicationCreateWithoutReviewerInput, InheritorApplicationUncheckedCreateWithoutReviewerInput> | InheritorApplicationCreateWithoutReviewerInput[] | InheritorApplicationUncheckedCreateWithoutReviewerInput[]
    connectOrCreate?: InheritorApplicationCreateOrConnectWithoutReviewerInput | InheritorApplicationCreateOrConnectWithoutReviewerInput[]
    upsert?: InheritorApplicationUpsertWithWhereUniqueWithoutReviewerInput | InheritorApplicationUpsertWithWhereUniqueWithoutReviewerInput[]
    createMany?: InheritorApplicationCreateManyReviewerInputEnvelope
    set?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
    disconnect?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
    delete?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
    connect?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
    update?: InheritorApplicationUpdateWithWhereUniqueWithoutReviewerInput | InheritorApplicationUpdateWithWhereUniqueWithoutReviewerInput[]
    updateMany?: InheritorApplicationUpdateManyWithWhereWithoutReviewerInput | InheritorApplicationUpdateManyWithWhereWithoutReviewerInput[]
    deleteMany?: InheritorApplicationScalarWhereInput | InheritorApplicationScalarWhereInput[]
  }

  export type RecipeUncheckedUpdateManyWithoutAuthorNestedInput = {
    create?: XOR<RecipeCreateWithoutAuthorInput, RecipeUncheckedCreateWithoutAuthorInput> | RecipeCreateWithoutAuthorInput[] | RecipeUncheckedCreateWithoutAuthorInput[]
    connectOrCreate?: RecipeCreateOrConnectWithoutAuthorInput | RecipeCreateOrConnectWithoutAuthorInput[]
    upsert?: RecipeUpsertWithWhereUniqueWithoutAuthorInput | RecipeUpsertWithWhereUniqueWithoutAuthorInput[]
    createMany?: RecipeCreateManyAuthorInputEnvelope
    set?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
    disconnect?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
    delete?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
    connect?: RecipeWhereUniqueInput | RecipeWhereUniqueInput[]
    update?: RecipeUpdateWithWhereUniqueWithoutAuthorInput | RecipeUpdateWithWhereUniqueWithoutAuthorInput[]
    updateMany?: RecipeUpdateManyWithWhereWithoutAuthorInput | RecipeUpdateManyWithWhereWithoutAuthorInput[]
    deleteMany?: RecipeScalarWhereInput | RecipeScalarWhereInput[]
  }

  export type CommentUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<CommentCreateWithoutUserInput, CommentUncheckedCreateWithoutUserInput> | CommentCreateWithoutUserInput[] | CommentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CommentCreateOrConnectWithoutUserInput | CommentCreateOrConnectWithoutUserInput[]
    upsert?: CommentUpsertWithWhereUniqueWithoutUserInput | CommentUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: CommentCreateManyUserInputEnvelope
    set?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    disconnect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    delete?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    connect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    update?: CommentUpdateWithWhereUniqueWithoutUserInput | CommentUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: CommentUpdateManyWithWhereWithoutUserInput | CommentUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: CommentScalarWhereInput | CommentScalarWhereInput[]
  }

  export type UserFavoriteUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<UserFavoriteCreateWithoutUserInput, UserFavoriteUncheckedCreateWithoutUserInput> | UserFavoriteCreateWithoutUserInput[] | UserFavoriteUncheckedCreateWithoutUserInput[]
    connectOrCreate?: UserFavoriteCreateOrConnectWithoutUserInput | UserFavoriteCreateOrConnectWithoutUserInput[]
    upsert?: UserFavoriteUpsertWithWhereUniqueWithoutUserInput | UserFavoriteUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: UserFavoriteCreateManyUserInputEnvelope
    set?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    disconnect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    delete?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    connect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    update?: UserFavoriteUpdateWithWhereUniqueWithoutUserInput | UserFavoriteUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: UserFavoriteUpdateManyWithWhereWithoutUserInput | UserFavoriteUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: UserFavoriteScalarWhereInput | UserFavoriteScalarWhereInput[]
  }

  export type InheritorApplicationUncheckedUpdateOneWithoutUserNestedInput = {
    create?: XOR<InheritorApplicationCreateWithoutUserInput, InheritorApplicationUncheckedCreateWithoutUserInput>
    connectOrCreate?: InheritorApplicationCreateOrConnectWithoutUserInput
    upsert?: InheritorApplicationUpsertWithoutUserInput
    disconnect?: InheritorApplicationWhereInput | boolean
    delete?: InheritorApplicationWhereInput | boolean
    connect?: InheritorApplicationWhereUniqueInput
    update?: XOR<XOR<InheritorApplicationUpdateToOneWithWhereWithoutUserInput, InheritorApplicationUpdateWithoutUserInput>, InheritorApplicationUncheckedUpdateWithoutUserInput>
  }

  export type InheritorApplicationUncheckedUpdateManyWithoutReviewerNestedInput = {
    create?: XOR<InheritorApplicationCreateWithoutReviewerInput, InheritorApplicationUncheckedCreateWithoutReviewerInput> | InheritorApplicationCreateWithoutReviewerInput[] | InheritorApplicationUncheckedCreateWithoutReviewerInput[]
    connectOrCreate?: InheritorApplicationCreateOrConnectWithoutReviewerInput | InheritorApplicationCreateOrConnectWithoutReviewerInput[]
    upsert?: InheritorApplicationUpsertWithWhereUniqueWithoutReviewerInput | InheritorApplicationUpsertWithWhereUniqueWithoutReviewerInput[]
    createMany?: InheritorApplicationCreateManyReviewerInputEnvelope
    set?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
    disconnect?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
    delete?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
    connect?: InheritorApplicationWhereUniqueInput | InheritorApplicationWhereUniqueInput[]
    update?: InheritorApplicationUpdateWithWhereUniqueWithoutReviewerInput | InheritorApplicationUpdateWithWhereUniqueWithoutReviewerInput[]
    updateMany?: InheritorApplicationUpdateManyWithWhereWithoutReviewerInput | InheritorApplicationUpdateManyWithWhereWithoutReviewerInput[]
    deleteMany?: InheritorApplicationScalarWhereInput | InheritorApplicationScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutInheritorApplicationInput = {
    create?: XOR<UserCreateWithoutInheritorApplicationInput, UserUncheckedCreateWithoutInheritorApplicationInput>
    connectOrCreate?: UserCreateOrConnectWithoutInheritorApplicationInput
    connect?: UserWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutReviewedApplicationsInput = {
    create?: XOR<UserCreateWithoutReviewedApplicationsInput, UserUncheckedCreateWithoutReviewedApplicationsInput>
    connectOrCreate?: UserCreateOrConnectWithoutReviewedApplicationsInput
    connect?: UserWhereUniqueInput
  }

  export type EnumApplicationStatusFieldUpdateOperationsInput = {
    set?: $Enums.ApplicationStatus
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type UserUpdateOneRequiredWithoutInheritorApplicationNestedInput = {
    create?: XOR<UserCreateWithoutInheritorApplicationInput, UserUncheckedCreateWithoutInheritorApplicationInput>
    connectOrCreate?: UserCreateOrConnectWithoutInheritorApplicationInput
    upsert?: UserUpsertWithoutInheritorApplicationInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutInheritorApplicationInput, UserUpdateWithoutInheritorApplicationInput>, UserUncheckedUpdateWithoutInheritorApplicationInput>
  }

  export type UserUpdateOneWithoutReviewedApplicationsNestedInput = {
    create?: XOR<UserCreateWithoutReviewedApplicationsInput, UserUncheckedCreateWithoutReviewedApplicationsInput>
    connectOrCreate?: UserCreateOrConnectWithoutReviewedApplicationsInput
    upsert?: UserUpsertWithoutReviewedApplicationsInput
    disconnect?: UserWhereInput | boolean
    delete?: UserWhereInput | boolean
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutReviewedApplicationsInput, UserUpdateWithoutReviewedApplicationsInput>, UserUncheckedUpdateWithoutReviewedApplicationsInput>
  }

  export type UserCreateNestedOneWithoutRecipesInput = {
    create?: XOR<UserCreateWithoutRecipesInput, UserUncheckedCreateWithoutRecipesInput>
    connectOrCreate?: UserCreateOrConnectWithoutRecipesInput
    connect?: UserWhereUniqueInput
  }

  export type RecipeCategoryCreateNestedManyWithoutRecipeInput = {
    create?: XOR<RecipeCategoryCreateWithoutRecipeInput, RecipeCategoryUncheckedCreateWithoutRecipeInput> | RecipeCategoryCreateWithoutRecipeInput[] | RecipeCategoryUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: RecipeCategoryCreateOrConnectWithoutRecipeInput | RecipeCategoryCreateOrConnectWithoutRecipeInput[]
    createMany?: RecipeCategoryCreateManyRecipeInputEnvelope
    connect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
  }

  export type CommentCreateNestedManyWithoutRecipeInput = {
    create?: XOR<CommentCreateWithoutRecipeInput, CommentUncheckedCreateWithoutRecipeInput> | CommentCreateWithoutRecipeInput[] | CommentUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: CommentCreateOrConnectWithoutRecipeInput | CommentCreateOrConnectWithoutRecipeInput[]
    createMany?: CommentCreateManyRecipeInputEnvelope
    connect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
  }

  export type UserFavoriteCreateNestedManyWithoutRecipeInput = {
    create?: XOR<UserFavoriteCreateWithoutRecipeInput, UserFavoriteUncheckedCreateWithoutRecipeInput> | UserFavoriteCreateWithoutRecipeInput[] | UserFavoriteUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: UserFavoriteCreateOrConnectWithoutRecipeInput | UserFavoriteCreateOrConnectWithoutRecipeInput[]
    createMany?: UserFavoriteCreateManyRecipeInputEnvelope
    connect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
  }

  export type RecipeCategoryUncheckedCreateNestedManyWithoutRecipeInput = {
    create?: XOR<RecipeCategoryCreateWithoutRecipeInput, RecipeCategoryUncheckedCreateWithoutRecipeInput> | RecipeCategoryCreateWithoutRecipeInput[] | RecipeCategoryUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: RecipeCategoryCreateOrConnectWithoutRecipeInput | RecipeCategoryCreateOrConnectWithoutRecipeInput[]
    createMany?: RecipeCategoryCreateManyRecipeInputEnvelope
    connect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
  }

  export type CommentUncheckedCreateNestedManyWithoutRecipeInput = {
    create?: XOR<CommentCreateWithoutRecipeInput, CommentUncheckedCreateWithoutRecipeInput> | CommentCreateWithoutRecipeInput[] | CommentUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: CommentCreateOrConnectWithoutRecipeInput | CommentCreateOrConnectWithoutRecipeInput[]
    createMany?: CommentCreateManyRecipeInputEnvelope
    connect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
  }

  export type UserFavoriteUncheckedCreateNestedManyWithoutRecipeInput = {
    create?: XOR<UserFavoriteCreateWithoutRecipeInput, UserFavoriteUncheckedCreateWithoutRecipeInput> | UserFavoriteCreateWithoutRecipeInput[] | UserFavoriteUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: UserFavoriteCreateOrConnectWithoutRecipeInput | UserFavoriteCreateOrConnectWithoutRecipeInput[]
    createMany?: UserFavoriteCreateManyRecipeInputEnvelope
    connect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type UserUpdateOneRequiredWithoutRecipesNestedInput = {
    create?: XOR<UserCreateWithoutRecipesInput, UserUncheckedCreateWithoutRecipesInput>
    connectOrCreate?: UserCreateOrConnectWithoutRecipesInput
    upsert?: UserUpsertWithoutRecipesInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutRecipesInput, UserUpdateWithoutRecipesInput>, UserUncheckedUpdateWithoutRecipesInput>
  }

  export type RecipeCategoryUpdateManyWithoutRecipeNestedInput = {
    create?: XOR<RecipeCategoryCreateWithoutRecipeInput, RecipeCategoryUncheckedCreateWithoutRecipeInput> | RecipeCategoryCreateWithoutRecipeInput[] | RecipeCategoryUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: RecipeCategoryCreateOrConnectWithoutRecipeInput | RecipeCategoryCreateOrConnectWithoutRecipeInput[]
    upsert?: RecipeCategoryUpsertWithWhereUniqueWithoutRecipeInput | RecipeCategoryUpsertWithWhereUniqueWithoutRecipeInput[]
    createMany?: RecipeCategoryCreateManyRecipeInputEnvelope
    set?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    disconnect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    delete?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    connect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    update?: RecipeCategoryUpdateWithWhereUniqueWithoutRecipeInput | RecipeCategoryUpdateWithWhereUniqueWithoutRecipeInput[]
    updateMany?: RecipeCategoryUpdateManyWithWhereWithoutRecipeInput | RecipeCategoryUpdateManyWithWhereWithoutRecipeInput[]
    deleteMany?: RecipeCategoryScalarWhereInput | RecipeCategoryScalarWhereInput[]
  }

  export type CommentUpdateManyWithoutRecipeNestedInput = {
    create?: XOR<CommentCreateWithoutRecipeInput, CommentUncheckedCreateWithoutRecipeInput> | CommentCreateWithoutRecipeInput[] | CommentUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: CommentCreateOrConnectWithoutRecipeInput | CommentCreateOrConnectWithoutRecipeInput[]
    upsert?: CommentUpsertWithWhereUniqueWithoutRecipeInput | CommentUpsertWithWhereUniqueWithoutRecipeInput[]
    createMany?: CommentCreateManyRecipeInputEnvelope
    set?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    disconnect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    delete?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    connect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    update?: CommentUpdateWithWhereUniqueWithoutRecipeInput | CommentUpdateWithWhereUniqueWithoutRecipeInput[]
    updateMany?: CommentUpdateManyWithWhereWithoutRecipeInput | CommentUpdateManyWithWhereWithoutRecipeInput[]
    deleteMany?: CommentScalarWhereInput | CommentScalarWhereInput[]
  }

  export type UserFavoriteUpdateManyWithoutRecipeNestedInput = {
    create?: XOR<UserFavoriteCreateWithoutRecipeInput, UserFavoriteUncheckedCreateWithoutRecipeInput> | UserFavoriteCreateWithoutRecipeInput[] | UserFavoriteUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: UserFavoriteCreateOrConnectWithoutRecipeInput | UserFavoriteCreateOrConnectWithoutRecipeInput[]
    upsert?: UserFavoriteUpsertWithWhereUniqueWithoutRecipeInput | UserFavoriteUpsertWithWhereUniqueWithoutRecipeInput[]
    createMany?: UserFavoriteCreateManyRecipeInputEnvelope
    set?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    disconnect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    delete?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    connect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    update?: UserFavoriteUpdateWithWhereUniqueWithoutRecipeInput | UserFavoriteUpdateWithWhereUniqueWithoutRecipeInput[]
    updateMany?: UserFavoriteUpdateManyWithWhereWithoutRecipeInput | UserFavoriteUpdateManyWithWhereWithoutRecipeInput[]
    deleteMany?: UserFavoriteScalarWhereInput | UserFavoriteScalarWhereInput[]
  }

  export type RecipeCategoryUncheckedUpdateManyWithoutRecipeNestedInput = {
    create?: XOR<RecipeCategoryCreateWithoutRecipeInput, RecipeCategoryUncheckedCreateWithoutRecipeInput> | RecipeCategoryCreateWithoutRecipeInput[] | RecipeCategoryUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: RecipeCategoryCreateOrConnectWithoutRecipeInput | RecipeCategoryCreateOrConnectWithoutRecipeInput[]
    upsert?: RecipeCategoryUpsertWithWhereUniqueWithoutRecipeInput | RecipeCategoryUpsertWithWhereUniqueWithoutRecipeInput[]
    createMany?: RecipeCategoryCreateManyRecipeInputEnvelope
    set?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    disconnect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    delete?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    connect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    update?: RecipeCategoryUpdateWithWhereUniqueWithoutRecipeInput | RecipeCategoryUpdateWithWhereUniqueWithoutRecipeInput[]
    updateMany?: RecipeCategoryUpdateManyWithWhereWithoutRecipeInput | RecipeCategoryUpdateManyWithWhereWithoutRecipeInput[]
    deleteMany?: RecipeCategoryScalarWhereInput | RecipeCategoryScalarWhereInput[]
  }

  export type CommentUncheckedUpdateManyWithoutRecipeNestedInput = {
    create?: XOR<CommentCreateWithoutRecipeInput, CommentUncheckedCreateWithoutRecipeInput> | CommentCreateWithoutRecipeInput[] | CommentUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: CommentCreateOrConnectWithoutRecipeInput | CommentCreateOrConnectWithoutRecipeInput[]
    upsert?: CommentUpsertWithWhereUniqueWithoutRecipeInput | CommentUpsertWithWhereUniqueWithoutRecipeInput[]
    createMany?: CommentCreateManyRecipeInputEnvelope
    set?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    disconnect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    delete?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    connect?: CommentWhereUniqueInput | CommentWhereUniqueInput[]
    update?: CommentUpdateWithWhereUniqueWithoutRecipeInput | CommentUpdateWithWhereUniqueWithoutRecipeInput[]
    updateMany?: CommentUpdateManyWithWhereWithoutRecipeInput | CommentUpdateManyWithWhereWithoutRecipeInput[]
    deleteMany?: CommentScalarWhereInput | CommentScalarWhereInput[]
  }

  export type UserFavoriteUncheckedUpdateManyWithoutRecipeNestedInput = {
    create?: XOR<UserFavoriteCreateWithoutRecipeInput, UserFavoriteUncheckedCreateWithoutRecipeInput> | UserFavoriteCreateWithoutRecipeInput[] | UserFavoriteUncheckedCreateWithoutRecipeInput[]
    connectOrCreate?: UserFavoriteCreateOrConnectWithoutRecipeInput | UserFavoriteCreateOrConnectWithoutRecipeInput[]
    upsert?: UserFavoriteUpsertWithWhereUniqueWithoutRecipeInput | UserFavoriteUpsertWithWhereUniqueWithoutRecipeInput[]
    createMany?: UserFavoriteCreateManyRecipeInputEnvelope
    set?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    disconnect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    delete?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    connect?: UserFavoriteWhereUniqueInput | UserFavoriteWhereUniqueInput[]
    update?: UserFavoriteUpdateWithWhereUniqueWithoutRecipeInput | UserFavoriteUpdateWithWhereUniqueWithoutRecipeInput[]
    updateMany?: UserFavoriteUpdateManyWithWhereWithoutRecipeInput | UserFavoriteUpdateManyWithWhereWithoutRecipeInput[]
    deleteMany?: UserFavoriteScalarWhereInput | UserFavoriteScalarWhereInput[]
  }

  export type RecipeCategoryCreateNestedManyWithoutCategoryInput = {
    create?: XOR<RecipeCategoryCreateWithoutCategoryInput, RecipeCategoryUncheckedCreateWithoutCategoryInput> | RecipeCategoryCreateWithoutCategoryInput[] | RecipeCategoryUncheckedCreateWithoutCategoryInput[]
    connectOrCreate?: RecipeCategoryCreateOrConnectWithoutCategoryInput | RecipeCategoryCreateOrConnectWithoutCategoryInput[]
    createMany?: RecipeCategoryCreateManyCategoryInputEnvelope
    connect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
  }

  export type RecipeCategoryUncheckedCreateNestedManyWithoutCategoryInput = {
    create?: XOR<RecipeCategoryCreateWithoutCategoryInput, RecipeCategoryUncheckedCreateWithoutCategoryInput> | RecipeCategoryCreateWithoutCategoryInput[] | RecipeCategoryUncheckedCreateWithoutCategoryInput[]
    connectOrCreate?: RecipeCategoryCreateOrConnectWithoutCategoryInput | RecipeCategoryCreateOrConnectWithoutCategoryInput[]
    createMany?: RecipeCategoryCreateManyCategoryInputEnvelope
    connect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
  }

  export type RecipeCategoryUpdateManyWithoutCategoryNestedInput = {
    create?: XOR<RecipeCategoryCreateWithoutCategoryInput, RecipeCategoryUncheckedCreateWithoutCategoryInput> | RecipeCategoryCreateWithoutCategoryInput[] | RecipeCategoryUncheckedCreateWithoutCategoryInput[]
    connectOrCreate?: RecipeCategoryCreateOrConnectWithoutCategoryInput | RecipeCategoryCreateOrConnectWithoutCategoryInput[]
    upsert?: RecipeCategoryUpsertWithWhereUniqueWithoutCategoryInput | RecipeCategoryUpsertWithWhereUniqueWithoutCategoryInput[]
    createMany?: RecipeCategoryCreateManyCategoryInputEnvelope
    set?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    disconnect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    delete?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    connect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    update?: RecipeCategoryUpdateWithWhereUniqueWithoutCategoryInput | RecipeCategoryUpdateWithWhereUniqueWithoutCategoryInput[]
    updateMany?: RecipeCategoryUpdateManyWithWhereWithoutCategoryInput | RecipeCategoryUpdateManyWithWhereWithoutCategoryInput[]
    deleteMany?: RecipeCategoryScalarWhereInput | RecipeCategoryScalarWhereInput[]
  }

  export type RecipeCategoryUncheckedUpdateManyWithoutCategoryNestedInput = {
    create?: XOR<RecipeCategoryCreateWithoutCategoryInput, RecipeCategoryUncheckedCreateWithoutCategoryInput> | RecipeCategoryCreateWithoutCategoryInput[] | RecipeCategoryUncheckedCreateWithoutCategoryInput[]
    connectOrCreate?: RecipeCategoryCreateOrConnectWithoutCategoryInput | RecipeCategoryCreateOrConnectWithoutCategoryInput[]
    upsert?: RecipeCategoryUpsertWithWhereUniqueWithoutCategoryInput | RecipeCategoryUpsertWithWhereUniqueWithoutCategoryInput[]
    createMany?: RecipeCategoryCreateManyCategoryInputEnvelope
    set?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    disconnect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    delete?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    connect?: RecipeCategoryWhereUniqueInput | RecipeCategoryWhereUniqueInput[]
    update?: RecipeCategoryUpdateWithWhereUniqueWithoutCategoryInput | RecipeCategoryUpdateWithWhereUniqueWithoutCategoryInput[]
    updateMany?: RecipeCategoryUpdateManyWithWhereWithoutCategoryInput | RecipeCategoryUpdateManyWithWhereWithoutCategoryInput[]
    deleteMany?: RecipeCategoryScalarWhereInput | RecipeCategoryScalarWhereInput[]
  }

  export type RecipeCreateNestedOneWithoutCategoriesInput = {
    create?: XOR<RecipeCreateWithoutCategoriesInput, RecipeUncheckedCreateWithoutCategoriesInput>
    connectOrCreate?: RecipeCreateOrConnectWithoutCategoriesInput
    connect?: RecipeWhereUniqueInput
  }

  export type CategoryCreateNestedOneWithoutRecipesInput = {
    create?: XOR<CategoryCreateWithoutRecipesInput, CategoryUncheckedCreateWithoutRecipesInput>
    connectOrCreate?: CategoryCreateOrConnectWithoutRecipesInput
    connect?: CategoryWhereUniqueInput
  }

  export type RecipeUpdateOneRequiredWithoutCategoriesNestedInput = {
    create?: XOR<RecipeCreateWithoutCategoriesInput, RecipeUncheckedCreateWithoutCategoriesInput>
    connectOrCreate?: RecipeCreateOrConnectWithoutCategoriesInput
    upsert?: RecipeUpsertWithoutCategoriesInput
    connect?: RecipeWhereUniqueInput
    update?: XOR<XOR<RecipeUpdateToOneWithWhereWithoutCategoriesInput, RecipeUpdateWithoutCategoriesInput>, RecipeUncheckedUpdateWithoutCategoriesInput>
  }

  export type CategoryUpdateOneRequiredWithoutRecipesNestedInput = {
    create?: XOR<CategoryCreateWithoutRecipesInput, CategoryUncheckedCreateWithoutRecipesInput>
    connectOrCreate?: CategoryCreateOrConnectWithoutRecipesInput
    upsert?: CategoryUpsertWithoutRecipesInput
    connect?: CategoryWhereUniqueInput
    update?: XOR<XOR<CategoryUpdateToOneWithWhereWithoutRecipesInput, CategoryUpdateWithoutRecipesInput>, CategoryUncheckedUpdateWithoutRecipesInput>
  }

  export type UserCreateNestedOneWithoutCommentsInput = {
    create?: XOR<UserCreateWithoutCommentsInput, UserUncheckedCreateWithoutCommentsInput>
    connectOrCreate?: UserCreateOrConnectWithoutCommentsInput
    connect?: UserWhereUniqueInput
  }

  export type RecipeCreateNestedOneWithoutCommentsInput = {
    create?: XOR<RecipeCreateWithoutCommentsInput, RecipeUncheckedCreateWithoutCommentsInput>
    connectOrCreate?: RecipeCreateOrConnectWithoutCommentsInput
    connect?: RecipeWhereUniqueInput
  }

  export type UserUpdateOneRequiredWithoutCommentsNestedInput = {
    create?: XOR<UserCreateWithoutCommentsInput, UserUncheckedCreateWithoutCommentsInput>
    connectOrCreate?: UserCreateOrConnectWithoutCommentsInput
    upsert?: UserUpsertWithoutCommentsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutCommentsInput, UserUpdateWithoutCommentsInput>, UserUncheckedUpdateWithoutCommentsInput>
  }

  export type RecipeUpdateOneRequiredWithoutCommentsNestedInput = {
    create?: XOR<RecipeCreateWithoutCommentsInput, RecipeUncheckedCreateWithoutCommentsInput>
    connectOrCreate?: RecipeCreateOrConnectWithoutCommentsInput
    upsert?: RecipeUpsertWithoutCommentsInput
    connect?: RecipeWhereUniqueInput
    update?: XOR<XOR<RecipeUpdateToOneWithWhereWithoutCommentsInput, RecipeUpdateWithoutCommentsInput>, RecipeUncheckedUpdateWithoutCommentsInput>
  }

  export type UserCreateNestedOneWithoutFavoritesInput = {
    create?: XOR<UserCreateWithoutFavoritesInput, UserUncheckedCreateWithoutFavoritesInput>
    connectOrCreate?: UserCreateOrConnectWithoutFavoritesInput
    connect?: UserWhereUniqueInput
  }

  export type RecipeCreateNestedOneWithoutFavoritesInput = {
    create?: XOR<RecipeCreateWithoutFavoritesInput, RecipeUncheckedCreateWithoutFavoritesInput>
    connectOrCreate?: RecipeCreateOrConnectWithoutFavoritesInput
    connect?: RecipeWhereUniqueInput
  }

  export type UserUpdateOneRequiredWithoutFavoritesNestedInput = {
    create?: XOR<UserCreateWithoutFavoritesInput, UserUncheckedCreateWithoutFavoritesInput>
    connectOrCreate?: UserCreateOrConnectWithoutFavoritesInput
    upsert?: UserUpsertWithoutFavoritesInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutFavoritesInput, UserUpdateWithoutFavoritesInput>, UserUncheckedUpdateWithoutFavoritesInput>
  }

  export type RecipeUpdateOneRequiredWithoutFavoritesNestedInput = {
    create?: XOR<RecipeCreateWithoutFavoritesInput, RecipeUncheckedCreateWithoutFavoritesInput>
    connectOrCreate?: RecipeCreateOrConnectWithoutFavoritesInput
    upsert?: RecipeUpsertWithoutFavoritesInput
    connect?: RecipeWhereUniqueInput
    update?: XOR<XOR<RecipeUpdateToOneWithWhereWithoutFavoritesInput, RecipeUpdateWithoutFavoritesInput>, RecipeUncheckedUpdateWithoutFavoritesInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedEnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedEnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedEnumApplicationStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.ApplicationStatus | EnumApplicationStatusFieldRefInput<$PrismaModel>
    in?: $Enums.ApplicationStatus[] | ListEnumApplicationStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.ApplicationStatus[] | ListEnumApplicationStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumApplicationStatusFilter<$PrismaModel> | $Enums.ApplicationStatus
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedEnumApplicationStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.ApplicationStatus | EnumApplicationStatusFieldRefInput<$PrismaModel>
    in?: $Enums.ApplicationStatus[] | ListEnumApplicationStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.ApplicationStatus[] | ListEnumApplicationStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumApplicationStatusWithAggregatesFilter<$PrismaModel> | $Enums.ApplicationStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumApplicationStatusFilter<$PrismaModel>
    _max?: NestedEnumApplicationStatusFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type RecipeCreateWithoutAuthorInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    categories?: RecipeCategoryCreateNestedManyWithoutRecipeInput
    comments?: CommentCreateNestedManyWithoutRecipeInput
    favorites?: UserFavoriteCreateNestedManyWithoutRecipeInput
  }

  export type RecipeUncheckedCreateWithoutAuthorInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    categories?: RecipeCategoryUncheckedCreateNestedManyWithoutRecipeInput
    comments?: CommentUncheckedCreateNestedManyWithoutRecipeInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutRecipeInput
  }

  export type RecipeCreateOrConnectWithoutAuthorInput = {
    where: RecipeWhereUniqueInput
    create: XOR<RecipeCreateWithoutAuthorInput, RecipeUncheckedCreateWithoutAuthorInput>
  }

  export type RecipeCreateManyAuthorInputEnvelope = {
    data: RecipeCreateManyAuthorInput | RecipeCreateManyAuthorInput[]
    skipDuplicates?: boolean
  }

  export type CommentCreateWithoutUserInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    recipe: RecipeCreateNestedOneWithoutCommentsInput
  }

  export type CommentUncheckedCreateWithoutUserInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    recipeId: string
  }

  export type CommentCreateOrConnectWithoutUserInput = {
    where: CommentWhereUniqueInput
    create: XOR<CommentCreateWithoutUserInput, CommentUncheckedCreateWithoutUserInput>
  }

  export type CommentCreateManyUserInputEnvelope = {
    data: CommentCreateManyUserInput | CommentCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type UserFavoriteCreateWithoutUserInput = {
    createdAt?: Date | string
    recipe: RecipeCreateNestedOneWithoutFavoritesInput
  }

  export type UserFavoriteUncheckedCreateWithoutUserInput = {
    recipeId: string
    createdAt?: Date | string
  }

  export type UserFavoriteCreateOrConnectWithoutUserInput = {
    where: UserFavoriteWhereUniqueInput
    create: XOR<UserFavoriteCreateWithoutUserInput, UserFavoriteUncheckedCreateWithoutUserInput>
  }

  export type UserFavoriteCreateManyUserInputEnvelope = {
    data: UserFavoriteCreateManyUserInput | UserFavoriteCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type InheritorApplicationCreateWithoutUserInput = {
    id?: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications?: string | null
    introduction: string
    status?: $Enums.ApplicationStatus
    appliedAt?: Date | string
    reviewedAt?: Date | string | null
    reviewComment?: string | null
    reviewer?: UserCreateNestedOneWithoutReviewedApplicationsInput
  }

  export type InheritorApplicationUncheckedCreateWithoutUserInput = {
    id?: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications?: string | null
    introduction: string
    status?: $Enums.ApplicationStatus
    appliedAt?: Date | string
    reviewedAt?: Date | string | null
    reviewedBy?: string | null
    reviewComment?: string | null
  }

  export type InheritorApplicationCreateOrConnectWithoutUserInput = {
    where: InheritorApplicationWhereUniqueInput
    create: XOR<InheritorApplicationCreateWithoutUserInput, InheritorApplicationUncheckedCreateWithoutUserInput>
  }

  export type InheritorApplicationCreateWithoutReviewerInput = {
    id?: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications?: string | null
    introduction: string
    status?: $Enums.ApplicationStatus
    appliedAt?: Date | string
    reviewedAt?: Date | string | null
    reviewComment?: string | null
    user: UserCreateNestedOneWithoutInheritorApplicationInput
  }

  export type InheritorApplicationUncheckedCreateWithoutReviewerInput = {
    id?: string
    userId: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications?: string | null
    introduction: string
    status?: $Enums.ApplicationStatus
    appliedAt?: Date | string
    reviewedAt?: Date | string | null
    reviewComment?: string | null
  }

  export type InheritorApplicationCreateOrConnectWithoutReviewerInput = {
    where: InheritorApplicationWhereUniqueInput
    create: XOR<InheritorApplicationCreateWithoutReviewerInput, InheritorApplicationUncheckedCreateWithoutReviewerInput>
  }

  export type InheritorApplicationCreateManyReviewerInputEnvelope = {
    data: InheritorApplicationCreateManyReviewerInput | InheritorApplicationCreateManyReviewerInput[]
    skipDuplicates?: boolean
  }

  export type RecipeUpsertWithWhereUniqueWithoutAuthorInput = {
    where: RecipeWhereUniqueInput
    update: XOR<RecipeUpdateWithoutAuthorInput, RecipeUncheckedUpdateWithoutAuthorInput>
    create: XOR<RecipeCreateWithoutAuthorInput, RecipeUncheckedCreateWithoutAuthorInput>
  }

  export type RecipeUpdateWithWhereUniqueWithoutAuthorInput = {
    where: RecipeWhereUniqueInput
    data: XOR<RecipeUpdateWithoutAuthorInput, RecipeUncheckedUpdateWithoutAuthorInput>
  }

  export type RecipeUpdateManyWithWhereWithoutAuthorInput = {
    where: RecipeScalarWhereInput
    data: XOR<RecipeUpdateManyMutationInput, RecipeUncheckedUpdateManyWithoutAuthorInput>
  }

  export type RecipeScalarWhereInput = {
    AND?: RecipeScalarWhereInput | RecipeScalarWhereInput[]
    OR?: RecipeScalarWhereInput[]
    NOT?: RecipeScalarWhereInput | RecipeScalarWhereInput[]
    id?: StringFilter<"Recipe"> | string
    name?: StringFilter<"Recipe"> | string
    dialectName?: StringFilter<"Recipe"> | string
    dialectAudio?: StringNullableFilter<"Recipe"> | string | null
    region?: StringFilter<"Recipe"> | string
    ingredients?: StringFilter<"Recipe"> | string
    steps?: StringFilter<"Recipe"> | string
    image?: StringNullableFilter<"Recipe"> | string | null
    description?: StringNullableFilter<"Recipe"> | string | null
    difficulty?: IntFilter<"Recipe"> | number
    cookTime?: IntNullableFilter<"Recipe"> | number | null
    servings?: IntNullableFilter<"Recipe"> | number | null
    isPublished?: BoolFilter<"Recipe"> | boolean
    viewCount?: IntFilter<"Recipe"> | number
    culturalHistory?: StringNullableFilter<"Recipe"> | string | null
    regionalFeature?: StringNullableFilter<"Recipe"> | string | null
    culturalMeaning?: StringNullableFilter<"Recipe"> | string | null
    inheritanceStory?: StringNullableFilter<"Recipe"> | string | null
    createdAt?: DateTimeFilter<"Recipe"> | Date | string
    updatedAt?: DateTimeFilter<"Recipe"> | Date | string
    authorId?: StringFilter<"Recipe"> | string
  }

  export type CommentUpsertWithWhereUniqueWithoutUserInput = {
    where: CommentWhereUniqueInput
    update: XOR<CommentUpdateWithoutUserInput, CommentUncheckedUpdateWithoutUserInput>
    create: XOR<CommentCreateWithoutUserInput, CommentUncheckedCreateWithoutUserInput>
  }

  export type CommentUpdateWithWhereUniqueWithoutUserInput = {
    where: CommentWhereUniqueInput
    data: XOR<CommentUpdateWithoutUserInput, CommentUncheckedUpdateWithoutUserInput>
  }

  export type CommentUpdateManyWithWhereWithoutUserInput = {
    where: CommentScalarWhereInput
    data: XOR<CommentUpdateManyMutationInput, CommentUncheckedUpdateManyWithoutUserInput>
  }

  export type CommentScalarWhereInput = {
    AND?: CommentScalarWhereInput | CommentScalarWhereInput[]
    OR?: CommentScalarWhereInput[]
    NOT?: CommentScalarWhereInput | CommentScalarWhereInput[]
    id?: StringFilter<"Comment"> | string
    content?: StringFilter<"Comment"> | string
    rating?: IntNullableFilter<"Comment"> | number | null
    createdAt?: DateTimeFilter<"Comment"> | Date | string
    userId?: StringFilter<"Comment"> | string
    recipeId?: StringFilter<"Comment"> | string
  }

  export type UserFavoriteUpsertWithWhereUniqueWithoutUserInput = {
    where: UserFavoriteWhereUniqueInput
    update: XOR<UserFavoriteUpdateWithoutUserInput, UserFavoriteUncheckedUpdateWithoutUserInput>
    create: XOR<UserFavoriteCreateWithoutUserInput, UserFavoriteUncheckedCreateWithoutUserInput>
  }

  export type UserFavoriteUpdateWithWhereUniqueWithoutUserInput = {
    where: UserFavoriteWhereUniqueInput
    data: XOR<UserFavoriteUpdateWithoutUserInput, UserFavoriteUncheckedUpdateWithoutUserInput>
  }

  export type UserFavoriteUpdateManyWithWhereWithoutUserInput = {
    where: UserFavoriteScalarWhereInput
    data: XOR<UserFavoriteUpdateManyMutationInput, UserFavoriteUncheckedUpdateManyWithoutUserInput>
  }

  export type UserFavoriteScalarWhereInput = {
    AND?: UserFavoriteScalarWhereInput | UserFavoriteScalarWhereInput[]
    OR?: UserFavoriteScalarWhereInput[]
    NOT?: UserFavoriteScalarWhereInput | UserFavoriteScalarWhereInput[]
    userId?: StringFilter<"UserFavorite"> | string
    recipeId?: StringFilter<"UserFavorite"> | string
    createdAt?: DateTimeFilter<"UserFavorite"> | Date | string
  }

  export type InheritorApplicationUpsertWithoutUserInput = {
    update: XOR<InheritorApplicationUpdateWithoutUserInput, InheritorApplicationUncheckedUpdateWithoutUserInput>
    create: XOR<InheritorApplicationCreateWithoutUserInput, InheritorApplicationUncheckedCreateWithoutUserInput>
    where?: InheritorApplicationWhereInput
  }

  export type InheritorApplicationUpdateToOneWithWhereWithoutUserInput = {
    where?: InheritorApplicationWhereInput
    data: XOR<InheritorApplicationUpdateWithoutUserInput, InheritorApplicationUncheckedUpdateWithoutUserInput>
  }

  export type InheritorApplicationUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
    reviewer?: UserUpdateOneWithoutReviewedApplicationsNestedInput
  }

  export type InheritorApplicationUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewedBy?: NullableStringFieldUpdateOperationsInput | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type InheritorApplicationUpsertWithWhereUniqueWithoutReviewerInput = {
    where: InheritorApplicationWhereUniqueInput
    update: XOR<InheritorApplicationUpdateWithoutReviewerInput, InheritorApplicationUncheckedUpdateWithoutReviewerInput>
    create: XOR<InheritorApplicationCreateWithoutReviewerInput, InheritorApplicationUncheckedCreateWithoutReviewerInput>
  }

  export type InheritorApplicationUpdateWithWhereUniqueWithoutReviewerInput = {
    where: InheritorApplicationWhereUniqueInput
    data: XOR<InheritorApplicationUpdateWithoutReviewerInput, InheritorApplicationUncheckedUpdateWithoutReviewerInput>
  }

  export type InheritorApplicationUpdateManyWithWhereWithoutReviewerInput = {
    where: InheritorApplicationScalarWhereInput
    data: XOR<InheritorApplicationUpdateManyMutationInput, InheritorApplicationUncheckedUpdateManyWithoutReviewerInput>
  }

  export type InheritorApplicationScalarWhereInput = {
    AND?: InheritorApplicationScalarWhereInput | InheritorApplicationScalarWhereInput[]
    OR?: InheritorApplicationScalarWhereInput[]
    NOT?: InheritorApplicationScalarWhereInput | InheritorApplicationScalarWhereInput[]
    id?: StringFilter<"InheritorApplication"> | string
    userId?: StringFilter<"InheritorApplication"> | string
    realName?: StringFilter<"InheritorApplication"> | string
    phone?: StringFilter<"InheritorApplication"> | string
    region?: StringFilter<"InheritorApplication"> | string
    specialties?: StringFilter<"InheritorApplication"> | string
    experience?: StringFilter<"InheritorApplication"> | string
    certifications?: StringNullableFilter<"InheritorApplication"> | string | null
    introduction?: StringFilter<"InheritorApplication"> | string
    status?: EnumApplicationStatusFilter<"InheritorApplication"> | $Enums.ApplicationStatus
    appliedAt?: DateTimeFilter<"InheritorApplication"> | Date | string
    reviewedAt?: DateTimeNullableFilter<"InheritorApplication"> | Date | string | null
    reviewedBy?: StringNullableFilter<"InheritorApplication"> | string | null
    reviewComment?: StringNullableFilter<"InheritorApplication"> | string | null
  }

  export type UserCreateWithoutInheritorApplicationInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeCreateNestedManyWithoutAuthorInput
    comments?: CommentCreateNestedManyWithoutUserInput
    favorites?: UserFavoriteCreateNestedManyWithoutUserInput
    reviewedApplications?: InheritorApplicationCreateNestedManyWithoutReviewerInput
  }

  export type UserUncheckedCreateWithoutInheritorApplicationInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeUncheckedCreateNestedManyWithoutAuthorInput
    comments?: CommentUncheckedCreateNestedManyWithoutUserInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutUserInput
    reviewedApplications?: InheritorApplicationUncheckedCreateNestedManyWithoutReviewerInput
  }

  export type UserCreateOrConnectWithoutInheritorApplicationInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutInheritorApplicationInput, UserUncheckedCreateWithoutInheritorApplicationInput>
  }

  export type UserCreateWithoutReviewedApplicationsInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeCreateNestedManyWithoutAuthorInput
    comments?: CommentCreateNestedManyWithoutUserInput
    favorites?: UserFavoriteCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationCreateNestedOneWithoutUserInput
  }

  export type UserUncheckedCreateWithoutReviewedApplicationsInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeUncheckedCreateNestedManyWithoutAuthorInput
    comments?: CommentUncheckedCreateNestedManyWithoutUserInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationUncheckedCreateNestedOneWithoutUserInput
  }

  export type UserCreateOrConnectWithoutReviewedApplicationsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutReviewedApplicationsInput, UserUncheckedCreateWithoutReviewedApplicationsInput>
  }

  export type UserUpsertWithoutInheritorApplicationInput = {
    update: XOR<UserUpdateWithoutInheritorApplicationInput, UserUncheckedUpdateWithoutInheritorApplicationInput>
    create: XOR<UserCreateWithoutInheritorApplicationInput, UserUncheckedCreateWithoutInheritorApplicationInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutInheritorApplicationInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutInheritorApplicationInput, UserUncheckedUpdateWithoutInheritorApplicationInput>
  }

  export type UserUpdateWithoutInheritorApplicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUpdateManyWithoutAuthorNestedInput
    comments?: CommentUpdateManyWithoutUserNestedInput
    favorites?: UserFavoriteUpdateManyWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUpdateManyWithoutReviewerNestedInput
  }

  export type UserUncheckedUpdateWithoutInheritorApplicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUncheckedUpdateManyWithoutAuthorNestedInput
    comments?: CommentUncheckedUpdateManyWithoutUserNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUncheckedUpdateManyWithoutReviewerNestedInput
  }

  export type UserUpsertWithoutReviewedApplicationsInput = {
    update: XOR<UserUpdateWithoutReviewedApplicationsInput, UserUncheckedUpdateWithoutReviewedApplicationsInput>
    create: XOR<UserCreateWithoutReviewedApplicationsInput, UserUncheckedCreateWithoutReviewedApplicationsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutReviewedApplicationsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutReviewedApplicationsInput, UserUncheckedUpdateWithoutReviewedApplicationsInput>
  }

  export type UserUpdateWithoutReviewedApplicationsInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUpdateManyWithoutAuthorNestedInput
    comments?: CommentUpdateManyWithoutUserNestedInput
    favorites?: UserFavoriteUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUpdateOneWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutReviewedApplicationsInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUncheckedUpdateManyWithoutAuthorNestedInput
    comments?: CommentUncheckedUpdateManyWithoutUserNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUncheckedUpdateOneWithoutUserNestedInput
  }

  export type UserCreateWithoutRecipesInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    comments?: CommentCreateNestedManyWithoutUserInput
    favorites?: UserFavoriteCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationCreateNestedOneWithoutUserInput
    reviewedApplications?: InheritorApplicationCreateNestedManyWithoutReviewerInput
  }

  export type UserUncheckedCreateWithoutRecipesInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    comments?: CommentUncheckedCreateNestedManyWithoutUserInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationUncheckedCreateNestedOneWithoutUserInput
    reviewedApplications?: InheritorApplicationUncheckedCreateNestedManyWithoutReviewerInput
  }

  export type UserCreateOrConnectWithoutRecipesInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutRecipesInput, UserUncheckedCreateWithoutRecipesInput>
  }

  export type RecipeCategoryCreateWithoutRecipeInput = {
    category: CategoryCreateNestedOneWithoutRecipesInput
  }

  export type RecipeCategoryUncheckedCreateWithoutRecipeInput = {
    categoryId: string
  }

  export type RecipeCategoryCreateOrConnectWithoutRecipeInput = {
    where: RecipeCategoryWhereUniqueInput
    create: XOR<RecipeCategoryCreateWithoutRecipeInput, RecipeCategoryUncheckedCreateWithoutRecipeInput>
  }

  export type RecipeCategoryCreateManyRecipeInputEnvelope = {
    data: RecipeCategoryCreateManyRecipeInput | RecipeCategoryCreateManyRecipeInput[]
    skipDuplicates?: boolean
  }

  export type CommentCreateWithoutRecipeInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutCommentsInput
  }

  export type CommentUncheckedCreateWithoutRecipeInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    userId: string
  }

  export type CommentCreateOrConnectWithoutRecipeInput = {
    where: CommentWhereUniqueInput
    create: XOR<CommentCreateWithoutRecipeInput, CommentUncheckedCreateWithoutRecipeInput>
  }

  export type CommentCreateManyRecipeInputEnvelope = {
    data: CommentCreateManyRecipeInput | CommentCreateManyRecipeInput[]
    skipDuplicates?: boolean
  }

  export type UserFavoriteCreateWithoutRecipeInput = {
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutFavoritesInput
  }

  export type UserFavoriteUncheckedCreateWithoutRecipeInput = {
    userId: string
    createdAt?: Date | string
  }

  export type UserFavoriteCreateOrConnectWithoutRecipeInput = {
    where: UserFavoriteWhereUniqueInput
    create: XOR<UserFavoriteCreateWithoutRecipeInput, UserFavoriteUncheckedCreateWithoutRecipeInput>
  }

  export type UserFavoriteCreateManyRecipeInputEnvelope = {
    data: UserFavoriteCreateManyRecipeInput | UserFavoriteCreateManyRecipeInput[]
    skipDuplicates?: boolean
  }

  export type UserUpsertWithoutRecipesInput = {
    update: XOR<UserUpdateWithoutRecipesInput, UserUncheckedUpdateWithoutRecipesInput>
    create: XOR<UserCreateWithoutRecipesInput, UserUncheckedCreateWithoutRecipesInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutRecipesInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutRecipesInput, UserUncheckedUpdateWithoutRecipesInput>
  }

  export type UserUpdateWithoutRecipesInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    comments?: CommentUpdateManyWithoutUserNestedInput
    favorites?: UserFavoriteUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUpdateOneWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUpdateManyWithoutReviewerNestedInput
  }

  export type UserUncheckedUpdateWithoutRecipesInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    comments?: CommentUncheckedUpdateManyWithoutUserNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUncheckedUpdateOneWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUncheckedUpdateManyWithoutReviewerNestedInput
  }

  export type RecipeCategoryUpsertWithWhereUniqueWithoutRecipeInput = {
    where: RecipeCategoryWhereUniqueInput
    update: XOR<RecipeCategoryUpdateWithoutRecipeInput, RecipeCategoryUncheckedUpdateWithoutRecipeInput>
    create: XOR<RecipeCategoryCreateWithoutRecipeInput, RecipeCategoryUncheckedCreateWithoutRecipeInput>
  }

  export type RecipeCategoryUpdateWithWhereUniqueWithoutRecipeInput = {
    where: RecipeCategoryWhereUniqueInput
    data: XOR<RecipeCategoryUpdateWithoutRecipeInput, RecipeCategoryUncheckedUpdateWithoutRecipeInput>
  }

  export type RecipeCategoryUpdateManyWithWhereWithoutRecipeInput = {
    where: RecipeCategoryScalarWhereInput
    data: XOR<RecipeCategoryUpdateManyMutationInput, RecipeCategoryUncheckedUpdateManyWithoutRecipeInput>
  }

  export type RecipeCategoryScalarWhereInput = {
    AND?: RecipeCategoryScalarWhereInput | RecipeCategoryScalarWhereInput[]
    OR?: RecipeCategoryScalarWhereInput[]
    NOT?: RecipeCategoryScalarWhereInput | RecipeCategoryScalarWhereInput[]
    recipeId?: StringFilter<"RecipeCategory"> | string
    categoryId?: StringFilter<"RecipeCategory"> | string
  }

  export type CommentUpsertWithWhereUniqueWithoutRecipeInput = {
    where: CommentWhereUniqueInput
    update: XOR<CommentUpdateWithoutRecipeInput, CommentUncheckedUpdateWithoutRecipeInput>
    create: XOR<CommentCreateWithoutRecipeInput, CommentUncheckedCreateWithoutRecipeInput>
  }

  export type CommentUpdateWithWhereUniqueWithoutRecipeInput = {
    where: CommentWhereUniqueInput
    data: XOR<CommentUpdateWithoutRecipeInput, CommentUncheckedUpdateWithoutRecipeInput>
  }

  export type CommentUpdateManyWithWhereWithoutRecipeInput = {
    where: CommentScalarWhereInput
    data: XOR<CommentUpdateManyMutationInput, CommentUncheckedUpdateManyWithoutRecipeInput>
  }

  export type UserFavoriteUpsertWithWhereUniqueWithoutRecipeInput = {
    where: UserFavoriteWhereUniqueInput
    update: XOR<UserFavoriteUpdateWithoutRecipeInput, UserFavoriteUncheckedUpdateWithoutRecipeInput>
    create: XOR<UserFavoriteCreateWithoutRecipeInput, UserFavoriteUncheckedCreateWithoutRecipeInput>
  }

  export type UserFavoriteUpdateWithWhereUniqueWithoutRecipeInput = {
    where: UserFavoriteWhereUniqueInput
    data: XOR<UserFavoriteUpdateWithoutRecipeInput, UserFavoriteUncheckedUpdateWithoutRecipeInput>
  }

  export type UserFavoriteUpdateManyWithWhereWithoutRecipeInput = {
    where: UserFavoriteScalarWhereInput
    data: XOR<UserFavoriteUpdateManyMutationInput, UserFavoriteUncheckedUpdateManyWithoutRecipeInput>
  }

  export type RecipeCategoryCreateWithoutCategoryInput = {
    recipe: RecipeCreateNestedOneWithoutCategoriesInput
  }

  export type RecipeCategoryUncheckedCreateWithoutCategoryInput = {
    recipeId: string
  }

  export type RecipeCategoryCreateOrConnectWithoutCategoryInput = {
    where: RecipeCategoryWhereUniqueInput
    create: XOR<RecipeCategoryCreateWithoutCategoryInput, RecipeCategoryUncheckedCreateWithoutCategoryInput>
  }

  export type RecipeCategoryCreateManyCategoryInputEnvelope = {
    data: RecipeCategoryCreateManyCategoryInput | RecipeCategoryCreateManyCategoryInput[]
    skipDuplicates?: boolean
  }

  export type RecipeCategoryUpsertWithWhereUniqueWithoutCategoryInput = {
    where: RecipeCategoryWhereUniqueInput
    update: XOR<RecipeCategoryUpdateWithoutCategoryInput, RecipeCategoryUncheckedUpdateWithoutCategoryInput>
    create: XOR<RecipeCategoryCreateWithoutCategoryInput, RecipeCategoryUncheckedCreateWithoutCategoryInput>
  }

  export type RecipeCategoryUpdateWithWhereUniqueWithoutCategoryInput = {
    where: RecipeCategoryWhereUniqueInput
    data: XOR<RecipeCategoryUpdateWithoutCategoryInput, RecipeCategoryUncheckedUpdateWithoutCategoryInput>
  }

  export type RecipeCategoryUpdateManyWithWhereWithoutCategoryInput = {
    where: RecipeCategoryScalarWhereInput
    data: XOR<RecipeCategoryUpdateManyMutationInput, RecipeCategoryUncheckedUpdateManyWithoutCategoryInput>
  }

  export type RecipeCreateWithoutCategoriesInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    author: UserCreateNestedOneWithoutRecipesInput
    comments?: CommentCreateNestedManyWithoutRecipeInput
    favorites?: UserFavoriteCreateNestedManyWithoutRecipeInput
  }

  export type RecipeUncheckedCreateWithoutCategoriesInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    authorId: string
    comments?: CommentUncheckedCreateNestedManyWithoutRecipeInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutRecipeInput
  }

  export type RecipeCreateOrConnectWithoutCategoriesInput = {
    where: RecipeWhereUniqueInput
    create: XOR<RecipeCreateWithoutCategoriesInput, RecipeUncheckedCreateWithoutCategoriesInput>
  }

  export type CategoryCreateWithoutRecipesInput = {
    id?: string
    name: string
    description?: string | null
    icon?: string | null
    createdAt?: Date | string
  }

  export type CategoryUncheckedCreateWithoutRecipesInput = {
    id?: string
    name: string
    description?: string | null
    icon?: string | null
    createdAt?: Date | string
  }

  export type CategoryCreateOrConnectWithoutRecipesInput = {
    where: CategoryWhereUniqueInput
    create: XOR<CategoryCreateWithoutRecipesInput, CategoryUncheckedCreateWithoutRecipesInput>
  }

  export type RecipeUpsertWithoutCategoriesInput = {
    update: XOR<RecipeUpdateWithoutCategoriesInput, RecipeUncheckedUpdateWithoutCategoriesInput>
    create: XOR<RecipeCreateWithoutCategoriesInput, RecipeUncheckedCreateWithoutCategoriesInput>
    where?: RecipeWhereInput
  }

  export type RecipeUpdateToOneWithWhereWithoutCategoriesInput = {
    where?: RecipeWhereInput
    data: XOR<RecipeUpdateWithoutCategoriesInput, RecipeUncheckedUpdateWithoutCategoriesInput>
  }

  export type RecipeUpdateWithoutCategoriesInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    author?: UserUpdateOneRequiredWithoutRecipesNestedInput
    comments?: CommentUpdateManyWithoutRecipeNestedInput
    favorites?: UserFavoriteUpdateManyWithoutRecipeNestedInput
  }

  export type RecipeUncheckedUpdateWithoutCategoriesInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    authorId?: StringFieldUpdateOperationsInput | string
    comments?: CommentUncheckedUpdateManyWithoutRecipeNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutRecipeNestedInput
  }

  export type CategoryUpsertWithoutRecipesInput = {
    update: XOR<CategoryUpdateWithoutRecipesInput, CategoryUncheckedUpdateWithoutRecipesInput>
    create: XOR<CategoryCreateWithoutRecipesInput, CategoryUncheckedCreateWithoutRecipesInput>
    where?: CategoryWhereInput
  }

  export type CategoryUpdateToOneWithWhereWithoutRecipesInput = {
    where?: CategoryWhereInput
    data: XOR<CategoryUpdateWithoutRecipesInput, CategoryUncheckedUpdateWithoutRecipesInput>
  }

  export type CategoryUpdateWithoutRecipesInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    icon?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CategoryUncheckedUpdateWithoutRecipesInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    icon?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserCreateWithoutCommentsInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeCreateNestedManyWithoutAuthorInput
    favorites?: UserFavoriteCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationCreateNestedOneWithoutUserInput
    reviewedApplications?: InheritorApplicationCreateNestedManyWithoutReviewerInput
  }

  export type UserUncheckedCreateWithoutCommentsInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeUncheckedCreateNestedManyWithoutAuthorInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationUncheckedCreateNestedOneWithoutUserInput
    reviewedApplications?: InheritorApplicationUncheckedCreateNestedManyWithoutReviewerInput
  }

  export type UserCreateOrConnectWithoutCommentsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutCommentsInput, UserUncheckedCreateWithoutCommentsInput>
  }

  export type RecipeCreateWithoutCommentsInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    author: UserCreateNestedOneWithoutRecipesInput
    categories?: RecipeCategoryCreateNestedManyWithoutRecipeInput
    favorites?: UserFavoriteCreateNestedManyWithoutRecipeInput
  }

  export type RecipeUncheckedCreateWithoutCommentsInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    authorId: string
    categories?: RecipeCategoryUncheckedCreateNestedManyWithoutRecipeInput
    favorites?: UserFavoriteUncheckedCreateNestedManyWithoutRecipeInput
  }

  export type RecipeCreateOrConnectWithoutCommentsInput = {
    where: RecipeWhereUniqueInput
    create: XOR<RecipeCreateWithoutCommentsInput, RecipeUncheckedCreateWithoutCommentsInput>
  }

  export type UserUpsertWithoutCommentsInput = {
    update: XOR<UserUpdateWithoutCommentsInput, UserUncheckedUpdateWithoutCommentsInput>
    create: XOR<UserCreateWithoutCommentsInput, UserUncheckedCreateWithoutCommentsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutCommentsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutCommentsInput, UserUncheckedUpdateWithoutCommentsInput>
  }

  export type UserUpdateWithoutCommentsInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUpdateManyWithoutAuthorNestedInput
    favorites?: UserFavoriteUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUpdateOneWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUpdateManyWithoutReviewerNestedInput
  }

  export type UserUncheckedUpdateWithoutCommentsInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUncheckedUpdateManyWithoutAuthorNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUncheckedUpdateOneWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUncheckedUpdateManyWithoutReviewerNestedInput
  }

  export type RecipeUpsertWithoutCommentsInput = {
    update: XOR<RecipeUpdateWithoutCommentsInput, RecipeUncheckedUpdateWithoutCommentsInput>
    create: XOR<RecipeCreateWithoutCommentsInput, RecipeUncheckedCreateWithoutCommentsInput>
    where?: RecipeWhereInput
  }

  export type RecipeUpdateToOneWithWhereWithoutCommentsInput = {
    where?: RecipeWhereInput
    data: XOR<RecipeUpdateWithoutCommentsInput, RecipeUncheckedUpdateWithoutCommentsInput>
  }

  export type RecipeUpdateWithoutCommentsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    author?: UserUpdateOneRequiredWithoutRecipesNestedInput
    categories?: RecipeCategoryUpdateManyWithoutRecipeNestedInput
    favorites?: UserFavoriteUpdateManyWithoutRecipeNestedInput
  }

  export type RecipeUncheckedUpdateWithoutCommentsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    authorId?: StringFieldUpdateOperationsInput | string
    categories?: RecipeCategoryUncheckedUpdateManyWithoutRecipeNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutRecipeNestedInput
  }

  export type UserCreateWithoutFavoritesInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeCreateNestedManyWithoutAuthorInput
    comments?: CommentCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationCreateNestedOneWithoutUserInput
    reviewedApplications?: InheritorApplicationCreateNestedManyWithoutReviewerInput
  }

  export type UserUncheckedCreateWithoutFavoritesInput = {
    id?: string
    username: string
    email?: string | null
    password: string
    role?: $Enums.UserRole
    avatar?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    recipes?: RecipeUncheckedCreateNestedManyWithoutAuthorInput
    comments?: CommentUncheckedCreateNestedManyWithoutUserInput
    inheritorApplication?: InheritorApplicationUncheckedCreateNestedOneWithoutUserInput
    reviewedApplications?: InheritorApplicationUncheckedCreateNestedManyWithoutReviewerInput
  }

  export type UserCreateOrConnectWithoutFavoritesInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutFavoritesInput, UserUncheckedCreateWithoutFavoritesInput>
  }

  export type RecipeCreateWithoutFavoritesInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    author: UserCreateNestedOneWithoutRecipesInput
    categories?: RecipeCategoryCreateNestedManyWithoutRecipeInput
    comments?: CommentCreateNestedManyWithoutRecipeInput
  }

  export type RecipeUncheckedCreateWithoutFavoritesInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    authorId: string
    categories?: RecipeCategoryUncheckedCreateNestedManyWithoutRecipeInput
    comments?: CommentUncheckedCreateNestedManyWithoutRecipeInput
  }

  export type RecipeCreateOrConnectWithoutFavoritesInput = {
    where: RecipeWhereUniqueInput
    create: XOR<RecipeCreateWithoutFavoritesInput, RecipeUncheckedCreateWithoutFavoritesInput>
  }

  export type UserUpsertWithoutFavoritesInput = {
    update: XOR<UserUpdateWithoutFavoritesInput, UserUncheckedUpdateWithoutFavoritesInput>
    create: XOR<UserCreateWithoutFavoritesInput, UserUncheckedCreateWithoutFavoritesInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutFavoritesInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutFavoritesInput, UserUncheckedUpdateWithoutFavoritesInput>
  }

  export type UserUpdateWithoutFavoritesInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUpdateManyWithoutAuthorNestedInput
    comments?: CommentUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUpdateOneWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUpdateManyWithoutReviewerNestedInput
  }

  export type UserUncheckedUpdateWithoutFavoritesInput = {
    id?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipes?: RecipeUncheckedUpdateManyWithoutAuthorNestedInput
    comments?: CommentUncheckedUpdateManyWithoutUserNestedInput
    inheritorApplication?: InheritorApplicationUncheckedUpdateOneWithoutUserNestedInput
    reviewedApplications?: InheritorApplicationUncheckedUpdateManyWithoutReviewerNestedInput
  }

  export type RecipeUpsertWithoutFavoritesInput = {
    update: XOR<RecipeUpdateWithoutFavoritesInput, RecipeUncheckedUpdateWithoutFavoritesInput>
    create: XOR<RecipeCreateWithoutFavoritesInput, RecipeUncheckedCreateWithoutFavoritesInput>
    where?: RecipeWhereInput
  }

  export type RecipeUpdateToOneWithWhereWithoutFavoritesInput = {
    where?: RecipeWhereInput
    data: XOR<RecipeUpdateWithoutFavoritesInput, RecipeUncheckedUpdateWithoutFavoritesInput>
  }

  export type RecipeUpdateWithoutFavoritesInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    author?: UserUpdateOneRequiredWithoutRecipesNestedInput
    categories?: RecipeCategoryUpdateManyWithoutRecipeNestedInput
    comments?: CommentUpdateManyWithoutRecipeNestedInput
  }

  export type RecipeUncheckedUpdateWithoutFavoritesInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    authorId?: StringFieldUpdateOperationsInput | string
    categories?: RecipeCategoryUncheckedUpdateManyWithoutRecipeNestedInput
    comments?: CommentUncheckedUpdateManyWithoutRecipeNestedInput
  }

  export type RecipeCreateManyAuthorInput = {
    id?: string
    name: string
    dialectName: string
    dialectAudio?: string | null
    region: string
    ingredients: string
    steps: string
    image?: string | null
    description?: string | null
    difficulty?: number
    cookTime?: number | null
    servings?: number | null
    isPublished?: boolean
    viewCount?: number
    culturalHistory?: string | null
    regionalFeature?: string | null
    culturalMeaning?: string | null
    inheritanceStory?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type CommentCreateManyUserInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    recipeId: string
  }

  export type UserFavoriteCreateManyUserInput = {
    recipeId: string
    createdAt?: Date | string
  }

  export type InheritorApplicationCreateManyReviewerInput = {
    id?: string
    userId: string
    realName: string
    phone: string
    region: string
    specialties: string
    experience: string
    certifications?: string | null
    introduction: string
    status?: $Enums.ApplicationStatus
    appliedAt?: Date | string
    reviewedAt?: Date | string | null
    reviewComment?: string | null
  }

  export type RecipeUpdateWithoutAuthorInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    categories?: RecipeCategoryUpdateManyWithoutRecipeNestedInput
    comments?: CommentUpdateManyWithoutRecipeNestedInput
    favorites?: UserFavoriteUpdateManyWithoutRecipeNestedInput
  }

  export type RecipeUncheckedUpdateWithoutAuthorInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    categories?: RecipeCategoryUncheckedUpdateManyWithoutRecipeNestedInput
    comments?: CommentUncheckedUpdateManyWithoutRecipeNestedInput
    favorites?: UserFavoriteUncheckedUpdateManyWithoutRecipeNestedInput
  }

  export type RecipeUncheckedUpdateManyWithoutAuthorInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dialectName?: StringFieldUpdateOperationsInput | string
    dialectAudio?: NullableStringFieldUpdateOperationsInput | string | null
    region?: StringFieldUpdateOperationsInput | string
    ingredients?: StringFieldUpdateOperationsInput | string
    steps?: StringFieldUpdateOperationsInput | string
    image?: NullableStringFieldUpdateOperationsInput | string | null
    description?: NullableStringFieldUpdateOperationsInput | string | null
    difficulty?: IntFieldUpdateOperationsInput | number
    cookTime?: NullableIntFieldUpdateOperationsInput | number | null
    servings?: NullableIntFieldUpdateOperationsInput | number | null
    isPublished?: BoolFieldUpdateOperationsInput | boolean
    viewCount?: IntFieldUpdateOperationsInput | number
    culturalHistory?: NullableStringFieldUpdateOperationsInput | string | null
    regionalFeature?: NullableStringFieldUpdateOperationsInput | string | null
    culturalMeaning?: NullableStringFieldUpdateOperationsInput | string | null
    inheritanceStory?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CommentUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipe?: RecipeUpdateOneRequiredWithoutCommentsNestedInput
  }

  export type CommentUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipeId?: StringFieldUpdateOperationsInput | string
  }

  export type CommentUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipeId?: StringFieldUpdateOperationsInput | string
  }

  export type UserFavoriteUpdateWithoutUserInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    recipe?: RecipeUpdateOneRequiredWithoutFavoritesNestedInput
  }

  export type UserFavoriteUncheckedUpdateWithoutUserInput = {
    recipeId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFavoriteUncheckedUpdateManyWithoutUserInput = {
    recipeId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type InheritorApplicationUpdateWithoutReviewerInput = {
    id?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
    user?: UserUpdateOneRequiredWithoutInheritorApplicationNestedInput
  }

  export type InheritorApplicationUncheckedUpdateWithoutReviewerInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type InheritorApplicationUncheckedUpdateManyWithoutReviewerInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    realName?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    region?: StringFieldUpdateOperationsInput | string
    specialties?: StringFieldUpdateOperationsInput | string
    experience?: StringFieldUpdateOperationsInput | string
    certifications?: NullableStringFieldUpdateOperationsInput | string | null
    introduction?: StringFieldUpdateOperationsInput | string
    status?: EnumApplicationStatusFieldUpdateOperationsInput | $Enums.ApplicationStatus
    appliedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    reviewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reviewComment?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type RecipeCategoryCreateManyRecipeInput = {
    categoryId: string
  }

  export type CommentCreateManyRecipeInput = {
    id?: string
    content: string
    rating?: number | null
    createdAt?: Date | string
    userId: string
  }

  export type UserFavoriteCreateManyRecipeInput = {
    userId: string
    createdAt?: Date | string
  }

  export type RecipeCategoryUpdateWithoutRecipeInput = {
    category?: CategoryUpdateOneRequiredWithoutRecipesNestedInput
  }

  export type RecipeCategoryUncheckedUpdateWithoutRecipeInput = {
    categoryId?: StringFieldUpdateOperationsInput | string
  }

  export type RecipeCategoryUncheckedUpdateManyWithoutRecipeInput = {
    categoryId?: StringFieldUpdateOperationsInput | string
  }

  export type CommentUpdateWithoutRecipeInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutCommentsNestedInput
  }

  export type CommentUncheckedUpdateWithoutRecipeInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type CommentUncheckedUpdateManyWithoutRecipeInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    rating?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type UserFavoriteUpdateWithoutRecipeInput = {
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutFavoritesNestedInput
  }

  export type UserFavoriteUncheckedUpdateWithoutRecipeInput = {
    userId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserFavoriteUncheckedUpdateManyWithoutRecipeInput = {
    userId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type RecipeCategoryCreateManyCategoryInput = {
    recipeId: string
  }

  export type RecipeCategoryUpdateWithoutCategoryInput = {
    recipe?: RecipeUpdateOneRequiredWithoutCategoriesNestedInput
  }

  export type RecipeCategoryUncheckedUpdateWithoutCategoryInput = {
    recipeId?: StringFieldUpdateOperationsInput | string
  }

  export type RecipeCategoryUncheckedUpdateManyWithoutCategoryInput = {
    recipeId?: StringFieldUpdateOperationsInput | string
  }



  /**
   * Aliases for legacy arg types
   */
    /**
     * @deprecated Use UserCountOutputTypeDefaultArgs instead
     */
    export type UserCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use RecipeCountOutputTypeDefaultArgs instead
     */
    export type RecipeCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = RecipeCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CategoryCountOutputTypeDefaultArgs instead
     */
    export type CategoryCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CategoryCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use UserDefaultArgs instead
     */
    export type UserArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserDefaultArgs<ExtArgs>
    /**
     * @deprecated Use InheritorApplicationDefaultArgs instead
     */
    export type InheritorApplicationArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = InheritorApplicationDefaultArgs<ExtArgs>
    /**
     * @deprecated Use RecipeDefaultArgs instead
     */
    export type RecipeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = RecipeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CategoryDefaultArgs instead
     */
    export type CategoryArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CategoryDefaultArgs<ExtArgs>
    /**
     * @deprecated Use RecipeCategoryDefaultArgs instead
     */
    export type RecipeCategoryArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = RecipeCategoryDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CommentDefaultArgs instead
     */
    export type CommentArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CommentDefaultArgs<ExtArgs>
    /**
     * @deprecated Use UserFavoriteDefaultArgs instead
     */
    export type UserFavoriteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserFavoriteDefaultArgs<ExtArgs>

  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}