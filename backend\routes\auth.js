import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import prisma from '../lib/db.js';

const router = express.Router();

// 生成JWT Token
function generateToken(user) {
  return jwt.sign(
    { 
      userId: user.id, 
      username: user.username, 
      role: user.role 
    },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
}

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, role = 'USER' } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json({ 
        error: '用户名和密码不能为空' 
      });
    }

    // 检查用户名是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { username }
    });

    if (existingUser) {
      return res.status(400).json({ 
        error: '用户名已存在' 
      });
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await prisma.user.findUnique({
        where: { email }
      });

      if (existingEmail) {
        return res.status(400).json({ 
          error: '邮箱已被注册' 
        });
      }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        username,
        email: email || null,
        password: hashedPassword,
        role
      },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        createdAt: true
      }
    });

    // 生成Token
    const token = generateToken(user);

    res.status(201).json({
      message: '注册成功',
      user,
      token
    });

  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({ 
      error: '注册失败，请稍后重试' 
    });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json({ 
        error: '用户名和密码不能为空' 
      });
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { username }
    });

    if (!user) {
      return res.status(401).json({
        error: '用户名或密码错误'
      });
    }

    // 检查账号是否被禁用
    if (!user.isActive) {
      return res.status(401).json({
        error: '账号已被禁用，请联系管理员'
      })
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password);

    if (!isValidPassword) {
      return res.status(401).json({ 
        error: '用户名或密码错误' 
      });
    }

    // 生成Token
    const token = generateToken(user);

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      message: '登录成功',
      user: userWithoutPassword,
      token
    });

  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ 
      error: '登录失败，请稍后重试' 
    });
  }
});

// 获取当前用户信息
router.get('/me', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        error: '未提供认证令牌'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatar: true,
        createdAt: true,
        _count: {
          select: {
            recipes: true,
            favorites: true,
            comments: true
          }
        }
      }
    });

    if (!user) {
      return res.status(401).json({
        error: '用户不存在'
      });
    }

    res.json({ user });

  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(401).json({
      error: '认证令牌无效'
    });
  }
});

// 更新用户信息
router.put('/profile', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        error: '未提供认证令牌'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const { username, email, avatar } = req.body;

    // 检查用户名是否已被其他用户使用
    if (username) {
      const existingUser = await prisma.user.findFirst({
        where: {
          username,
          id: { not: decoded.userId }
        }
      });

      if (existingUser) {
        return res.status(400).json({
          error: '用户名已被使用'
        });
      }
    }

    // 检查邮箱是否已被其他用户使用
    if (email) {
      const existingEmail = await prisma.user.findFirst({
        where: {
          email,
          id: { not: decoded.userId }
        }
      });

      if (existingEmail) {
        return res.status(400).json({
          error: '邮箱已被使用'
        });
      }
    }

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id: decoded.userId },
      data: {
        ...(username && { username }),
        ...(email !== undefined && { email: email || null }),
        ...(avatar !== undefined && { avatar: avatar || null })
      },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatar: true,
        createdAt: true,
        _count: {
          select: {
            recipes: true,
            favorites: true,
            comments: true
          }
        }
      }
    });

    res.json({
      message: '个人信息更新成功',
      user: updatedUser
    });

  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({
      error: '更新个人信息失败'
    });
  }
});

export default router;
