export class Credentials {
  constructor({
    accessKey,
    secretKey,
    sessionToken
  }) {
    this.accessKey = accessKey;
    this.secretKey = secretKey;
    this.sessionToken = sessionToken;
  }
  setAccessKey(accessKey) {
    this.accessKey = accessKey;
  }
  getAccessKey() {
    return this.accessKey;
  }
  setSecretKey(secretKey) {
    this.secretKey = secretKey;
  }
  getSecretKey() {
    return this.secretKey;
  }
  setSessionToken(sessionToken) {
    this.sessionToken = sessionToken;
  }
  getSessionToken() {
    return this.sessionToken;
  }
  get() {
    return this;
  }
}

// deprecated default export, please use named exports.
// keep for backward compatibility.
// eslint-disable-next-line import/no-default-export
export default Credentials;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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