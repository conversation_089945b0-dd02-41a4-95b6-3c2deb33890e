{"version": 3, "file": "encoding-factory.js", "sourceRoot": "", "sources": ["../../../src/encoding/encoding-factory.ts"], "names": [], "mappings": "AAAA,0EAA0E;AAC1E,uCAAuC;AAEvC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAC3E,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC7E,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAC3D,OAAO,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAGpF,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,KAAK,EAAE,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAGjE,EAAE;AACF,YAAY;AACZ,EAAE;AAEF,iCAAiC;AAEjC,EAAE;AACF,2CAA2C;AAC3C,oCAAoC;AACpC,EAAE;AAEF,EAAE;AACF,iBAAiB;AACjB,EAAE;AAEF,mCAAmC;AAEnC,EAAE;AACF,eAAe;AACf,EAAE;AAEF,iCAAiC;AAEjC,EAAE;AACF,aAAa;AACb,EAAE;AAEF,+BAA+B;AAE/B,IAAM,eAAe,GAAG,kBAAkB,EAAE,CAAC;AAwB7C,8DAA8D;AAC9D,kEAAkE;AAClE,iCAAiC;AAEjC,kEAAkE;AAClE,iCAAiC;AAE/B,EAAE;AACF,mCAAmC;AACnC,EAAE;AAEF,2BAA2B;AAE3B,2BAA2B;AAE7B,MAAM,CAAC,IAAM,QAAQ,GAAa;IAEhC,YAAY;IAEZ,sBAAsB;IAEtB,sBAAsB;IAEtB,wCAAwC;IACxC,OAAO,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,WAAW,CAAC,OAAO,CAAC,EAAxB,CAAwB;IAEnE,EAAE;IACF,uDAAuD;IACvD,EAAE;IAEF,WAAW;IAEX,qBAAqB;IACrB,sCAAsC;IAEtC,qBAAqB;IACrB,4DAA4D;IAC5D,wCAAwC;IACxC,KAAK,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,EAAjC,CAAiC;IAE1E,eAAe;IACf,yBAAyB;IAEzB,yBAAyB;IAEzB,wCAAwC;IACxC,SAAS,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,cAAc,CAAC,OAAO,CAAC,EAA3B,CAA2B;IAIxE,EAAE;IACF,wDAAwD;IACxD,EAAE;IAEF,YAAY;IAEZ,sBAAsB;IAEtB,sBAAsB;IAEtB,wCAAwC;IACxC,MAAM,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,WAAW,CAAC,OAAO,CAAC,EAAxB,CAAwB;IAGlE,EAAE;IACF,2CAA2C;IAC3C,EAAE;IAEF,cAAc;IAEd,wBAAwB;IAExB,wBAAwB;IAExB,wCAAwC;IACxC,QAAQ,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,YAAY,CAAC,OAAO,CAAC,EAAzB,CAAyB;IAErE,mBAAmB;IAEnB,6BAA6B;IAE7B,6BAA6B;IAE7B,wCAAwC;IACxC,aAAa,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAA7B,CAA6B;IAE9E,iBAAiB;IAEjB,2BAA2B;IAE3B,2BAA2B;IAE3B,wCAAwC;IACxC,WAAW,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,eAAe,CAAC,OAAO,CAAC,EAA5B,CAA4B;IAE3E,EAAE;IACF,yCAAyC;IACzC,EAAE;IAEF,cAAc;IAEd,wBAAwB;IAExB,wBAAwB;IAExB,wCAAwC;IACxC,QAAQ,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,YAAY,CAAC,OAAO,CAAC,EAAzB,CAAyB;IAGrE,EAAE;IACF,qCAAqC;IACrC,EAAE;IAEF,mBAAmB;IAEnB,qCAAqC;IAErC,uDAAuD;IAEvD,+BAA+B;IAE/B,+BAA+B;IAE/B,gBAAgB;IAChB,0BAA0B;IAC1B,wCAAwC;IACxC,UAAU,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EAA/B,CAA+B;IAC7E,0BAA0B;IAE1B,gBAAgB;IAChB,0BAA0B;IAC1B,wCAAwC;IACxC,UAAU,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,EAAhC,CAAgC;IAC9E,0BAA0B;IAE1B,sBAAsB;IAEtB,gCAAgC;IAEhC,gCAAgC;IAEhC,wCAAwC;IACxC,gBAAgB,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAhC,CAAgC;CACrF,CAAA;AAID,MAAM,CAAC,IAAM,QAAQ,GAAa;IAChC,wCAAwC;IACxC,OAAO,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,WAAW,CAAC,OAAO,CAAC,EAAxB,CAAwB;IACnE,wCAAwC;IACxC,KAAK,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,cAAc,CAAC,OAAO,CAAC,EAA3B,CAA2B;IACpE,wCAAwC;IACxC,SAAS,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,cAAc,CAAC,OAAO,CAAC,EAA3B,CAA2B;IACxE,wCAAwC;IACxC,MAAM,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,WAAW,CAAC,OAAO,CAAC,EAAxB,CAAwB;IAClE,wCAAwC;IACxC,QAAQ,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,YAAY,CAAC,OAAO,CAAC,EAAzB,CAAyB;IACrE,wCAAwC;IACxC,aAAa,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAA7B,CAA6B;IAC9E,wCAAwC;IACxC,WAAW,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,eAAe,CAAC,OAAO,CAAC,EAA5B,CAA4B;IAC3E,wCAAwC;IACxC,QAAQ,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,YAAY,CAAC,OAAO,CAAC,EAAzB,CAAyB;IACrE,wCAAwC;IACxC,UAAU,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EAA/B,CAA+B;IAC7E,wCAAwC;IACxC,UAAU,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,EAAhC,CAAgC;IAC9E,wCAAwC;IACxC,gBAAgB,EAAE,UAAC,OAA4B,IAAK,OAAA,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAhC,CAAgC;CACrF,CAAA;AAGD,IAAI,eAAe,EAAE;IACnB,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ;QAClC,IAAI,QAAQ,CAAC,OAAO,KAAK,8BAA8B;YACrD,OAAO;QACT,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ;YAC3C,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,IAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACtC,wCAAwC;YACxC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,OAA4B;gBACrD,OAAO,IAAI,iBAAiB,CAAC,GAAe,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC,CAAC;YACF,wCAAwC;YACxC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,OAA4B;gBACrD,OAAO,IAAI,iBAAiB,CAAC,GAAe,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;CACJ"}