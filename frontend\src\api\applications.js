import api from './index.js'

// 提交传承人申请
export const submitInheritorApplication = (data) => {
  return api.post('/applications', data)
}

// 使用临时token提交传承人申请
export const submitInheritorApplicationWithToken = (data, tempToken) => {
  return api.post('/applications', data, {
    headers: {
      'Authorization': `Bearer ${tempToken}`
    }
  })
}

// 获取当前用户的申请状态
export const getMyApplication = () => {
  return api.get('/applications/my-application')
}

// 获取所有申请（管理员）
export const getApplications = (params = {}) => {
  return api.get('/applications', { params })
}

// 审核申请（管理员）
export const reviewApplication = (id, data) => {
  return api.patch(`/applications/${id}/review`, data)
}
