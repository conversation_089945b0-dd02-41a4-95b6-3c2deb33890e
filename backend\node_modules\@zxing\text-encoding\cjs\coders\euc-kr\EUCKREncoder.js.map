{"version": 3, "file": "EUCKREncoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/euc-kr/EUCKREncoder.ts"], "names": [], "mappings": ";;AACA,sDAAwD;AACxD,oDAAmD;AACnD,kDAAgE;AAChE,0DAA6E;AAE7E;;;;GAIG;AACH;IAIE,sBAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,8BAAO,GAAP,UAAQ,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,2BAAa;YAC9B,OAAO,mBAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,8BAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,8DAA8D;QAC9D,UAAU;QACV,IAAM,OAAO,GAAG,yBAAe,CAAC,UAAU,EAAE,eAAK,CAAC,QAAQ,CAAa,CAAC,CAAC;QAEzE,uDAAuD;QACvD,IAAI,OAAO,KAAK,IAAI;YAClB,OAAO,wBAAY,CAAC,UAAU,CAAC,CAAC;QAElC,mDAAmD;QACnD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAE9C,wCAAwC;QACxC,IAAM,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAErC,uDAAuD;QACvD,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvB,CAAC;IACH,mBAAC;AAAD,CAAC,AAxCD,IAwCC;AAxCY,oCAAY"}