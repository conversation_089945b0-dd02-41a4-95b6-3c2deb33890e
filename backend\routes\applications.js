import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();
const prisma = new PrismaClient();

// 提交传承人申请
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      realName,
      phone,
      region,
      specialties,
      experience,
      certifications,
      introduction
    } = req.body;

    // 验证必填字段
    if (!realName || !phone || !region || !specialties || !experience || !introduction) {
      return res.status(400).json({
        error: '请填写所有必填字段'
      });
    }

    // 检查用户是否已经提交过申请
    const existingApplication = await prisma.inheritorApplication.findUnique({
      where: { userId: req.user.id }
    });

    if (existingApplication) {
      return res.status(400).json({
        error: '您已经提交过传承人申请，请等待审核结果'
      });
    }

    // 检查用户是否已经是传承人
    const user = await prisma.user.findUnique({
      where: { id: req.user.id }
    });

    if (user.role === 'INHERITOR') {
      return res.status(400).json({
        error: '您已经是传承人，无需重复申请'
      });
    }

    // 创建申请
    const application = await prisma.inheritorApplication.create({
      data: {
        userId: req.user.id,
        realName,
        phone,
        region,
        specialties,
        experience,
        certifications: certifications || null,
        introduction,
        status: 'PENDING'
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    res.json({
      message: '传承人申请提交成功，请等待管理员审核',
      application
    });

  } catch (error) {
    console.error('提交传承人申请失败:', error);
    res.status(500).json({
      error: '提交申请失败'
    });
  }
});

// 获取当前用户的申请状态
router.get('/my-application', authenticateToken, async (req, res) => {
  try {
    const application = await prisma.inheritorApplication.findUnique({
      where: { userId: req.user.id },
      include: {
        reviewer: {
          select: {
            id: true,
            username: true
          }
        }
      }
    });

    res.json({ application });

  } catch (error) {
    console.error('获取申请状态失败:', error);
    res.status(500).json({
      error: '获取申请状态失败'
    });
  }
});

// 获取所有申请（管理员）
router.get('/', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const skip = (page - 1) * limit;

    const where = {};
    if (status && ['PENDING', 'APPROVED', 'REJECTED'].includes(status)) {
      where.status = status;
    }

    const [applications, total] = await Promise.all([
      prisma.inheritorApplication.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { appliedAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              role: true
            }
          },
          reviewer: {
            select: {
              id: true,
              username: true
            }
          }
        }
      }),
      prisma.inheritorApplication.count({ where })
    ]);

    res.json({
      applications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('获取申请列表失败:', error);
    res.status(500).json({
      error: '获取申请列表失败'
    });
  }
});

// 审核申请（管理员）
router.patch('/:id/review', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reviewComment } = req.body;

    if (!['APPROVED', 'REJECTED'].includes(status)) {
      return res.status(400).json({
        error: '无效的审核状态'
      });
    }

    // 获取申请信息
    const application = await prisma.inheritorApplication.findUnique({
      where: { id },
      include: { user: true }
    });

    if (!application) {
      return res.status(404).json({
        error: '申请不存在'
      });
    }

    if (application.status !== 'PENDING') {
      return res.status(400).json({
        error: '该申请已经被审核过了'
      });
    }

    // 使用事务处理审核
    const result = await prisma.$transaction(async (tx) => {
      // 更新申请状态
      const updatedApplication = await tx.inheritorApplication.update({
        where: { id },
        data: {
          status,
          reviewComment,
          reviewedAt: new Date(),
          reviewedBy: req.user.id
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true
            }
          },
          reviewer: {
            select: {
              id: true,
              username: true
            }
          }
        }
      });

      // 如果审核通过，更新用户角色为传承人
      if (status === 'APPROVED') {
        await tx.user.update({
          where: { id: application.userId },
          data: { role: 'INHERITOR' }
        });
      }

      return updatedApplication;
    });

    res.json({
      message: status === 'APPROVED' ? '申请已通过，用户已成为传承人' : '申请已拒绝',
      application: result
    });

  } catch (error) {
    console.error('审核申请失败:', error);
    res.status(500).json({
      error: '审核申请失败'
    });
  }
});

export default router;
