<template>
  <el-button
    :type="isFavorited ? 'warning' : 'default'"
    :loading="loading"
    @click="handleToggleFavorite"
    :disabled="!userStore.isLoggedIn"
  >
    <el-icon>
      <StarFilled v-if="isFavorited" />
      <Star v-else />
    </el-icon>
    {{ isFavorited ? '已收藏' : '收藏' }}
  </el-button>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '../stores/user.js'
import { checkFavorite, toggleFavorite } from '../api/favorites.js'
import { ElMessage } from 'element-plus'

const props = defineProps({
  recipeId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['favoriteChanged'])

const userStore = useUserStore()
const isFavorited = ref(false)
const loading = ref(false)

const checkFavoriteStatus = async () => {
  if (!userStore.isLoggedIn) return
  
  try {
    const result = await checkFavorite(props.recipeId)
    isFavorited.value = result.isFavorited
  } catch (error) {
    console.error('检查收藏状态失败:', error)
  }
}

const handleToggleFavorite = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    loading.value = true
    const result = await toggleFavorite(props.recipeId)
    isFavorited.value = result.isFavorited
    ElMessage.success(result.message)
    
    // 通知父组件收藏状态改变
    emit('favoriteChanged', {
      recipeId: props.recipeId,
      isFavorited: result.isFavorited
    })
  } catch (error) {
    ElMessage.error(error.error || '操作失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  checkFavoriteStatus()
})
</script>

<style scoped>
/* 组件样式可以根据需要调整 */
</style>
