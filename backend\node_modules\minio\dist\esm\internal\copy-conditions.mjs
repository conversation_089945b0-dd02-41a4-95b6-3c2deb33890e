export class CopyConditions {
  modified = '';
  unmodified = '';
  matchETag = '';
  matchETagExcept = '';
  setModified(date) {
    if (!(date instanceof Date)) {
      throw new TypeError('date must be of type Date');
    }
    this.modified = date.toUTCString();
  }
  setUnmodified(date) {
    if (!(date instanceof Date)) {
      throw new TypeError('date must be of type Date');
    }
    this.unmodified = date.toUTCString();
  }
  setMatchETag(etag) {
    this.matchETag = etag;
  }
  setMatchETagExcept(etag) {
    this.matchETagExcept = etag;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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