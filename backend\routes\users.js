import express from 'express';
import prisma from '../lib/db.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();

// 获取所有用户（管理员）
router.get('/', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { page = 1, limit = 10, role, search } = req.query;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where = {};
    
    // 角色筛选
    if (role && ['USER', 'INHERITOR', 'ADMIN'].includes(role)) {
      where.role = role;
    }
    
    // 搜索条件（用户名或邮箱）
    if (search) {
      where.OR = [
        { username: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          avatar: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              recipes: true,
              favorites: true,
              comments: true
            }
          }
        }
      }),
      prisma.user.count({ where })
    ]);

    res.json({
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      error: '获取用户列表失败'
    });
  }
});

// 获取单个用户详情（管理员）
router.get('/:id', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatar: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            recipes: true,
            favorites: true,
            comments: true
          }
        },
        inheritorApplication: {
          select: {
            id: true,
            status: true,
            appliedAt: true,
            reviewedAt: true
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }

    res.json({ user });

  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({
      error: '获取用户详情失败'
    });
  }
});

// 禁用/启用用户账号（管理员）
router.patch('/:id/status', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    // 验证参数
    if (typeof isActive !== 'boolean') {
      return res.status(400).json({
        error: '无效的状态参数'
      });
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        role: true,
        isActive: true
      }
    });

    if (!user) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }

    // 不能禁用管理员账号
    if (user.role === 'ADMIN' && !isActive) {
      return res.status(400).json({
        error: '不能禁用管理员账号'
      });
    }

    // 不能禁用自己的账号
    if (user.id === req.user.id && !isActive) {
      return res.status(400).json({
        error: '不能禁用自己的账号'
      });
    }

    // 更新用户状态
    const updatedUser = await prisma.user.update({
      where: { id },
      data: { isActive },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        isActive: true,
        updatedAt: true
      }
    });

    res.json({
      message: isActive ? '用户账号已启用' : '用户账号已禁用',
      user: updatedUser
    });

  } catch (error) {
    console.error('更新用户状态失败:', error);
    res.status(500).json({
      error: '更新用户状态失败'
    });
  }
});

// 删除用户（管理员）- 谨慎操作
router.delete('/:id', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        role: true
      }
    });

    if (!user) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }

    // 不能删除管理员账号
    if (user.role === 'ADMIN') {
      return res.status(400).json({
        error: '不能删除管理员账号'
      });
    }

    // 不能删除自己的账号
    if (user.id === req.user.id) {
      return res.status(400).json({
        error: '不能删除自己的账号'
      });
    }

    // 删除用户及其所有关联数据（使用事务确保数据一致性）
    await prisma.$transaction(async (tx) => {
      console.log(`开始删除用户 ${user.username} (${id}) 及其关联数据...`);

      // 1. 删除用户的评论
      const deletedComments = await tx.comment.deleteMany({
        where: { userId: id }
      });
      console.log(`删除了 ${deletedComments.count} 条评论`);

      // 2. 删除用户的收藏
      const deletedFavorites = await tx.userFavorite.deleteMany({
        where: { userId: id }
      });
      console.log(`删除了 ${deletedFavorites.count} 条收藏`);

      // 3. 删除用户的传承人申请（如果有）
      const deletedApplications = await tx.inheritorApplication.deleteMany({
        where: { userId: id }
      });
      console.log(`删除了 ${deletedApplications.count} 条传承人申请`);

      // 4. 删除用户作为审核人的申请记录（将reviewedBy设为null）
      const updatedReviews = await tx.inheritorApplication.updateMany({
        where: { reviewedBy: id },
        data: { reviewedBy: null }
      });
      console.log(`更新了 ${updatedReviews.count} 条审核记录`);

      // 5. 删除用户的菜谱（这会级联删除菜谱的评论和收藏）
      const deletedRecipes = await tx.recipe.deleteMany({
        where: { authorId: id }
      });
      console.log(`删除了 ${deletedRecipes.count} 个菜谱`);

      // 6. 最后删除用户
      await tx.user.delete({
        where: { id }
      });
      console.log(`用户 ${user.username} 删除完成`);
    });

    res.json({
      message: '用户已删除',
      deletedUser: {
        id: user.id,
        username: user.username
      }
    });

  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      error: '删除用户失败'
    });
  }
});

export default router;
