import api from './index.js'

// 获取菜谱列表
export const getRecipes = (params = {}) => {
  return api.get('/recipes', { params })
}

// 获取菜谱详情
export const getRecipeById = (id) => {
  return api.get(`/recipes/${id}`)
}

// 创建菜谱
export const createRecipe = (recipeData) => {
  return api.post('/recipes', recipeData)
}

// 更新菜谱
export const updateRecipe = (id, recipeData) => {
  return api.put(`/recipes/${id}`, recipeData)
}

// 删除菜谱
export const deleteRecipe = (id) => {
  return api.delete(`/recipes/${id}`)
}

// 获取我的菜谱列表
export const getMyRecipes = (params = {}) => {
  return api.get('/recipes', { params })
}

// 搜索菜谱
export const searchRecipes = (searchParams) => {
  return api.get('/recipes', { params: searchParams })
}
