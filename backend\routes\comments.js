import express from 'express';
import prisma from '../lib/db.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取菜谱的评论列表
router.get('/recipe/:recipeId', async (req, res) => {
  try {
    const { recipeId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // 检查菜谱是否存在
    const recipe = await prisma.recipe.findUnique({
      where: { id: recipeId }
    });

    if (!recipe) {
      return res.status(404).json({ 
        error: '菜谱不存在' 
      });
    }

    // 获取评论列表
    const comments = await prisma.comment.findMany({
      where: {
        recipeId: recipeId
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            avatar: true,
            role: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: parseInt(limit)
    });

    // 获取总数
    const total = await prisma.comment.count({
      where: {
        recipeId: recipeId
      }
    });

    // 获取评分统计
    const ratingStats = await prisma.comment.aggregate({
      where: {
        recipeId: recipeId,
        rating: {
          not: null
        }
      },
      _avg: {
        rating: true
      },
      _count: {
        rating: true
      }
    });

    res.json({
      comments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      ratingStats: {
        averageRating: ratingStats._avg.rating || 0,
        totalRatings: ratingStats._count.rating || 0
      }
    });

  } catch (error) {
    console.error('获取评论列表错误:', error);
    res.status(500).json({ 
      error: '获取评论列表失败' 
    });
  }
});

// 添加评论
router.post('/recipe/:recipeId', authenticateToken, async (req, res) => {
  try {
    const { recipeId } = req.params;
    const { content, rating } = req.body;

    // 验证必填字段
    if (!content || content.trim().length === 0) {
      return res.status(400).json({ 
        error: '评论内容不能为空' 
      });
    }

    // 验证评分
    if (rating !== undefined && (rating < 1 || rating > 5)) {
      return res.status(400).json({ 
        error: '评分必须在1-5之间' 
      });
    }

    // 检查菜谱是否存在
    const recipe = await prisma.recipe.findUnique({
      where: { id: recipeId }
    });

    if (!recipe) {
      return res.status(404).json({ 
        error: '菜谱不存在' 
      });
    }

    // 移除重复评论限制，允许用户对同一菜谱发表多条评论

    // 创建评论
    const comment = await prisma.comment.create({
      data: {
        content: content.trim(),
        rating: rating ? parseInt(rating) : null,
        userId: req.user.userId,
        recipeId: recipeId
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            avatar: true,
            role: true
          }
        }
      }
    });

    res.status(201).json({
      message: '评论添加成功',
      comment
    });

  } catch (error) {
    console.error('添加评论错误:', error);
    res.status(500).json({ 
      error: '添加评论失败' 
    });
  }
});

// 更新评论
router.put('/:commentId', authenticateToken, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { content, rating } = req.body;

    // 验证必填字段
    if (!content || content.trim().length === 0) {
      return res.status(400).json({ 
        error: '评论内容不能为空' 
      });
    }

    // 验证评分
    if (rating !== undefined && (rating < 1 || rating > 5)) {
      return res.status(400).json({ 
        error: '评分必须在1-5之间' 
      });
    }

    // 检查评论是否存在
    const existingComment = await prisma.comment.findUnique({
      where: { id: commentId }
    });

    if (!existingComment) {
      return res.status(404).json({ 
        error: '评论不存在' 
      });
    }

    // 检查权限（只有评论作者或管理员可以修改）
    if (existingComment.userId !== req.user.userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({ 
        error: '没有权限修改此评论' 
      });
    }

    // 更新评论
    const comment = await prisma.comment.update({
      where: { id: commentId },
      data: {
        content: content.trim(),
        rating: rating ? parseInt(rating) : null
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            avatar: true,
            role: true
          }
        }
      }
    });

    res.json({
      message: '评论更新成功',
      comment
    });

  } catch (error) {
    console.error('更新评论错误:', error);
    res.status(500).json({ 
      error: '更新评论失败' 
    });
  }
});

// 删除评论
router.delete('/:commentId', authenticateToken, async (req, res) => {
  try {
    const { commentId } = req.params;

    // 检查评论是否存在
    const existingComment = await prisma.comment.findUnique({
      where: { id: commentId }
    });

    if (!existingComment) {
      return res.status(404).json({ 
        error: '评论不存在' 
      });
    }

    // 检查权限（只有评论作者或管理员可以删除）
    if (existingComment.userId !== req.user.userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({ 
        error: '没有权限删除此评论' 
      });
    }

    // 删除评论
    await prisma.comment.delete({
      where: { id: commentId }
    });

    res.json({
      message: '评论删除成功'
    });

  } catch (error) {
    console.error('删除评论错误:', error);
    res.status(500).json({ 
      error: '删除评论失败' 
    });
  }
});

// 获取用户的评论列表
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const comments = await prisma.comment.findMany({
      where: {
        userId: userId
      },
      include: {
        recipe: {
          select: {
            id: true,
            name: true,
            dialectName: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: parseInt(limit)
    });

    const total = await prisma.comment.count({
      where: {
        userId: userId
      }
    });

    res.json({
      comments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('获取用户评论列表错误:', error);
    res.status(500).json({ 
      error: '获取用户评论列表失败' 
    });
  }
});

export default router;
