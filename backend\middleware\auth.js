import jwt from 'jsonwebtoken';
import prisma from '../lib/db.js';

// JWT认证中间件
export function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ 
      error: '访问被拒绝，需要认证令牌' 
    });
  }

  jwt.verify(token, process.env.JWT_SECRET, async (err, decoded) => {
    if (err) {
      return res.status(403).json({ 
        error: '认证令牌无效或已过期' 
      });
    }

    try {
      // 验证用户是否仍然存在
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          username: true,
          role: true
        }
      });

      if (!user) {
        return res.status(403).json({ 
          error: '用户不存在' 
        });
      }

      req.user = {
        id: user.id,
        userId: user.id,  // 保持向后兼容
        username: user.username,
        role: user.role
      };
      
      next();
    } catch (error) {
      console.error('认证中间件错误:', error);
      return res.status(500).json({ 
        error: '认证验证失败' 
      });
    }
  });
}

// 角色权限检查中间件
export function requireRole(roles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        error: '需要先进行身份认证' 
      });
    }

    const userRole = req.user.role;
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({ 
        error: '权限不足，无法访问此资源' 
      });
    }

    next();
  };
}

// 管理员权限检查
export function requireAdmin(req, res, next) {
  return requireRole(['ADMIN'])(req, res, next);
}

// 传承人或管理员权限检查
export function requireInheritorOrAdmin(req, res, next) {
  return requireRole(['INHERITOR', 'ADMIN'])(req, res, next);
}

// 可选认证中间件（不强制要求登录）
export function optionalAuth(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    req.user = null;
    return next();
  }

  jwt.verify(token, process.env.JWT_SECRET, async (err, decoded) => {
    if (err) {
      req.user = null;
      return next();
    }

    try {
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          username: true,
          role: true
        }
      });

      req.user = user ? {
        id: user.id,
        userId: user.id,  // 保持向后兼容
        username: user.username,
        role: user.role
      } : null;

      next();
    } catch (error) {
      console.error('可选认证中间件错误:', error);
      req.user = null;
      next();
    }
  });
}
