import express from 'express';
import prisma from '../lib/db.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();

// MinIO返回的已经是完整URL，不需要额外处理

// 获取菜谱列表（支持搜索和分页）
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 12,
      search = '',
      region = '',
      category = '',
      authorId = '',  // 新增：按作者筛选
      isPublished = ''  // 新增：按发布状态筛选
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    const where = {
      // 如果没有指定authorId，默认只显示已发布的菜谱
      ...(authorId ? {} : { isPublished: true }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { dialectName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(region && { region: { contains: region, mode: 'insensitive' } }),
      ...(authorId && { authorId: authorId }),
      ...(isPublished !== '' && { isPublished: isPublished === 'true' })
    };

    // 获取菜谱列表
    const recipes = await prisma.recipe.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            username: true,
            role: true
          }
        },
        categories: {
          include: {
            category: true
          }
        },
        _count: {
          select: {
            comments: true,
            favorites: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: parseInt(limit)
    });

    // 获取总数
    const total = await prisma.recipe.count({ where });

    res.json({
      recipes,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('获取菜谱列表错误:', error);
    res.status(500).json({ 
      error: '获取菜谱列表失败' 
    });
  }
});

// 获取菜谱详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const recipe = await prisma.recipe.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            role: true,
            avatar: true
          }
        },
        categories: {
          include: {
            category: true
          }
        },
        comments: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                avatar: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        _count: {
          select: {
            favorites: true
          }
        }
      }
    });

    if (!recipe) {
      return res.status(404).json({
        error: '菜谱不存在'
      });
    }

    // 增加浏览量（异步触发，不阻塞返回）
    prisma.recipe.update({
      where: { id },
      data: { viewCount: { increment: 1 } }
    }).catch(() => {});

    res.json({ recipe });

  } catch (error) {
    console.error('获取菜谱详情错误:', error);
    res.status(500).json({ 
      error: '获取菜谱详情失败' 
    });
  }
});

// 创建菜谱（需要登录）
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      name,
      dialectName,
      dialectAudio,
      region,
      ingredients,
      steps,
      image,
      description,
      difficulty = 1,
      cookTime,
      servings,
      // 文化传承字段
      culturalHistory,
      regionalFeature,
      culturalMeaning,
      inheritanceStory
    } = req.body;

    // 验证必填字段
    if (!name || !dialectName || !region || !ingredients || !steps) {
      return res.status(400).json({
        error: '请填写所有必填字段'
      });
    }

    // 规范化可选字段
    const normalized = {
      dialectAudio: dialectAudio === '' ? null : dialectAudio,
      image: image === '' ? null : image,
      description: description === '' ? null : description,
      cookTime: cookTime ? parseInt(cookTime) : null,
      servings: servings ? parseInt(servings) : null,
      difficulty: difficulty ? parseInt(difficulty) : 1,
      // 文化传承字段规范化
      culturalHistory: culturalHistory === '' ? null : culturalHistory,
      regionalFeature: regionalFeature === '' ? null : regionalFeature,
      culturalMeaning: culturalMeaning === '' ? null : culturalMeaning,
      inheritanceStory: inheritanceStory === '' ? null : inheritanceStory
    };

    // 创建菜谱（仅白名单字段）
    const recipe = await prisma.recipe.create({
      data: {
        name,
        dialectName,
        region,
        ingredients,
        steps,
        ...normalized,
        authorId: req.user.userId
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            role: true
          }
        }
      }
    });

    res.status(201).json({
      message: '菜谱创建成功',
      recipe
    });

  } catch (error) {
    console.error('创建菜谱错误:', error);
    res.status(500).json({
      error: '创建菜谱失败'
    });
  }
});

// 更新菜谱（需要是作者或管理员）
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const body = req.body || {};

    // 检查菜谱是否存在
    const existingRecipe = await prisma.recipe.findUnique({
      where: { id }
    });

    if (!existingRecipe) {
      return res.status(404).json({
        error: '菜谱不存在'
      });
    }

    // 检查权限（只有作者或管理员可以修改）
    if (existingRecipe.authorId !== req.user.userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        error: '没有权限修改此菜谱'
      });
    }

    // 仅允许白名单字段
    const allowedFields = [
      'name','dialectName','dialectAudio','region','ingredients','steps',
      'image','description','difficulty','cookTime','servings','isPublished',
      // 文化传承字段
      'culturalHistory','regionalFeature','culturalMeaning','inheritanceStory'
    ];

    const data = {};
    for (const key of allowedFields) {
      if (Object.prototype.hasOwnProperty.call(body, key)) {
        data[key] = body[key];
      }
    }

    // 类型/空值规范化
    if (data.difficulty !== undefined) data.difficulty = parseInt(data.difficulty);
    if (data.cookTime !== undefined) data.cookTime = data.cookTime === '' ? null : parseInt(data.cookTime);
    if (data.servings !== undefined) data.servings = data.servings === '' ? null : parseInt(data.servings);
    if (data.dialectAudio !== undefined) data.dialectAudio = data.dialectAudio === '' ? null : data.dialectAudio;
    if (data.image !== undefined) data.image = data.image === '' ? null : data.image;
    if (data.description !== undefined) data.description = data.description === '' ? null : data.description;

    // 文化传承字段规范化
    if (data.culturalHistory !== undefined) data.culturalHistory = data.culturalHistory === '' ? null : data.culturalHistory;
    if (data.regionalFeature !== undefined) data.regionalFeature = data.regionalFeature === '' ? null : data.regionalFeature;
    if (data.culturalMeaning !== undefined) data.culturalMeaning = data.culturalMeaning === '' ? null : data.culturalMeaning;
    if (data.inheritanceStory !== undefined) data.inheritanceStory = data.inheritanceStory === '' ? null : data.inheritanceStory;

    const recipe = await prisma.recipe.update({
      where: { id },
      data,
      include: {
        author: {
          select: { id: true, username: true, role: true }
        }
      }
    });

    res.json({ message: '菜谱更新成功', recipe });

  } catch (error) {
    console.error('更新菜谱错误:', error);
    res.status(500).json({
      error: '更新菜谱失败'
    });
  }
});

// 删除菜谱（需要是作者或管理员）
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查菜谱是否存在
    const existingRecipe = await prisma.recipe.findUnique({
      where: { id }
    });

    if (!existingRecipe) {
      return res.status(404).json({ 
        error: '菜谱不存在' 
      });
    }

    // 检查权限
    if (existingRecipe.authorId !== req.user.userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({ 
        error: '没有权限删除此菜谱' 
      });
    }

    // 删除菜谱
    await prisma.recipe.delete({
      where: { id }
    });

    res.json({
      message: '菜谱删除成功'
    });

  } catch (error) {
    console.error('删除菜谱错误:', error);
    res.status(500).json({ 
      error: '删除菜谱失败' 
    });
  }
});

export default router;
