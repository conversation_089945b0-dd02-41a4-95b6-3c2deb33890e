"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var errors = _interopRequireWildcard(require("../errors.js"), true);
var _helper = require("./helper.js");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
// Build PostPolicy object that can be signed by presignedPostPolicy

class PostPolicy {
  policy = {
    conditions: []
  };
  formData = {};

  // set expiration date
  setExpires(date) {
    if (!date) {
      throw new errors.InvalidDateError('Invalid date: cannot be null');
    }
    this.policy.expiration = date.toISOString();
  }

  // set object name
  setKey(objectName) {
    if (!(0, _helper.isValidObjectName)(objectName)) {
      throw new errors.InvalidObjectNameError(`Invalid object name : ${objectName}`);
    }
    this.policy.conditions.push(['eq', '$key', objectName]);
    this.formData.key = objectName;
  }

  // set object name prefix, i.e policy allows any keys with this prefix
  setKeyStartsWith(prefix) {
    if (!(0, _helper.isValidPrefix)(prefix)) {
      throw new errors.InvalidPrefixError(`Invalid prefix : ${prefix}`);
    }
    this.policy.conditions.push(['starts-with', '$key', prefix]);
    this.formData.key = prefix;
  }

  // set bucket name
  setBucket(bucketName) {
    if (!(0, _helper.isValidBucketName)(bucketName)) {
      throw new errors.InvalidBucketNameError(`Invalid bucket name : ${bucketName}`);
    }
    this.policy.conditions.push(['eq', '$bucket', bucketName]);
    this.formData.bucket = bucketName;
  }

  // set Content-Type
  setContentType(type) {
    if (!type) {
      throw new Error('content-type cannot be null');
    }
    this.policy.conditions.push(['eq', '$Content-Type', type]);
    this.formData['Content-Type'] = type;
  }

  // set Content-Type prefix, i.e image/ allows any image
  setContentTypeStartsWith(prefix) {
    if (!prefix) {
      throw new Error('content-type cannot be null');
    }
    this.policy.conditions.push(['starts-with', '$Content-Type', prefix]);
    this.formData['Content-Type'] = prefix;
  }

  // set Content-Disposition
  setContentDisposition(value) {
    if (!value) {
      throw new Error('content-disposition cannot be null');
    }
    this.policy.conditions.push(['eq', '$Content-Disposition', value]);
    this.formData['Content-Disposition'] = value;
  }

  // set minimum/maximum length of what Content-Length can be.
  setContentLengthRange(min, max) {
    if (min > max) {
      throw new Error('min cannot be more than max');
    }
    if (min < 0) {
      throw new Error('min should be > 0');
    }
    if (max < 0) {
      throw new Error('max should be > 0');
    }
    this.policy.conditions.push(['content-length-range', min, max]);
  }

  // set user defined metadata
  setUserMetaData(metaData) {
    if (!(0, _helper.isObject)(metaData)) {
      throw new TypeError('metadata should be of type "object"');
    }
    Object.entries(metaData).forEach(([key, value]) => {
      const amzMetaDataKey = `x-amz-meta-${key}`;
      this.policy.conditions.push(['eq', `$${amzMetaDataKey}`, value]);
      this.formData[amzMetaDataKey] = value.toString();
    });
  }
}
exports.PostPolicy = PostPolicy;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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