import api from './index.js'

// 获取用户列表（管理员）
export const getUsers = (params = {}) => {
  return api.get('/users', { params })
}

// 获取用户详情（管理员）
export const getUserDetail = (userId) => {
  return api.get(`/users/${userId}`)
}

// 禁用/启用用户账号（管理员）
export const updateUserStatus = (userId, isActive) => {
  return api.patch(`/users/${userId}/status`, { isActive })
}

// 删除用户（管理员）
export const deleteUser = (userId) => {
  return api.delete(`/users/${userId}`)
}
