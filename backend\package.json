{"name": "digital-hometown-backend", "version": "1.0.0", "description": "数字乡味后端API服务", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "seed": "node scripts/seed.js", "seed:recipes": "node scripts/seed_recipes.js"}, "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "minio": "^8.0.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "prisma": "^5.7.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["digital", "hometown", "flavor", "api"], "author": "Digital Hometown Team", "license": "MIT"}