import api from './index.js'

// 用户注册
export const register = (userData) => {
  return api.post('/auth/register', userData)
}

// 用户登录
export const login = (credentials) => {
  return api.post('/auth/login', credentials)
}

// 获取当前用户信息
export const getCurrentUser = () => {
  return api.get('/auth/me')
}

// 更新用户信息
export const updateProfile = (profileData) => {
  return api.put('/auth/profile', profileData)
}

// 登出（前端处理）
export const logout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
  window.location.href = '/login'
}
