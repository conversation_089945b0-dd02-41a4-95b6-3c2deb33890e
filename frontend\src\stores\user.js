import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { login, register, getCurrentUser, updateProfile } from '../api/auth.js'

export const useUserStore = defineStore('user', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token'))
  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')

  // 登录
  async function loginUser(credentials) {
    try {
      const response = await login(credentials)
      token.value = response.token
      user.value = response.user

      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))

      return response
    } catch (error) {
      throw error
    }
  }

  // 注册
  async function registerUser(userData) {
    try {
      const response = await register(userData)
      token.value = response.token
      user.value = response.user

      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))

      return response
    } catch (error) {
      throw error
    }
  }

  // 注册用户但不自动登录
  async function registerUserWithoutLogin(userData) {
    try {
      const response = await register(userData)
      // 不保存token和用户信息到localStorage
      // 只返回响应数据
      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  function logoutUser() {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 初始化用户信息
  function initUser() {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      user.value = JSON.parse(savedUser)
    }
  }

  // 获取当前用户信息
  async function fetchCurrentUser() {
    try {
      const response = await getCurrentUser()
      user.value = response.user
      localStorage.setItem('user', JSON.stringify(response.user))
      return response.user
    } catch (error) {
      logoutUser()
      throw error
    }
  }

  // 更新用户信息
  async function updateUserProfile(profileData) {
    try {
      const response = await updateProfile(profileData)
      user.value = response.user
      localStorage.setItem('user', JSON.stringify(response.user))
      return response.user
    } catch (error) {
      throw error
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    isAdmin,
    loginUser,
    registerUser,
    registerUserWithoutLogin,
    logoutUser,
    initUser,
    fetchCurrentUser,
    updateUserProfile
  }
})
