<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-card">
        <div class="register-header">
          <h2>注册数字乡味</h2>
          <p>加入我们，一起传承家乡文化</p>
        </div>

        <!-- 角色选择 -->
        <div class="role-selection">
          <h3>选择您的身份</h3>
          <el-radio-group v-model="selectedRole" class="role-options">
            <el-radio value="USER" class="role-option">
              <div class="role-card">
                <el-icon class="role-icon"><User /></el-icon>
                <div class="role-info">
                  <h4>普通用户</h4>
                  <p>浏览菜谱、收藏、评论</p>
                </div>
              </div>
            </el-radio>
            <el-radio value="INHERITOR" class="role-option">
              <div class="role-card">
                <el-icon class="role-icon"><User /></el-icon>
                <div class="role-info">
                  <h4>传承人申请</h4>
                  <p>分享传统菜谱、传承文化</p>
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
        
        <el-form
          ref="registerFormRef"
          :model="allFormData"
          :rules="allRules"
          class="register-form"
          @submit.prevent="handleRegister"
        >
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="请输入用户名"
              size="large"
              :prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="请输入邮箱（可选）"
              size="large"
              :prefix-icon="Message"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请确认密码"
              size="large"
              :prefix-icon="Lock"
              show-password
              @keyup.enter="handleRegister"
            />
          </el-form-item>

          <!-- 传承人申请表单 -->
          <div v-if="selectedRole === 'INHERITOR'" class="inheritor-form">
            <el-divider content-position="center">传承人申请信息</el-divider>

            <el-form-item prop="realName">
              <el-input
                v-model="inheritorForm.realName"
                placeholder="请输入真实姓名"
                size="large"
                :prefix-icon="User"
              />
            </el-form-item>

            <el-form-item prop="phone">
              <el-input
                v-model="inheritorForm.phone"
                placeholder="请输入联系电话"
                size="large"
                :prefix-icon="Phone"
              />
            </el-form-item>

            <el-form-item prop="region">
              <el-select
                v-model="inheritorForm.region"
                placeholder="请选择所在地区"
                size="large"
                style="width: 100%"
              >
                <el-option label="潮州" value="潮州" />
                <el-option label="汕头" value="汕头" />
                <el-option label="揭阳" value="揭阳" />
                <el-option label="汕尾" value="汕尾" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>

            <el-form-item prop="specialties">
              <el-input
                v-model="inheritorForm.specialties"
                placeholder="请输入擅长的菜系或特色菜"
                size="large"
                type="textarea"
                :rows="2"
              />
            </el-form-item>

            <el-form-item prop="experience">
              <el-input
                v-model="inheritorForm.experience"
                placeholder="请描述您的从业经验"
                size="large"
                type="textarea"
                :rows="3"
              />
            </el-form-item>

            <el-form-item prop="certifications">
              <el-input
                v-model="inheritorForm.certifications"
                placeholder="相关证书或资质（可选）"
                size="large"
                type="textarea"
                :rows="2"
              />
            </el-form-item>

            <el-form-item prop="introduction">
              <el-input
                v-model="inheritorForm.introduction"
                placeholder="请介绍一下您自己和您对传承潮汕菜文化的理解"
                size="large"
                type="textarea"
                :rows="4"
              />
            </el-form-item>
          </div>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="register-button"
              :loading="loading"
              @click="handleRegister"
            >
              {{ loading ? '注册中...' : '注册' }}
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="register-footer">
          <p>
            已有账号？
            <router-link to="/login" class="login-link">立即登录</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { ElMessage } from 'element-plus'
import { User, Lock, Message, Phone } from '@element-plus/icons-vue'
import { submitInheritorApplicationWithToken } from '../api/applications.js'

const router = useRouter()
const userStore = useUserStore()

const registerFormRef = ref(null)
const loading = ref(false)
const selectedRole = ref('USER')

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const inheritorForm = reactive({
  realName: '',
  phone: '',
  region: '',
  specialties: '',
  experience: '',
  certifications: '',
  introduction: ''
})

// 合并表单数据
const allFormData = computed(() => ({
  ...registerForm,
  ...inheritorForm
}))

const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 合并验证规则
const allRules = computed(() => {
  const rules = { ...registerRules }

  if (selectedRole.value === 'inheritor') {
    // 添加传承人表单的验证规则
    rules.realName = [
      { required: true, message: '请输入真实姓名', trigger: 'blur' },
      { min: 2, max: 10, message: '姓名长度在 2 到 10 个字符', trigger: 'blur' }
    ]
    rules.phone = [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
    rules.region = [
      { required: true, message: '请输入所在地区', trigger: 'blur' }
    ]
    rules.specialties = [
      { required: true, message: '请输入专业技能', trigger: 'blur' },
      { min: 5, max: 200, message: '专业技能描述长度在 5 到 200 个字符', trigger: 'blur' }
    ]
    rules.experience = [
      { required: true, message: '请输入相关经验', trigger: 'blur' },
      { min: 10, max: 500, message: '相关经验描述长度在 10 到 500 个字符', trigger: 'blur' }
    ]
    rules.certifications = [
      { min: 0, max: 200, message: '证书资质描述不能超过 200 个字符', trigger: 'blur' }
    ]
    rules.introduction = [
      { min: 0, max: 300, message: '个人简介不能超过 300 个字符', trigger: 'blur' }
    ]
  }

  return rules
})

const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, message: '用户名只能包含字母、数字、下划线和中文', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  // 传承人申请表单验证
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  region: [
    { required: true, message: '请选择所在地区', trigger: 'change' }
  ],
  specialties: [
    { required: true, message: '请输入擅长的菜系或特色菜', trigger: 'blur' },
    { min: 5, max: 200, message: '内容长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  experience: [
    { required: true, message: '请描述您的从业经验', trigger: 'blur' },
    { min: 10, max: 500, message: '内容长度在 10 到 500 个字符', trigger: 'blur' }
  ],
  introduction: [
    { required: true, message: '请介绍一下您自己', trigger: 'blur' },
    { min: 20, max: 1000, message: '内容长度在 20 到 1000 个字符', trigger: 'blur' }
  ]
}

const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    await registerFormRef.value.validate()
    loading.value = true

    const userData = {
      username: registerForm.username,
      password: registerForm.password,
      email: registerForm.email || undefined
    }

    // 根据角色选择不同的注册方式
    if (selectedRole.value === 'INHERITOR') {
      // 传承人注册：不自动登录，但获取临时token用于提交申请
      const registerResponse = await userStore.registerUserWithoutLogin(userData)

      try {
        // 使用临时token提交传承人申请
        await submitInheritorApplicationWithToken(inheritorForm, registerResponse.token)
        ElMessage.success('注册成功！传承人申请已提交，请等待管理员审核。请前往登录页面登录。')
      } catch (appError) {
        console.error('传承人申请提交失败:', appError)
        ElMessage.warning('注册成功，但传承人申请提交失败，请稍后在个人中心重新申请。请前往登录页面登录。')
      }
    } else {
      // 普通用户注册：不自动登录
      await userStore.registerUserWithoutLogin(userData)
      ElMessage.success('注册成功！请前往登录页面登录。')
    }

    // 跳转到登录页面
    router.push('/login')

  } catch (error) {
    console.error('注册失败:', error)
    ElMessage.error(error.error || '注册失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-container {
  width: 100%;
  max-width: 500px;
}

.register-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h2 {
  color: #303133;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.register-header p {
  color: #909399;
  font-size: 14px;
}

.register-form {
  margin-bottom: 20px;
}

.register-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: bold;
}

.register-footer {
  text-align: center;
}

.register-footer p {
  color: #909399;
  font-size: 14px;
}

.login-link {
  color: #409eff;
  text-decoration: none;
  font-weight: bold;
}

.login-link:hover {
  text-decoration: underline;
}

/* 角色选择样式 */
.role-selection {
  margin-bottom: 30px;
}

.role-selection h3 {
  text-align: center;
  color: #303133;
  margin-bottom: 20px;
  font-size: 18px;
}

.role-options {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.role-option {
  flex: 1;
  min-width: 180px;
  max-width: 220px;
}

.role-option :deep(.el-radio__input) {
  display: none;
}

.role-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.role-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.role-option :deep(.el-radio.is-checked) .role-card {
  border-color: #409eff;
  background: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.role-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 10px;
}

.role-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.role-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 传承人表单样式 */
.inheritor-form {
  margin-top: 20px;
  padding: 20px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .register-container {
    max-width: 90%;
    padding: 0 10px;
  }

  .register-card {
    padding: 30px 20px;
  }

  .role-options {
    flex-direction: column;
    gap: 10px;
  }

  .role-option {
    min-width: auto;
    max-width: none;
  }

  .role-card {
    padding: 12px;
  }

  .role-icon {
    font-size: 28px;
  }
}

.inheritor-form {
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.inheritor-form :deep(.el-divider) {
  margin: 0 0 20px 0;
}

.inheritor-form :deep(.el-divider__text) {
  background: #f8f9fa;
  color: #409eff;
  font-weight: 600;
}
</style>
