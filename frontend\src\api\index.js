import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response
      
      if (status === 401) {
        // 未授权，清除token
        localStorage.removeItem('token')
        localStorage.removeItem('user')

        // 如果不是在登录页面，则跳转到登录页
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login'
        }
      }
      
      return Promise.reject(data || { error: '请求失败' })
    } else if (error.request) {
      // 网络错误
      return Promise.reject({ error: '网络连接失败，请检查网络' })
    } else {
      // 其他错误
      return Promise.reject({ error: '请求配置错误' })
    }
  }
)

export default api
