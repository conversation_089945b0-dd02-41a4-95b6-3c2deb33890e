import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { authenticateToken } from '../middleware/auth.js';
import { uploadToMinio, deleteFromMinio, initializeBucket } from '../lib/minio.js';

const router = express.Router();

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads');
const audioDir = path.join(uploadDir, 'audio');
const imageDir = path.join(uploadDir, 'images');

[uploadDir, audioDir, imageDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// 配置multer存储 - 使用内存存储，然后上传到MinIO
const storage = multer.memoryStorage();

// 文件过滤器
const fileFilter = (req, file, cb) => {
  if (file.fieldname === 'audio') {
    // 音频文件类型检查
    const allowedAudioTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/ogg'];
    if (allowedAudioTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只支持 MP3, WAV, OGG 格式的音频文件'), false);
    }
  } else if (file.fieldname === 'image') {
    // 图片文件类型检查
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (allowedImageTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只支持 JPEG, PNG, WebP 格式的图片文件'), false);
    }
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

// 配置multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB限制
  }
});

// 上传音频文件
router.post('/audio', authenticateToken, upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: '请选择要上传的音频文件'
      });
    }

    // 上传到MinIO
    const result = await uploadToMinio(req.file, 'audio');

    res.json({
      message: '音频上传成功',
      file: {
        filename: result.fileName,
        originalName: result.originalName,
        size: result.size,
        url: result.url,
        bucket: result.bucket
      }
    });

  } catch (error) {
    console.error('音频上传错误:', error);
    res.status(500).json({
      error: error.message || '音频上传失败'
    });
  }
});

// 上传图片文件
router.post('/image', authenticateToken, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: '请选择要上传的图片文件'
      });
    }

    // 上传到MinIO
    const result = await uploadToMinio(req.file, 'images');

    res.json({
      message: '图片上传成功',
      file: {
        filename: result.fileName,
        originalName: result.originalName,
        size: result.size,
        url: result.url,
        bucket: result.bucket
      }
    });

  } catch (error) {
    console.error('图片上传错误:', error);
    res.status(500).json({
      error: error.message || '图片上传失败'
    });
  }
});

// 同时上传音频和图片
router.post('/multiple', authenticateToken, upload.fields([
  { name: 'audio', maxCount: 1 },
  { name: 'image', maxCount: 1 }
]), async (req, res) => {
  try {
    const result = {
      message: '文件上传成功',
      files: {}
    };

    if (req.files.audio && req.files.audio[0]) {
      const audioResult = await uploadToMinio(req.files.audio[0], 'audio');
      result.files.audio = {
        filename: audioResult.fileName,
        originalName: audioResult.originalName,
        size: audioResult.size,
        url: audioResult.url,
        bucket: audioResult.bucket
      };
    }

    if (req.files.image && req.files.image[0]) {
      const imageResult = await uploadToMinio(req.files.image[0], 'images');
      result.files.image = {
        filename: imageResult.fileName,
        originalName: imageResult.originalName,
        size: imageResult.size,
        url: imageResult.url,
        bucket: imageResult.bucket
      };
    }

    if (!req.files.audio && !req.files.image) {
      return res.status(400).json({
        error: '请选择要上传的文件'
      });
    }

    res.json(result);

  } catch (error) {
    console.error('文件上传错误:', error);
    res.status(500).json({
      error: error.message || '文件上传失败'
    });
  }
});

// 删除文件
router.delete('/:objectName(*)', authenticateToken, async (req, res) => {
  try {
    const { objectName } = req.params;

    if (!objectName) {
      return res.status(400).json({
        error: '请提供要删除的文件名'
      });
    }

    // 从MinIO删除文件
    await deleteFromMinio(objectName);

    res.json({
      message: '文件删除成功'
    });

  } catch (error) {
    console.error('文件删除错误:', error);
    res.status(500).json({
      error: error.message || '文件删除失败'
    });
  }
});

// 错误处理中间件
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ 
        error: '文件大小超过限制（最大10MB）' 
      });
    }
  }
  
  res.status(400).json({ 
    error: error.message || '文件上传失败' 
  });
});

export default router;
